{"compilerOptions": {"target": "ES2022", "module": "ES2022", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "outDir": "./dist", "rootDir": ".", "declaration": true, "declarationMap": true, "sourceMap": true, "allowJs": true, "checkJs": false, "types": ["node"], "baseUrl": ".", "paths": {"@trustsync/types": ["../packages/trustsync-types/dist"], "@trustsync/types/*": ["../packages/trustsync-types/dist/*"]}}, "include": ["*.ts", "app/**/*.ts", "app/**/*.js"], "exclude": ["node_modules", "frontend", "dist", "build", "eslint.config.js", "app/commands/**/*"]}