import dayjs from "dayjs";
import { autoPublishHelpTextOptions } from "./config";

// Type definitions for helper functions
interface OnboardingSteps {
  EMAIL_SENDING?: boolean;
  REVIEW_LINK?: boolean;
  EMAIL_TRIGGER?: boolean;
  SUBSCRIBED?: boolean;
}

interface NotificationOptions {
  message?: string;
  timeout?: number;
}

// Declare global shopify object for toast notifications
declare global {
  interface Window {
    shopify: {
      toast: {
        hide(): void;
        show(message: string, options: { duration: number }): void;
      };
    };
  }
}

export const helpTextForAutoPublish = (autoPublish: number | string): string => {
  const key = typeof autoPublish === "string" ? parseInt(autoPublish) : autoPublish;
  return autoPublishHelpTextOptions[key] ?? "";
};

export const calculatePercentage = (sendEmails: number, limitEmails: number): number => {
  const percentage = Math.ceil((sendEmails / limitEmails) * 100);
  return parseInt(Math.min(percentage, 100).toString()) || 0; // Set percentage to 100 if it's greater than 100
};

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const roundToTwoDigits = (num: number): number => {
  return Math.round((num + Number.EPSILON) * 100) / 100;
};

export const formatDateTime = (date: string | Date): string => {
  return dayjs(date).format("DD MMM YY, hh:mm A");
};

export const capitalizeFirstLetter = (str: string): string => {
  if (str.length === 0) {
    return str;
  }
  return str.charAt(0).toUpperCase() + str.slice(1);
};

export const getDateTimeStamp = (date: string | Date): number => {
  return new Date(date).getTime();
};

export const validateDomainName = (domain: string): boolean => {
  const domainRegex = /^(?!:\/\/)([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$/;
  return domainRegex.test(domain);
};

export const checkOnboardingCompletion = (steps: OnboardingSteps | undefined): boolean => {
  const isEmailSendingEnabled = steps?.EMAIL_SENDING || false;
  const hasReviewLinks = steps?.REVIEW_LINK || false;
  const isEmailTriggerEnabled = steps?.EMAIL_TRIGGER || false;
  // const isSubscribedEnabled = steps?.SUBSCRIBED || false;

  return isEmailSendingEnabled && hasReviewLinks && isEmailTriggerEnabled;
};

export const checkAnyOnboardingCompletion = (steps: OnboardingSteps | undefined): boolean => {
  const isEmailSendingEnabled = steps?.EMAIL_SENDING || false;
  const hasReviewLinks = steps?.REVIEW_LINK || false;
  const isEmailTriggerEnabled = steps?.EMAIL_TRIGGER || false;
  // const isSubscribedEnabled = steps?.SUBSCRIBED || false;

  return isEmailSendingEnabled || hasReviewLinks || isEmailTriggerEnabled;
};

export const sleep = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const validateImage = (file: File): string => {
  const validTypes = ["image/jpg", "image/jpeg", "image/png", "image/gif"];
  const maxSize = 2 * 1024 * 1024; // 2 MB

  if (!validTypes.includes(file.type)) {
    return "Invalid file type. Only JPEG, PNG, and GIF are allowed.";
  }

  if (file.size > maxSize) {
    return "File size exceeds the limit of 2MB.";
  }

  return "";
};

export const truncateString = (str: string | null | undefined): string => {
  if (!str) {
    return "";
  }

  if (str.length > 20) {
    return str.slice(0, 20) + "...";
  }

  return str;
};

export const isEmpty = (value: any[] | string | null | undefined): boolean => {
  if (Array.isArray(value)) {
    return value.length === 0;
  } else {
    return value === "" || value == null;
  }
};

export const formatDate = (date: Date): string => {
  return date.toISOString().split("T")[0];
};

export const showNotification = ({ message = "", timeout = 4500 }: NotificationOptions): void => {
  setTimeout(() => {
    window.shopify.toast.hide();
    window.shopify.toast.show(message, {
      duration: timeout,
    });
  }, 1000);
};

export const convertDateTime = (date: string | Date): string => {
  const rawDate = dayjs(date);
  const formattedDate = rawDate.format("MMMM D, YYYY [at] h.mm a");
  return formattedDate;
};

export const stripTags = (html: string): string => html.replace(/<[^>]*>/g, "");
