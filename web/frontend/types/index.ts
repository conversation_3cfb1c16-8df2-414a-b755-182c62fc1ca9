/**
 * Frontend Type Definitions
 *
 * This file contains TypeScript interface definitions for the frontend React components.
 * These types are separate from the backend types but should be kept in sync with
 * the shared types package and backend MongoDB schemas.
 */

import {
  EmailDocument,
  EmailSettingsDocument,
  NegativeReviewDocument,
  SecondWhenToSend,
  ShopDocument,
  WhenToSend,
} from "@trustsync/types";
import React from "react";

// Frontend-specific types

export interface FeedbackReplyPayload {
  reviewId: string;
  message: string;
  attachments?: string[];
}

// Component prop types
export interface OnboardingSteps {
  EMAIL_SENDING?: boolean;
  REVIEW_LINK?: boolean;
  EMAIL_TRIGGER?: boolean;
  SUBSCRIBED?: boolean;
}

export interface IOnboardingItem {
  id: string;
  title: string;
  children: React.ReactNode;
  active: boolean;
  loading: boolean;
  isCompleted: boolean;
}

export interface EmailAnalyticsQuery {
  q?: string;
  platform?: string;
  since?: string;
  until?: string;
  selected?: number;
  page?: number;
  pageSize?: number;
}

// Email analytics data interface
export interface EmailAnalyticsData {
  emails: EmailDocument[];
  states: {
    totalPending: number;
    totalSent: number;
    totalOpen: number;
    totalClick: number;
  };
  pagination: {
    page: number;
    pageSize: number;
    totalItems: number;
  };
}

export interface INegativeFeedback extends NegativeReviewDocument {
  hasAttachment: boolean;
}

export interface INegativeFeedbacksList {
  negativeReviews: INegativeFeedback[];
  pagination: {
    page: number;
    pageSize: number;
    totalItems: number;
  };
}

export interface IEmailSettings {
  settings: EmailSettingsDocument;
  shop: ShopDocument;
}

export interface GeneralSettingsFormData {
  active?: boolean;
  sentPastOrders?: boolean;
  whenToSend: WhenToSend;
  sendOnRepeat?: string;
  secondEmail?: boolean;
  secondWhenToSend: SecondWhenToSend;
  onlyOnlineStoreOrders?: boolean;
  specificTimeActive?: boolean;
  specificTime?: any;
  selectedBlacklistEmails?: string[];
  selectedReplyToEmails: string[];
  sender?: string;
  domain?: string;
}
