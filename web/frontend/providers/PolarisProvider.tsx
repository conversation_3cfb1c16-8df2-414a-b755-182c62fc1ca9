import { App<PERSON>rovider } from "@shopify/polaris";
import React, { ReactNode, useCallback } from "react";
// import "@shopify/polaris/build/esm/styles.css";
import translations from "@shopify/polaris/locales/en.json";

// AppBridge Link component props interface
interface AppBridgeLinkProps {
  url: string;
  children?: ReactNode;
  external?: boolean;
  [key: string]: any; // For additional props
}

function AppBridgeLink({ url, children, external, ...rest }: AppBridgeLinkProps): React.ReactElement {
  const handleClick = useCallback(() => window.open(url), [url]);

  const IS_EXTERNAL_LINK_REGEX = /^(?:[a-z][a-z\d+.-]*:|\/\/)/;

  if (external || IS_EXTERNAL_LINK_REGEX.test(url)) {
    return (
      <a
        {...rest}
        href={url}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    );
  }

  return (
    <a
      {...rest}
      target="_self"
      onClick={handleClick}
    >
      {children}
    </a>
  );
}

// PolarisProvider props interface
interface PolarisProviderProps {
  children: ReactNode;
}

/**
 * Sets up the AppProvider from Polaris.
 * @desc PolarisProvider passes a custom link component to Polaris.
 * The Link component handles navigation within an embedded app.
 * Prefer using this vs any other method such as an anchor.
 * Use it by importing Link from Polaris, e.g:
 *
 * ```
 * import {Link} from '@shopify/polaris'
 *
 * function MyComponent() {
 *  return (
 *    <div><Link url="/tab2">Tab 2</Link></div>
 *  )
 * }
 * ```
 *
 * PolarisProvider also passes translations to Polaris.
 *
 */
export function PolarisProvider({ children }: PolarisProviderProps): React.ReactElement {
  return (
    <AppProvider
      i18n={translations}
      linkComponent={AppBridgeLink}
    >
      {children}
    </AppProvider>
  );
}
