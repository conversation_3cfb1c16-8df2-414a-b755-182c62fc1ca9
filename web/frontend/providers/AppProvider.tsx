import { ShopDocument, SubscriptionDocument } from "@trustsync/types";
import React, { createContext, ReactNode, useContext, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useAppQuery } from "storeware-tanstack-query";
import queryKeys from "../../app/enums/queryKeys";
import { fetchShopInfo } from "../apis/shop.api";

import AnalyticsLoader from "../components/loader/AnalyticsLoader";
import DashboardLoader from "../components/loader/DashboardLoader";
import NegativeFeedbackLoader from "../components/loader/NegativeFeedbackLoader";
import PageLoader from "../components/loader/PageLoader";
import SubscriptionLoader from "../components/loader/SubscriptionLoader";

// Configuration settings interface
interface IConfigurationSettings {
  [key: string]: boolean;
}

// App context value interface
interface AppContextValue {
  shop?: ShopDocument;
  subscription?: SubscriptionDocument;
  configurationSettings?: IConfigurationSettings;
  isShopUnderProPlan: boolean;
  isLoading: boolean;
}

// App provider props interface
interface AppProviderProps {
  children: ReactNode;
}

// Shop info API response interface
interface ShopInfoResponse {
  shop?: ShopDocument;
  subscription: SubscriptionDocument;
  configurationSettings?: IConfigurationSettings;
}

const AppContext = createContext<AppContextValue | undefined>(undefined);

export const useAppContext = (): AppContextValue => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error("useAppContext must be used within an AppProvider");
  }
  return context;
};

export const AppProvider = ({ children }: AppProviderProps): React.ReactElement => {
  const { data: { shop, subscription, configurationSettings } = {} as ShopInfoResponse, isLoading } = useAppQuery({
    queryKey: [queryKeys.FETCH_SHOP_INFO],
    queryFn: fetchShopInfo,
    reactQueryOptions: {
      enabled: true,
      staleTime: 0,
    },
  });

  console.log("subscription", subscription);

  const location = useLocation();

  const pageLoaders: Record<string, React.ReactElement> = {
    "/": <DashboardLoader />,
    "/analytics": <AnalyticsLoader />,
    "/negative-feedbacks": <NegativeFeedbackLoader />,
    "/subscription": <SubscriptionLoader />,
  };

  const loader = pageLoaders[location.pathname] || <PageLoader />;

  useEffect(() => {
    const staticContent = document.querySelector("#static") as HTMLElement | null;
    const appContent = document.querySelector("#app") as HTMLElement | null;

    if (!isLoading && shop && staticContent && appContent) {
      setTimeout(() => {
        appContent.style.opacity = "1";
        appContent.style.zIndex = "0";
      }, 100);
      setTimeout(() => {
        staticContent.style.display = "none";
        staticContent.style.zIndex = "-100";
      }, 1500);
    }
  }, [isLoading, shop]);

  const contextValue: AppContextValue = {
    shop,
    subscription,
    configurationSettings,
    isShopUnderProPlan: subscription?.plan !== "free",
    isLoading,
  };

  return <AppContext.Provider value={contextValue}>{isLoading ? loader : children}</AppContext.Provider>;
};
