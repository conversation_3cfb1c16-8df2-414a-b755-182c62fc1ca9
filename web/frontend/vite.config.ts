import react from "@vitejs/plugin-react";
import { dirname, resolve } from "path";
import { fileURLToPath } from "url";
import { defineConfig, loadEnv } from "vite";

if (process.env.npm_lifecycle_event === "build" && !process.env.CI && !process.env.SHOPIFY_API_KEY) {
  console.warn(
    "\n\nThe frontend build will not work without an API key. Set the SHOPIFY_API_KEY environment variable when running the build command, for example:" +
      "\n\nSHOPIFY_API_KEY=<your-api-key> npm run build\n"
  );
}

// process.env.VITE_SHOPIFY_API_KEY = process.env.SHOPIFY_API_KEY;

const proxyOptions = {
  target: `http://127.0.0.1:${process.env.BACKEND_PORT}`,
  changeOrigin: false,
  secure: true,
  ws: false,
};

const host = process.env.HOST ? process.env.HOST.replace(/https?:\/\//, "") : "localhost";

let hmrConfig;
if (host === "localhost") {
  hmrConfig = {
    protocol: "ws",
    host: "localhost",
    port: 64999,
    clientPort: 64999,
  };
} else {
  hmrConfig = {
    protocol: "wss",
    host: host,
    port: parseInt(process.env.FRONTEND_PORT || "3000"),
    clientPort: 443,
  };
}

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd() + "/../");

  return {
    root: dirname(fileURLToPath(import.meta.url)),
    plugins: [react()],
    define: {
      "process.env": { ...env },
    },
    resolve: {
      preserveSymlinks: true,
      alias: {
        "@trustsync/types": resolve(dirname(fileURLToPath(import.meta.url)), "../../packages/trustsync-types/dist"),
      },
    },
    server: {
      host: "localhost",
      port: parseInt(process.env.FRONTEND_PORT || "3000"),
      hmr: hmrConfig,
      proxy: {
        "^/webhooks(/[\\w\\W]*)?$": proxyOptions,
        "^/(\\?.*)?$": proxyOptions,
        "^/[a-z-]+\\?.+$": proxyOptions,
        "^/api(/|(\\?.*)?$)": proxyOptions,
      },
    },
  };
});
