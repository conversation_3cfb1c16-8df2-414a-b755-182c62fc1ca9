import React from "react";
import { Routes as ReactRouter<PERSON>out<PERSON>, Route } from "react-router-dom";

// Type definitions for routing
type PageModule = {
  default: React.ComponentType;
};

type Pages = Record<string, PageModule>;

type RouteInfo = {
  path: string;
  component: React.ComponentType;
};

interface RoutesProps {
  pages: Pages;
}

/**
 * File-based routing configuration for React Router v6+ data router.
 * @desc File-based routing that creates route objects for createBrowserRouter.
 * To create a new route create a new .jsx file in `/pages` with a default export.
 *
 * Some examples:
 * * `/pages/index.jsx` matches `/`
 * * `/pages/blog/[id].jsx` matches `/blog/123`
 * * `/pages/[...catchAll].jsx` matches any URL not explicitly matched
 *
 * @param pages value of import.meta.glob(). See https://vitejs.dev/guide/features.html#glob-import
 *
 * @return `<Routes/>` from React Router, with a `<Route/>` for each file in `pages`
 */
export default function Routes({ pages }: RoutesProps): React.ReactElement {
  const routes = useRoutes(pages);

  const notFoundRoute = routes.find(({ path }) => path === "/page-not-found");
  const NotFound = notFoundRoute?.component;

  if (!NotFound) {
    throw new Error("Page not found component is required");
  }

  const routeComponents = routes.map(({ path, component: Component }) => (
    <Route
      key={path}
      path={path}
      element={<Component />}
    />
  ));

  return (
    <ReactRouterRoutes>
      {routeComponents}
      <Route
        path="*"
        element={<NotFound />}
      />
    </ReactRouterRoutes>
  );
}

function useRoutes(pages: Pages): RouteInfo[] {
  const routes = Object.keys(pages)
    .map((key): RouteInfo | null => {
      let path = key
        .replace("./pages", "")
        .replace(/\.(t|j)sx?$/, "")
        /**
         * Replace /index with /
         */
        .replace(/\/index$/i, "/")
        /**
         * Only lowercase the first letter. This allows the developer to use camelCase
         * dynamic paths while ensuring their standard routes are normalized to lowercase.
         */
        .replace(/\b[A-Z]/, (firstLetter) => firstLetter.toLowerCase())
        /**
         * Convert /[handle].jsx and /[...handle].jsx to /:handle.jsx for react-router-dom
         */
        .replace(/\[(?:[.]{3})?(\w+?)\]/g, (_match, param) => `:${param}`);

      if (path.endsWith("/") && path !== "/") {
        path = path.substring(0, path.length - 1);
      }

      if (!pages[key].default) {
        console.warn(`${key} doesn't export a default React component`);
        return null;
      }

      return {
        path,
        component: pages[key].default,
      };
    })
    .filter((route): route is RouteInfo => route !== null);

  return routes;
}

/**
 * Creates route configuration objects for createBrowserRouter
 * @param pages - The pages object from import.meta.glob
 * @returns Array of route objects for React Router v6+ data router
 */
export function createRouteConfig(pages: Pages) {
  const routes = useRoutes(pages);
  return routes.map(({ path, component: Component }) => ({
    path,
    element: <Component />,
  }));
}
