.full-width-plan-list {
  column-count: 3;
  gap: 10px;
}

@media all and (max-width: 768px) {
  .full-width-plan-list {
    column-count: 2;
  }
}

@media all and (max-width: 575px) {
  .full-width-plan-list {
    column-count: 1;
  }
}

.ql-editor {
  min-height: 150px;
}

#tag-copy {
  cursor: copy;
}

/* Editor CSS */
.editor-email-section img {
  max-width: 100%;
  display: inline-block;
}

.editor-preview-logo {
  text-align: center;
}

.editor-preview-logo-image {
  max-width: 70% !important;
  max-height: 200px !important;
}

.editor-preview-rating-image {
  max-width: 100% !important;
}

.powered-by-section {
  display: inline-block;
  padding-top: 5px;
}

.editor-preview-powered-logo {
  font-size: 14px;
  color: #303030;
  font-weight: 500;
  margin-bottom: 5px;
  display: flex;
}

.editor-preview-powered-logo img {
  width: 20px;
  height: 20px;
  padding: 2px;
}

.editor-preview-powered-logo a {
  color: #303030;
}

.editor-preview-powered-support {
  max-width: 100%;
}

.editor-preview-powered-support img {
  max-width: 100%;
  cursor: pointer;
}

.Polaris-EmptyState__Image {
  opacity: 1 !important;
}

.footer {
  padding: var(--p-space-400);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--p-space-600);
}

.footer .footer__logo {
  line-height: 1;
}

.footer .footer__logo img {
  height: 28px;
}

.footer .copyright__text .link {
  text-decoration: none;
  color: var(--p-color-text-secondary);
  font-weight: 700;
}

@media all and (max-width: 991px) {
  .footer .footer__logo {
    order: 1;
    line-height: 1;
  }

  .footer .footer-language {
    order: 2;
  }

  .footer .Polaris-Text--root {
    flex: 1 1 100%;
    order: 3;
    text-align: center;
  }
}

.dashboardGetStartedSection .Polaris-VideoThumbnail__Thumbnail {
  background-size: 100% 100%;
}

/* Skeleton Style */
.skeleton-wrapper {
  line-height: 0;
}

.skeleton-box {
  display: inline-block;
  position: relative;
  overflow: hidden;
  background: #dde0e4;
  border-radius: 5px;
}

.skeleton-box:after {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transform: translateX(-100%);
  background: #dde0e4;
  content: "";
  background-image: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0,
    rgba(255, 255, 255, 0.2) 20%,
    rgba(255, 255, 255, 0.5) 60%,
    rgba(255, 255, 255, 0)
  );
  animation: skeleton-loading 2s infinite;
}

@keyframes skeleton-loading {
  100% {
    transform: translateX(100%);
  }
}

/* For negative review reply editor */
.Polaris-IndexTable__TableRow {
  cursor: default !important;
}

.Polaris-IndexTable-Checkbox__TableCellContentContainer {
  cursor: pointer !important;
}

.quill-editor-disabled {
  cursor: not-allowed !important;
}

.quill-editor-disabled .ql-snow.ql-toolbar button {
  cursor: not-allowed !important;
  opacity: 0.6;
}

.quill-editor-disabled .ql-snow .ql-picker.ql-header .ql-picker-label {
  cursor: not-allowed !important;
  color: #d5d5d5;
  border: 0px !important;
}

.quill-editor-disabled .ql-snow .ql-picker.ql-header .ql-picker-label:hover {
  cursor: not-allowed !important;
  color: #d5d5d5;
}

.quill-editor-disabled .ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  display: none !important;
}

.negative-feedback-modal section.Polaris-Box {
  padding: 0;
}

.negative-feedback-modal .modal-scroll {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
}

.negative-feedback-modal > .Polaris-Modal-Section > .Polaris-Box > .Polaris-BlockStack {
  max-height: calc(100vh - 174px);
}

.negative-feedback-modal .sticky-editor {
  display: grid;
  gap: 10px;
  position: sticky;
  background: white;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 16px;
  border-top: 1px solid #ebebeb;
}

.quill-wrapper {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #8a8a8a;
  margin-top: 5px;
}

.quill-wrapper .ql-container.ql-snow {
  border: none !important;
}

.quill-wrapper .ql-toolbar.ql-snow {
  border-top: 0px solid #8a8a8a !important;
  border-left: 0px solid #8a8a8a !important;
  border-right: 0px solid #8a8a8a !important;
  border-bottom: 1px solid #8a8a8a !important;
}
