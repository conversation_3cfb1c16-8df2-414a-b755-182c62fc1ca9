import { Modal, TitleBar, useAppBridge } from "@shopify/app-bridge-react";
import { Box, Text } from "@shopify/polaris";
import { debounce, isEqual } from "lodash";
import React, { useCallback, useEffect, useState } from "react";

// Hook parameters interface
interface UseUnsavedChangesParams<T = any> {
  originalData: T;
  currentData: T;
  onDiscardAction?: () => void;
}

// Hook return type interface
interface UseUnsavedChangesReturn {
  hasUnsavedChanges: boolean;
  DiscardChangesModal: React.ComponentType;
  showDiscardModal: () => void;
}

export default function useUnsavedChanges<T = any>({
  originalData,
  currentData,
  onDiscardAction = () => {},
}: UseUnsavedChangesParams<T>): UseUnsavedChangesReturn {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const shopify = useAppBridge();

  const modalId = "discard-confirmation-modal";

  const compareData = useCallback(
    debounce((originalData: T, currentData: T) => {
      setHasUnsavedChanges(!isEqual(originalData, currentData));
    }, 450),
    []
  );

  //   const discardChangesMenuAction = useMemo(
  //     () => ({
  //       content: "Discard",
  //       onAction: () => setShowDiscardChangeModal(true),
  //       destructive: true,
  //       disabled: !hasUnsavedChanges,
  //     }),
  //     [hasUnsavedChanges]
  //   );

  // const DiscardChangesButton = useCallback(
  //   () => (
  //     <Button
  //       tone=""
  //       onClick={() => shopify.modal.show(modalId)}
  //       disabled={!hasUnsavedChanges}
  //     >
  //       {"Discard"}
  //     </Button>
  //   ),
  //   [hasUnsavedChanges, shopify, modalId]
  // );

  const DiscardChangesModal = useCallback(
    (): React.ReactElement => (
      <Modal
        id={modalId}
        variant="base"
      >
        <Box padding="400">
          <Text as="p">{"If you discard changes, you’ll delete any edits you made since you last saved."}</Text>
        </Box>
        <TitleBar title="Discard all unsaved changes">
          <button
            variant="primary"
            tone="critical"
            onClick={() => {
              shopify.modal.hide(modalId);
              onDiscardAction();
            }}
          >
            Discard changes
          </button>
          <button onClick={() => shopify.modal.hide(modalId)}>Continue editing</button>
        </TitleBar>
      </Modal>
    ),
    [modalId, shopify, onDiscardAction]
  );

  const showDiscardModal = useCallback(() => {
    shopify.modal.show(modalId);
  }, [shopify, modalId]);

  useEffect(() => {
    compareData(originalData, currentData);
  }, [currentData, originalData, compareData]);

  return {
    hasUnsavedChanges,
    DiscardChangesModal,
    // DiscardChangesButton,
    showDiscardModal,
  };
}
