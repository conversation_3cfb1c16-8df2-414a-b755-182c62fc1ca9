import { ApiResponse } from "@trustsync/types";

// Configure settings payload interface
export interface ConfigureSettingsPayload {
  key: string;
  value: string | Date | null;
}

export const saveConfigureSettings = async (payload: ConfigureSettingsPayload): Promise<ApiResponse<Object>> => {
  const response = await fetch("/api/v1/configure-settings", {
    method: "POST",
    body: JSON.stringify(payload),
    headers: { "Content-Type": "application/json" },
  });

  return await response.json();
};
