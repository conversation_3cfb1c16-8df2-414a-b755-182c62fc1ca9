import { ApiResponse, ReviewLink } from "@trustsync/types";
const headers = {
  "Content-Type": "application/json",
};

// Onboard email enable setting payload interface
interface OnboardEmailEnablePayload {
  active: boolean;
}

// Onboard review link setting payload interface
interface OnboardReviewLinkPayload {
  reviewLinks: Array<ReviewLink>;
  language: string;
  autoPublish: number;
}

// Onboard email trigger setting payload interface
interface OnboardEmailTriggerPayload {
  sendOnRepeat: string;
  whenToSend: {
    days: string;
    after: string;
    tag: string;
  };
}

export const saveOnboardEnableEmailSetting = async (
  payload: OnboardEmailEnablePayload
): Promise<ApiResponse<Object>> => {
  const response = await fetch("/api/v1/onboard/email-enable", {
    method: "POST",
    headers,
    body: JSON.stringify(payload),
  });

  return await response.json();
};

export const saveOnboardReviewLinkSetting = async (payload: OnboardReviewLinkPayload): Promise<ApiResponse<Object>> => {
  const response = await fetch("/api/v1/onboard/review-link", {
    method: "POST",
    headers,
    body: JSON.stringify(payload),
  });

  return await response.json();
};

export const saveOnboardEmailTriggerSetting = async (
  payload: OnboardEmailTriggerPayload
): Promise<ApiResponse<Object>> => {
  const response = await fetch("/api/v1/onboard/email-trigger", {
    method: "POST",
    headers,
    body: JSON.stringify(payload),
  });

  return await response.json();
};
