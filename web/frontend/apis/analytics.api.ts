import { ApiResponse } from "@trustsync/types";
import { EmailAnalyticsData, EmailAnalyticsQuery, INegativeFeedbacksList } from "types";

interface NegativeFeedbackReplyPayload {
  feedbackId: string;
  message: string;
  senderEmail?: string;
}

export const fetchEmailAnalyticsInfo = async (payload: EmailAnalyticsQuery): Promise<EmailAnalyticsData> => {
  const params = new URLSearchParams(payload as Record<string, string>);

  const response = await fetch(`/api/v1/analytics-info?${params}`);

  const { data }: ApiResponse<EmailAnalyticsData> = await response.json();

  return data;
};

export const fetchNegativeFeedbacks = async (
  page: number,
  pageSize: number,
  query: string
): Promise<INegativeFeedbacksList> => {
  const response = await fetch(`/api/v1/negative-feedbacks/list?page=${page}&pageSize=${pageSize}&q=${query}`);

  const { data }: ApiResponse<INegativeFeedbacksList> = await response.json();

  return data;
};

export const negativeFeedbackReply = async (payload: NegativeFeedbackReplyPayload): Promise<ApiResponse<any>> => {
  const response = await fetch(`/api/v1/negative-feedbacks/reply`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload),
  });

  return await response.json();
};
