import { ApiResponse } from "@trustsync/types";

const headers = {
  "Content-Type": "application/json",
};

// Remove email payload interface
interface RemoveEmailPayload {
  ids: string[];
}

// Resend email payload interface
interface ResendEmailPayload {
  ids: string[];
}

// Test email payload interface
interface TestEmailPayload {
  email: string;
}

export const handleRemoveEmail = async (payload: RemoveEmailPayload): Promise<ApiResponse<Object>> => {
  const response = await fetch("/api/v1/email/remove-mail", {
    method: "POST",
    headers,
    body: JSON.stringify(payload),
  });

  return await response.json();
};

export const handleResendMail = async (payload: ResendEmailPayload): Promise<ApiResponse<Object>> => {
  const response = await fetch("/api/v1/email/resend-mail", {
    method: "POST",
    headers,
    body: JSON.stringify(payload),
  });

  return await response.json();
};

export const sentTestEmail = async (payload: TestEmailPayload): Promise<ApiResponse<Object>> => {
  const response = await fetch("/api/v1/email/send-test-mail", {
    method: "POST",
    headers,
    body: JSON.stringify(payload),
  });

  return await response.json();
};
