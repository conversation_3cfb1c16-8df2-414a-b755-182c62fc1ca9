import { ApiResponse, SubscriptionDocument } from "@trustsync/types";

const headers = {
  "Content-Type": "application/json",
};

// Pro plan subscription payload interface
interface ProPlanSubscriptionPayload {
  slug: string;
}

// Pro plan subscription request body interface
interface ProPlanRequestBody {
  plan: string;
}

export const fetchSubscription = async (): Promise<any> => {
  const response = await fetch("/api/v1/subscription");

  const { data }: ApiResponse<SubscriptionDocument> = await response.json();
  return data;
};

export const freePlanSubscription = async (): Promise<ApiResponse<any>> => {
  const response = await fetch("/api/v1/subscribe/free-plan", {
    method: "POST",
    headers,
  });

  return await response.json();
};

export const proPlanSubscription = async ({ slug }: ProPlanSubscriptionPayload): Promise<ApiResponse<any>> => {
  const requestBody: ProPlanRequestBody = { plan: slug };

  const response = await fetch("/api/v1/subscribe/pro-plan", {
    method: "POST",
    headers,
    body: JSON.stringify(requestBody),
  });

  return await response.json();
};
