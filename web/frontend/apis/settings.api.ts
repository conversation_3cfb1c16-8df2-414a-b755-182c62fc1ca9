import { ApiResponse, EmailSettingsDocument, ReviewLink } from "@trustsync/types";
import { IEmailSettings } from "types";

const headers = {
  "Content-Type": "application/json",
};

// Review settings payload interface
interface ReviewSettingsPayload {
  autoPublish?: number;
  reviewLinks?: ReviewLink[];
  customersPick?: boolean;
  customNegativeForm?: boolean;
  customNegativeLink?: string;
  language?: string;
}

interface EmailDesignSettingsPayload {
  from?: string;
  subject?: string;
  emailHTML?: string;
  designTemplate?: string;
  lowestText?: string;
  highestText?: string;
  logo?: string;
  logoHeight?: number;
  bgColor?: string;
  borderColor?: string;
  image?: any;
}

// General email settings payload interface
interface GeneralEmailSettingsPayload {
  domain?: string;
  senderEmail?: string;
  replyTo?: string;
  [key: string]: any;
}

export const fetchSettings = async (): Promise<IEmailSettings> => {
  const response = await fetch("/api/v1/settings");
  const { data }: ApiResponse<IEmailSettings> = await response.json();
  return data;
};

export const saveReviewSettings = async (payload: ReviewSettingsPayload): Promise<ApiResponse<any>> => {
  const response = await fetch("/api/v1/settings/review-settings", {
    method: "POST",
    headers,
    body: JSON.stringify(payload),
  });

  return await response.json();
};

export const saveEmailDesignSettings = async (payload: EmailDesignSettingsPayload): Promise<ApiResponse<any>> => {
  const response = await fetch("/api/v1/settings/email-designs", {
    method: "POST",
    headers,
    body: JSON.stringify(payload),
  });

  return await response.json();
};

export const saveGeneralEmailSettings = async (payload: GeneralEmailSettingsPayload): Promise<ApiResponse<Object>> => {
  const response = await fetch("/api/v1/settings/general-settings", {
    method: "POST",
    headers,
    body: JSON.stringify(payload),
  });

  return await response.json();
};
