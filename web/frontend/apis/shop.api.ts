import { ApiResponse, ShopDocument, SubscriptionDocument } from "@trustsync/types";

// Configuration settings interface
interface ConfigurationSettings {
  [key: string]: boolean;
}

// Shop info response interface
interface ShopInfoResponse {
  shop?: ShopDocument;
  subscription?: SubscriptionDocument;
  configurationSettings?: ConfigurationSettings;
}

export const fetchShop = async (): Promise<ShopDocument> => {
  const response = await fetch(`/api/v1/shop`);
  const { data }: ApiResponse<ShopDocument> = await response.json();
  return data;
};

export const fetchShopInfo = async (): Promise<ShopInfoResponse> => {
  const response = await fetch(`/api/v1/shop-info`);
  const { data }: ApiResponse<ShopInfoResponse> = await response.json();
  return data;
};
