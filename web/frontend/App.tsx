/// <reference path="./types/global.d.ts" />
import React from "react";
import { useTranslation } from "react-i18next";
import { createBrowserRouter, Link, Outlet, RouterProvider } from "react-router-dom";

import { createRouteConfig } from "./Routes";

import { PolarisProvider } from "./providers/PolarisProvider";

import { QueryProvider } from "storeware-tanstack-query";
import Footer from "./components/common/Footer";
import { AppProvider } from "./providers/AppProvider";

// Type for the pages glob import
type PageModule = {
  default: React.ComponentType;
};

type Pages = Record<string, PageModule>;

// Root layout component that includes navigation and common layout
function RootLayout(): React.ReactElement {
  const { t } = useTranslation();

  return (
    <PolarisProvider>
      <QueryProvider>
        <AppProvider>
          {React.createElement(
            "ui-nav-menu",
            {},
            <Link
              to="/"
              rel="home"
            />,
            <Link to="/settings/review">{t("NavigationMenu.reviewSettings")}</Link>,
            <Link to="/settings">{t("NavigationMenu.emailConfigurations")}</Link>,
            <Link to="/analytics">{t("NavigationMenu.analytics")}</Link>,
            <Link to="/negative-feedbacks">{t("NavigationMenu.negativeFeedbacks")}</Link>,
            <Link to="/subscription">{t("NavigationMenu.subscription")}</Link>
          )}

          <div style={{ display: "flex", flexDirection: "column", minHeight: "100vh" }}>
            <div style={{ flex: 1 }}>
              <Outlet />
            </div>
            <Footer />
          </div>
        </AppProvider>
      </QueryProvider>
    </PolarisProvider>
  );
}

// Create router configuration
const pages = import.meta.glob("./pages/**/!(*.test.[jt]sx)*.([jt]sx)", {
  eager: true,
}) as Pages;

const routes = createRouteConfig(pages);

const router = createBrowserRouter([
  {
    path: "/",
    element: <RootLayout />,
    children: routes,
  },
]);

export default function App(): React.ReactElement {
  return <RouterProvider router={router} />;
}
