{"name": "shopify-frontend-template-react", "version": "1.0.0", "private": true, "license": "UNLICENSED", "scripts": {"build": "vite build", "dev": "vite", "type-check": "tsc --noEmit", "coverage": "vitest run --coverage"}, "type": "module", "engines": {"node": ">= 12.16"}, "stylelint": {"extends": "@shopify/stylelint-polaris"}, "dependencies": {"@formatjs/intl-locale": "^3.3.2", "@formatjs/intl-localematcher": "^0.4.0", "@formatjs/intl-pluralrules": "^5.2.4", "@shopify/app-bridge": "^3.7.7", "@shopify/app-bridge-react": "^4.1.3", "@shopify/i18next-shopify": "^0.2.9", "@shopify/polaris": "^12.27.0", "@vitejs/plugin-react": "4.2.1", "crisp-sdk-web": "^1.0.25", "dayjs": "^1.11.13", "i18next": "^23.1.0", "i18next-resources-to-backend": "^1.1.4", "quill-resize-image": "^1.0.5", "react": "^18.2.0", "react-color": "^2.19.3", "react-dom": "^18.2.0", "react-i18next": "^13.0.0", "react-modal-video": "^2.0.2", "react-query": "^3.34.19", "react-quill-new": "^3.3.3", "react-router-dom": "^6.30.0", "storeware-tanstack-query": "github:Storeware-Apps/storeware-tanstack-query", "vite": "^5.4.0", "web-vitals": "^4.2.4"}, "devDependencies": {"@shopify/stylelint-polaris": "^12.0.0", "@types/lodash": "^4.17.17", "@types/react": "^19.1.8", "@types/react-color": "^3.0.13", "@types/react-dom": "^19.1.6", "@types/react-modal-video": "^1.2.3", "history": "^5.3.0", "jsdom": "^24.0.0", "prettier": "^3.2.5", "stylelint": "^15.0.0", "typescript": "^5.3.0"}}