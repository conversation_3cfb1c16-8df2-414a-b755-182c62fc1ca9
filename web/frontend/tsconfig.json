{"compilerOptions": {"target": "ES2020", "lib": ["ES2021", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "allowImportingTsExtensions": true, "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "declaration": true, "declarationMap": true, "sourceMap": true, "allowJs": true, "checkJs": false, "jsx": "react-jsx", "types": ["vite/client"], "baseUrl": ".", "paths": {"@trustsync/types": ["../../packages/trustsync-types/dist"], "@trustsync/types/*": ["../../packages/trustsync-types/dist/*"]}}, "include": ["*.jsx", "*.js", "**/*.jsx", "**/*.js", "**/*.ts", "**/*.tsx", "types/global.d.ts"], "exclude": ["node_modules", "dist", "build"]}