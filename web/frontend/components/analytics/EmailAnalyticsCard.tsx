import { Block<PERSON><PERSON><PERSON>, Card, InlineStack, Text } from "@shopify/polaris";
import React from "react";

interface EmailAnalyticsCardProps {
  label: string;
  value: number;
}

export default function EmailAnalyticsCard({ label, value }: EmailAnalyticsCardProps) {
  return (
    <Card>
      <InlineStack
        gap="200"
        blockAlign="center"
        align="space-between"
      >
        <BlockStack gap="200">
          <Text
            as="h3"
            variant="headingLg"
          >
            {value}
          </Text>
          <Text
            as="p"
            variant="bodySm"
            fontWeight="medium"
          >
            {label}
          </Text>
        </BlockStack>
        <svg
          width="50"
          height="50"
          viewBox="0 0 50 50"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            cx="25"
            cy="25"
            r="21.25"
            stroke="#F2F7FE"
            strokeWidth="7.5"
          />
        </svg>
      </InlineStack>
    </Card>
  );
}
