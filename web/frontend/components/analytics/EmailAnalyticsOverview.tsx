import { <PERSON><PERSON>ist, BlockStack, Button, InlineGrid, InlineStack, Popover, Text } from "@shopify/polaris";

import React, { useCallback, useState } from "react";
import { reviewLinkOptions } from "../../utils/config";
import { DateRangePicker } from "../common/DateRangePicker";
import EmailAnalyticsCard from "./EmailAnalyticsCard";

const reviewPlatformLinks = [{ label: "All review sources", value: "", prefix: false }, ...reviewLinkOptions];

interface AnalyticsStates {
  totalSent?: number;
  totalPending?: number;
  totalOpen?: number;
  totalClick?: number;
}

interface EmailAnalyticsOverviewProps {
  states: AnalyticsStates;
  handlePayloadUpdate: (payload: any) => void;
}

export default function EmailAnalyticsOverview({ states, handlePayloadUpdate }: EmailAnalyticsOverviewProps) {
  const [selectedReviewPlatform, setSelectedReviewPlatform] = useState<{ label?: string; value?: string }>({});
  const [isOpenReviewPlatform, setOpenReviewPlatform] = useState(false);

  const toogleReviewPlatformPopOver = useCallback(() => {
    setOpenReviewPlatform((isOpenReviewPlatform) => !isOpenReviewPlatform);
  }, []);

  const reviewPlatformActivator = (
    <Button
      onClick={toogleReviewPlatformPopOver}
      disclosure
    >
      {selectedReviewPlatform?.label ? selectedReviewPlatform?.label : "All review sources"}
    </Button>
  );

  const handleReviewPlatformFilter = (option: { label: string; value: string }) => {
    setOpenReviewPlatform(false);
    setSelectedReviewPlatform(option);
    handlePayloadUpdate({ platform: option.value });
  };

  return (
    <BlockStack gap="200">
      <InlineStack
        align="space-between"
        blockAlign="center"
        gap="300"
      >
        <Text
          as="h3"
          variant="headingLg"
        >
          Email Statistics
        </Text>

        <InlineStack gap="300">
          <Popover
            active={isOpenReviewPlatform}
            activator={reviewPlatformActivator}
            autofocusTarget="first-node"
            onClose={toogleReviewPlatformPopOver}
          >
            <ActionList
              actionRole="menuitem"
              items={reviewPlatformLinks.map((reviewPlatform) => ({
                content: reviewPlatform.label,
                onAction: () => handleReviewPlatformFilter(reviewPlatform),
                active: false,
              }))}
            />
          </Popover>

          <DateRangePicker handlePayloadUpdate={handlePayloadUpdate} />
        </InlineStack>
      </InlineStack>

      <InlineGrid
        columns={4}
        gap="400"
      >
        <EmailAnalyticsCard
          label="Total Sent"
          value={states?.totalSent || 0}
        />
        <EmailAnalyticsCard
          label="Total Pending"
          value={states?.totalPending || 0}
        />
        <EmailAnalyticsCard
          label="Total Open"
          value={states?.totalOpen || 0}
        />
        <EmailAnalyticsCard
          label="Total Click"
          value={states?.totalClick || 0}
        />
      </InlineGrid>
    </BlockStack>
  );
}
