import { useCallback } from "react";
import { useNavigate } from "react-router";

import {
  BlockStack,
  Box,
  Button,
  Card,
  EmptySearchResult,
  IndexTable,
  InlineStack,
  Tabs,
  Text,
  useBreakpoints,
} from "@shopify/polaris";

import { capitalizeFirstLetter, formatDateTime } from "../../utils/helper";

const tabItems = [
  {
    id: "pending-email",
    content: "Pending emails",
    panelID: "pending-emails",
  },
  {
    id: "send-email",
    content: "Latest sent",
    panelID: "latest-sent",
  },
];

interface DashboardEmailListProps {
  emails: any[];
  selected: number;
  setSelected: (selected: number) => void;
  setPagination: (pagination: any) => void;
  handlePayloadUpdate: (payload: any) => void;
}

export default function DashboardEmailList({
  emails,
  selected,
  setSelected,
  setPagination,
  handlePayloadUpdate,
}: DashboardEmailListProps) {
  const navigate = useNavigate();

  // Table tab switch
  const handleTabSwitch = useCallback((selectedTabIndex: number) => {
    setSelected(selectedTabIndex);
    setPagination((prev: any) => ({ ...prev, page: 1 }));
    handlePayloadUpdate({ selected: selectedTabIndex });
  }, []);

  return (
    <BlockStack gap="200">
      <Card padding="0">
        <Tabs
          tabs={tabItems}
          selected={selected}
          onSelect={handleTabSwitch}
        >
          <IndexTable
            condensed={useBreakpoints().smDown}
            resourceName={{ singular: "email", plural: "emails" }}
            itemCount={emails?.length}
            headings={[
              { title: "Name" },
              { title: "Email" },
              { title: "Date & Time" },
              { title: "Review Link" },
              { title: "Order Link" },
            ]}
            selectable={false}
            emptyState={
              <EmptySearchResult
                title={"No emails found"}
                withIllustration
              />
            }
          >
            {emails?.length > 0 &&
              emails.map((email, index) => {
                return (
                  <EmailListItem
                    email={email}
                    key={index}
                  />
                );
              })}
          </IndexTable>
        </Tabs>
      </Card>

      <InlineStack align="end">
        <Button
          onClick={() => {
            navigate("/analytics");
          }}
        >
          View all
        </Button>
      </InlineStack>
    </BlockStack>
  );
}

interface EmailListItemProps {
  email: any;
}

const EmailListItem = ({ email }: EmailListItemProps) => {
  return (
    <IndexTable.Row
      id={email._id}
      key={email._id}
      position={0}
    >
      <IndexTable.Cell>{email.firstName}</IndexTable.Cell>
      <IndexTable.Cell>
        <Box paddingBlock="200">
          <Text
            variant="bodyMd"
            fontWeight="medium"
            as="span"
          >
            {email.email}
          </Text>
        </Box>
      </IndexTable.Cell>
      <IndexTable.Cell>{formatDateTime(email.sendDate)}</IndexTable.Cell>
      <IndexTable.Cell>
        <Button
          variant="plain"
          onClick={() => {
            window.open(email.reviewURL, "_blank");
          }}
        >
          {capitalizeFirstLetter(email.reviewPlatform)}
        </Button>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <Button
          variant="plain"
          onClick={() => {
            window.open(`https://${email.shop}/admin/orders/${email.orderId}`, "_blank");
          }}
        >
          Go to order
        </Button>
      </IndexTable.Cell>
    </IndexTable.Row>
  );
};
