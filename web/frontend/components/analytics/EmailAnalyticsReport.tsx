import { useEffect, useState } from "react";
import { formatDate } from "../../utils/helper";

import { EmailDocument } from "@trustsync/types";
import { useAppQuery } from "storeware-tanstack-query";
import queryKeys from "../../../app/enums/queryKeys";
import { fetchEmailAnalyticsInfo } from "../../apis/analytics.api";
import DashboardEmailList from "./DashboardEmailList";
import EmailAnalyticsOverview from "./EmailAnalyticsOverview";

export default function EmailAnalyticsReport() {
  const [emails, setEmails] = useState<EmailDocument[]>([]);
  const [states, setStates] = useState({});
  const [selected, setSelected] = useState(0);

  const [payload, setPayload] = useState({
    platform: "",
    since: "",
    until: "",
    selected: 0,
  });

  const [pagination, setPagination] = useState({ page: 1, pageSize: 5, totalItems: 0 });

  const { data, isLoading } = useAppQuery({
    queryKey: [queryKeys.FETCH_ANALYTICS, payload, pagination.page, pagination.pageSize],
    queryFn: () => fetchEmailAnalyticsInfo({ ...payload, page: pagination.page, pageSize: pagination.pageSize }),
    reactQueryOptions: {
      enabled: true,
      staleTime: 0,
    },
  });

  useEffect(() => {
    if (data) {
      setEmails(data.emails);
      setStates(data.states);
      setPagination(data.pagination);
    }
  }, [data]);

  useEffect(() => {
    const today = new Date();
    const since = new Date();
    since.setDate(today.getDate() - 30);

    setPayload((prev) => ({
      ...prev,
      since: formatDate(since),
      until: formatDate(today),
    }));
  }, []);

  const handlePayloadUpdate = (obj: { platform?: string; since?: string; until?: string; selected?: number }) => {
    setPayload((prev) => ({ ...prev, ...obj }));
  };

  return (
    <>
      <EmailAnalyticsOverview
        handlePayloadUpdate={handlePayloadUpdate}
        states={states}
      />

      <DashboardEmailList
        emails={emails}
        selected={selected}
        setSelected={setSelected}
        setPagination={setPagination}
        handlePayloadUpdate={handlePayloadUpdate}
      />
    </>
  );
}
