import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  ChoiceList,
  EmptySearchResult,
  IndexFilters,
  IndexFiltersMode,
  IndexTable,
  Text,
  useBreakpoints,
  useIndexResourceState,
  useSetIndexFiltersMode,
} from "@shopify/polaris";

import { useCallback, useState } from "react";
import { useQueryClient } from "storeware-tanstack-query";
import queryKeys from "../../../app/enums/queryKeys";
import { handleRemoveEmail, handleResendMail } from "../../apis/email.api";
import { capitalizeFirstLetter, formatDateTime, isEmpty, sleep } from "../../utils/helper";

const tabItems = [
  {
    id: "pending-email",
    content: "Pending emails",
    panelID: "pending-emails",
  },
  {
    id: "send-email",
    content: "Latest sent",
    panelID: "latest-sent",
  },
];

interface SettingEmailListProps {
  isLoading: boolean;
  emails: any[];
  selected: number;
  setSelected: (selected: number) => void;
  pagination: any;
  setPagination: (pagination: any) => void;
  payload: any;
  handlePayloadUpdate: (payload: any) => void;
}

export default function SettingEmailList({
  isLoading,
  emails,
  selected,
  setSelected,
  pagination,
  setPagination,
  payload,
  handlePayloadUpdate,
}: SettingEmailListProps) {
  const [perPageSize, setPerPageSize] = useState([pagination?.pageSize.toString() ?? "10"]);
  const queryClient = useQueryClient();

  // For polaris index table
  const { mode, setMode } = useSetIndexFiltersMode();
  const { selectedResources, allResourcesSelected, handleSelectionChange, clearSelection } = useIndexResourceState(
    emails,
    {
      resourceIDResolver: (email: any): string => {
        return email._id;
      },
    }
  );

  // Table tab switch
  const handleTabSwitch = useCallback((selectedTabIndex: number) => {
    clearSelection();
    setPerPageSize(["10"]);
    setPagination((prev: any) => ({ ...prev, page: 1 }));
    setSelected(selectedTabIndex);
    handlePayloadUpdate({ selected: selectedTabIndex });
  }, []);

  // For pagination filter purpose
  const handleFilterPerPageSize = useCallback((pageSize: string[]) => {
    setPerPageSize(pageSize);
    setPagination((prev: any) => ({ ...prev, pageSize: pageSize[0] ?? 10 }));
  }, []);

  const removeFilterPerPageSize = useCallback(() => {
    setPerPageSize(["10"]);
    setPagination((prev: any) => ({ ...prev, pageSize: 10 }));
  }, []);

  const handleFilterClearAll = useCallback(() => {
    setPerPageSize(["10"]);
    setPagination((prev: any) => ({ ...prev, pageSize: 10 }));
    setMode(IndexFiltersMode.Default);
  }, []);

  const onHandleCancel = () => {
    setPerPageSize(["10"]);
    setPagination((prev: any) => ({ ...prev, pageSize: 10 }));
    setMode(IndexFiltersMode.Default);
    handlePayloadUpdate({ q: "" });
  };

  const handlePagination = (newPage: number) => {
    setPagination((prev: any) => ({ ...prev, page: newPage }));
    clearSelection();
  };

  const filters = [
    {
      key: "perPageSize",
      label: "Show emails",
      filter: (
        <ChoiceList
          title="Show emails"
          titleHidden
          choices={[
            { label: "10", value: "10" },
            { label: "25", value: "25" },
            { label: "50", value: "50" },
            { label: "100", value: "100" },
            { label: "200", value: "200" },
          ]}
          selected={perPageSize || []}
          onChange={handleFilterPerPageSize}
        />
      ),
      // shortcut: true,
      pinned: true,
      hideClearButton: true,
    },
  ];

  const appliedFilters =
    perPageSize && !isEmpty(perPageSize)
      ? [
          {
            key: "perPageSize",
            label: perPageSize.map((val) => `Show emails: ${val}`).join(", "),
            onRemove: removeFilterPerPageSize,
          },
        ]
      : [];

  const [isResendEmailLoading, setIsResendEmailLoading] = useState(false);
  const [isRemoveEmailLoading, setIsRemoveEmailLoading] = useState(false);

  const bulkActions = [
    {
      content: isResendEmailLoading ? "Procesing..." : selected ? "Resend" : "Send Now",
      onAction: async () => {
        try {
          setIsResendEmailLoading(true);

          await handleResendMail({ ids: selectedResources });
        } catch (error) {
          console.log(error);
        }

        await sleep(1500);

        queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_ANALYTICS] });

        setIsResendEmailLoading(false);
        clearSelection();

        shopify.toast.show("Email send", {
          duration: 5000,
        });
      },
    },
  ];

  if (selected === 0) {
    bulkActions.unshift({
      content: isRemoveEmailLoading ? "Procesing..." : "Remove",
      onAction: async () => {
        try {
          setIsRemoveEmailLoading(true);

          await handleRemoveEmail({ ids: selectedResources });
        } catch (error) {
          console.log(error);
        }

        await sleep(1500);

        queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_ANALYTICS] });

        setIsRemoveEmailLoading(false);
        clearSelection();

        shopify.toast.show("Email removed", {
          duration: 5000,
        });
      },
    });
  }

  return (
    <BlockStack gap="200">
      <Card padding="0">
        <IndexFilters
          queryValue={payload?.q || ""}
          queryPlaceholder="Searching via value"
          onQueryChange={(value) => handlePayloadUpdate({ q: value })}
          onQueryClear={() => handlePayloadUpdate({ q: "" })}
          cancelAction={{
            onAction: onHandleCancel,
            disabled: false,
            loading: false,
          }}
          tabs={tabItems}
          selected={selected}
          onSelect={handleTabSwitch}
          filters={filters}
          appliedFilters={appliedFilters}
          onClearAll={handleFilterClearAll}
          mode={mode}
          setMode={setMode}
          canCreateNewView={false}
          loading={isLoading}
        />

        <IndexTable
          condensed={useBreakpoints().smDown}
          resourceName={{ singular: "email", plural: "emails" }}
          itemCount={pagination?.totalItems}
          selectedItemsCount={allResourcesSelected ? "All" : selectedResources.length}
          onSelectionChange={handleSelectionChange}
          headings={[
            { title: "Name" },
            { title: "Email" },
            { title: "Date & Time" },
            { title: "Review Link" },
            { title: "Order Link" },
          ]}
          hasMoreItems={false}
          promotedBulkActions={bulkActions}
          pagination={
            pagination?.totalItems > 0
              ? {
                  hasPrevious: pagination.page > 1 && !isLoading,
                  onPrevious: () => {
                    handlePagination(pagination.page - 1);
                  },
                  hasNext: pagination.page * pagination.pageSize < pagination.totalItems && !isLoading,
                  onNext: () => {
                    handlePagination(pagination.page + 1);
                  },
                  previousTooltip: 'Previous page',
                  nextTooltip: 'Next page',
                  label: 'Page'
                }
              : undefined
          }
        >
          {emails.length > 0 &&
            emails.map((email, index) => {
              return (
                <EmailListItem
                  email={email}
                  selectedResources={selectedResources}
                  key={index}
                />
              );
            })}
        </IndexTable>
      </Card>
    </BlockStack>
  );
}

interface EmailListItemProps {
  email: any;
  selectedResources: string[];
}

const EmailListItem = ({ email, selectedResources }: EmailListItemProps) => {
  return (
    <IndexTable.Row
      id={email._id}
      key={email._id}
      selected={selectedResources.includes(email._id)}
      position={0}
      onNavigation={(event: any) => {
        event.preventDefault();
      }}
      onClick={() => {}} // Prevent row selection on any click within the row
    >
      <IndexTable.Cell>{email.firstName}</IndexTable.Cell>
      <IndexTable.Cell>
        <Box paddingBlock="200">
          <Text
            variant="bodyMd"
            fontWeight="medium"
            as="span"
          >
            {email.email}
          </Text>
        </Box>
      </IndexTable.Cell>
      <IndexTable.Cell>{formatDateTime(email.sendDate)}</IndexTable.Cell>
      <IndexTable.Cell>
        <Button
          variant="plain"
          onClick={() => {
            window.open(email.reviewURL, "_blank");
          }}
        >
          {capitalizeFirstLetter(email.reviewPlatform)}
        </Button>
      </IndexTable.Cell>
      <IndexTable.Cell>
        <Button
          variant="plain"
          onClick={() => {
            window.open(`https://${email.shop}/admin/orders/${email.orderId}`, "_blank");
          }}
        >
          Go to order
        </Button>
      </IndexTable.Cell>
    </IndexTable.Row>
  );
};
