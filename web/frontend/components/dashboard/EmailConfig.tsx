import React, { useCallback, useState } from "react";

import {
    <PERSON>List,
    BlockStack,
    Box,
    Button,
    Card,
    Icon,
    InlineStack,
    Link,
    Popover,
    Text,
    Tooltip,
} from "@shopify/polaris";

import { InfoIcon, MenuHorizontalIcon, RedoIcon, XIcon } from "@shopify/polaris-icons";

import { addDays } from "date-fns";
import { useNavigate } from "react-router-dom";
import { useMutation } from "storeware-tanstack-query";
import configureSettings from "../../../app/enums/configureSettings";
import { saveConfigureSettings } from "../../apis/configureSettings.api";

// Props interface for EmailConfig component
interface EmailConfigProps {
  setEmailConfigureSetting: (value: boolean) => void;
}

export default function EmailConfig({ setEmailConfigureSetting }: EmailConfigProps): React.ReactElement {
  const [popoverActive, setPopoverActive] = useState<boolean>(false);

  const togglePopoverActive = useCallback(() => {
    setPopoverActive((popoverActive) => !popoverActive);
  }, []);

  const activator = (
    <Button
      onClick={togglePopoverActive}
      variant="tertiary"
      icon={MenuHorizontalIcon}
    ></Button>
  );

  const { mutate: handleSaveConfigureSettings } = useMutation({
    mutationFn: (payload: any) => saveConfigureSettings(payload),
    onSuccess: () => {
      setEmailConfigureSetting(false);
    },
    onError: (error: any) => {
      console.error("Error saving email config settings:", error);
    },
    onSettled: () => {
      setPopoverActive(false);
    },
  });

  const defaultDismissItems = [
    {
      content: "Remind me later",
      icon: RedoIcon,
      onAction: () =>
        handleSaveConfigureSettings({
          key: configureSettings.EMAIL_CONFIGURE_SETTING,
          value: addDays(new Date(), 7).toISOString(),
        }),
    },
    {
      content: "Dismiss",
      icon: XIcon,
      destructive: true,
      onAction: () =>
        handleSaveConfigureSettings({
          key: configureSettings.EMAIL_CONFIGURE_SETTING,
          value: null,
        }),
    },
  ];

  const navigate = useNavigate();

  return (
    <Card>
      <BlockStack gap="300">
        <BlockStack gap="100">
          <InlineStack
            blockAlign="center"
            align="space-between"
            gap="100"
          >
            <InlineStack
              blockAlign="center"
              gap="100"
            >
              <Text
                as="h4"
                variant="headingSm"
              >
                Email configuration
              </Text>
              <Tooltip content="How to configure it?">
                <Link
                  url="https://trustsync.io/docs-category/configuration/"
                  target="_blank"
                  monochrome
                >
                  <Icon source={InfoIcon} />
                </Link>
              </Tooltip>
            </InlineStack>
            <Popover
              active={popoverActive}
              activator={activator}
              autofocusTarget="first-node"
              onClose={togglePopoverActive}
            >
              <ActionList
                actionRole="menuitem"
                items={defaultDismissItems}
              />
            </Popover>
          </InlineStack>
          <Text
            as="p"
            variant="bodySm"
            tone="subdued"
          >
            Set up your review request emails to automatically ask your customers to review your business
          </Text>
        </BlockStack>
        <Box>
          <Button
            onClick={(): void => {
              navigate("/settings");
            }}
          >
            Configure your email
          </Button>
        </Box>
      </BlockStack>
    </Card>
  );
}
