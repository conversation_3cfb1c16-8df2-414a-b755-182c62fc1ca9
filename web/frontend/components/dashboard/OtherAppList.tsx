import { <PERSON><PERSON>ist, <PERSON><PERSON>tack, <PERSON><PERSON>, Card, InlineGrid, InlineStack, Popover, Text } from "@shopify/polaris";
import { addDays } from "date-fns";
import React, { useCallback, useState } from "react";
import { otherAppList } from "../../utils/other-apps";
import OtherAppItem from "./OtherAppItem";

import { MenuHorizontalIcon, RedoIcon, XIcon } from "@shopify/polaris-icons";
import { useMutation } from "storeware-tanstack-query";
import configureSettings from "../../../app/enums/configureSettings";
import { ConfigureSettingsPayload, saveConfigureSettings } from "../../apis/configureSettings.api";

interface OtherAppListProps {
  setPartnerConfigureSetting: (value: boolean) => void;
}

function OtherAppList({ setPartnerConfigureSetting }: OtherAppListProps) {
  const [popoverActive, setPopoverActive] = useState(false);

  const togglePopoverActive = useCallback(() => {
    setPopoverActive((popoverActive) => !popoverActive);
  }, []);

  const activator = (
    <Button
      onClick={togglePopoverActive}
      variant="tertiary"
      icon={MenuHorizontalIcon}
    ></Button>
  );

  const { mutate: handleSaveConfigureSettings } = useMutation({
    mutationFn: (payload: ConfigureSettingsPayload) => saveConfigureSettings(payload),
    onSuccess: () => {
      setPartnerConfigureSetting(false);
    },
    onError: (error) => {
      console.error("Error saving partner configure setting:", error);
    },
    onSettled: () => {
      setPopoverActive(false);
    },
  });

  const defaultDismissItems = [
    {
      content: "Remind me later",
      icon: RedoIcon,
      onAction: () =>
        handleSaveConfigureSettings({
          key: configureSettings.PARTNER_CONFIGURE_SETTING,
          value: addDays(new Date(), 7).toISOString(),
        }),
    },
    {
      content: "Dismiss",
      icon: XIcon,
      destructive: true,
      onAction: () => handleSaveConfigureSettings({ key: configureSettings.PARTNER_CONFIGURE_SETTING, value: null }),
    },
  ];

  return (
    <Card>
      <BlockStack gap="400">
        <BlockStack gap="100">
          <InlineStack
            align="space-between"
            blockAlign="center"
          >
            <Text
              as="h4"
              variant="headingSm"
            >
              Discover apps for additional functionalities
            </Text>

            <Popover
              active={popoverActive}
              activator={activator}
              autofocusTarget="first-node"
              onClose={togglePopoverActive}
            >
              <ActionList
                actionRole="menuitem"
                items={defaultDismissItems}
              />
            </Popover>
          </InlineStack>
          <Text as="p">Take your Shopify experience to the next level with our recommended apps</Text>
        </BlockStack>

        <InlineGrid
          columns={3}
          gap="400"
        >
          {otherAppList.map((app, index) => (
            <OtherAppItem
              key={index}
              name={app.name}
              description={app.description}
              logo={app.logo}
              url={app.url}
            />
          ))}
        </InlineGrid>
      </BlockStack>
    </Card>
  );
}

export default OtherAppList;
