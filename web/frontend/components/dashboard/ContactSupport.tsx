import { Badge, InlineStack, MediaCard, Text } from "@shopify/polaris";
import { ChatIcon, PhoneIcon, RedoIcon, XIcon } from "@shopify/polaris-icons";
import { addDays } from "date-fns";
import { useMutation } from "storeware-tanstack-query";
import configureSettings from "../../../app/enums/configureSettings";
import { saveConfigureSettings } from "../../apis/configureSettings.api";
import { useAppContext } from "../../providers/AppProvider";
import { VerifyBadgeIcon } from "../common/SvgIcons";

// Props interface for ContactSupport component
interface ContactSupportProps {
  setConfigureSetting: (value: boolean) => void;
}

export default function ContactSupport({ setConfigureSetting }: ContactSupportProps): React.ReactElement {
  const { shop, isShopUnderProPlan } = useAppContext();

  const isShopVerified = shop?.verified && isShopUnderProPlan;

  const { mutate: handleSaveConfigureSettings } = useMutation({
    mutationFn: (payload: any) => saveConfigureSettings(payload),
    onSuccess: () => {
      setConfigureSetting(false);
    },
    onError: (error: any) => {
      console.error("Error saving contact support configure setting:", error);
    },
  });

  const defaultDismissItems = [
    {
      content: "Remind me later",
      icon: RedoIcon,
      onAction: () =>
        handleSaveConfigureSettings({
          key: configureSettings.CONTACT_SUPPORT_CONFIGURE_SETTING,
          value: addDays(new Date(), 7).toISOString(),
        }),
    },
    {
      content: "Dismiss",
      icon: XIcon,
      destructive: true,
      onAction: () =>
        handleSaveConfigureSettings({
          key: configureSettings.CONTACT_SUPPORT_CONFIGURE_SETTING,
          value: null,
        }),
    },
  ];

  return (
    <MediaCard
      title={
        isShopVerified ? (
          <Text as="p">Want to schedule a call with TrustSync experts?</Text>
        ) : (
          <InlineStack
            blockAlign="center"
            gap="100"
          >
            <VerifyBadgeIcon /> Verify your store for FREE & get 2X email limit &nbsp;
            <Badge tone="attention">Unverified</Badge>
          </InlineStack>
        )
      }
      popoverActions={defaultDismissItems}
      primaryAction={{
        content: "Schedule a call",
        icon: PhoneIcon,
        url: "https://storeware.io/trustsync/talk-with-expert",
        target: "_blank",
      }}
      secondaryAction={
        isShopVerified
          ? undefined
          : {
              content: "Open live chat",
              icon: ChatIcon,
              onAction: (): void => {
                (window as any).$crisp.do("chat:open");

                (window as any).$crisp.push([
                  "do",
                  "message:send",
                  [
                    "text",
                    "Hi, I’d like to verify my store to remove the TrustSync branding from email and double my email limit. Can you please help me with this?",
                  ],
                ]);
              },
            }
      }
      description={
        isShopVerified
          ? "We have some in-house Shopify experts ready to help if you need anything when using our app. Would you like to book a one-on-one session with them? It’s completely FREE!"
          : "Do you want to remove TrustSync branding and increase your email limit for free? Contact support team now & get help with verification, installation, configuration, and double your email limit."
      }
      size="small"
    >
      <img
        width="100%"
        height="100%"
        style={{
          objectFit: "cover",
          objectPosition: "center",
        }}
        src="data:image/png;base64,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"
      />
    </MediaCard>
  );
}
