import { BlockSta<PERSON>, Box, InlineStack, Link, Text } from "@shopify/polaris";

function OtherAppItem({
  name,
  description,
  logo,
  url,
}: {
  name: string;
  description: string;
  logo: string;
  url: string;
}) {
  return (
    <Box>
      <InlineStack
        wrap={false}
        gap="400"
      >
        <Box>
          <img
            src={logo}
            alt={name}
          />
        </Box>
        <BlockStack gap="200">
          <Text
            tone="success"
            as="h3"
            variant="headingSm"
          >
            {name}
          </Text>
          <Text
            as="p"
            variant="bodyMd"
            tone="subdued"
          >
            {description}
          </Text>
          <Link
            url={url}
            target="_blank"
          >
            View App
          </Link>
        </BlockStack>
      </InlineStack>
    </Box>
  );
}

export default OtherAppItem;
