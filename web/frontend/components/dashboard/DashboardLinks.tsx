import { <PERSON><PERSON>tack, Box, Card, Icon, InlineStack, Link, Text } from "@shopify/polaris";
import { ArrowRightIcon } from "@shopify/polaris-icons";
import React, { useState } from "react";
import { DocumentationSvg, FeatureSvg, SupportSvg, TutorialSvg } from "../common/SvgIcons";

// Type declaration
interface IAllLinksData {
  title: string;
  description: string;
  illustration: React.ReactElement;
  action: string;
}

type IconTone = "base" | "subdued";

const allLinksData: IAllLinksData[] = [
  {
    title: "Tutorial",
    description: "Check out our collection of video tutorials to get started",
    illustration: <TutorialSvg />,
    action: "https://www.youtube.com/playlist?list=PL_ZFczsfC-xdpKmLEJsicVm88SYf-zV3u",
  },
  {
    title: "Documentation",
    description: "Read our step-by-step guidelines for each and every feature",
    illustration: <DocumentationSvg />,
    action: "https://trustsync.io/docs",
  },
  {
    title: "Support",
    description: "Feel free to reach out to us at any time to solve your issue",
    illustration: <SupportSvg />,
    action: "https://storeware.io/trustsync/talk-with-expert",
  },
  {
    title: "TrustSync Walkthrough",
    description: "Talk with our experts and get a complete TrustSync Walkthrough",
    illustration: <FeatureSvg />,
    action: "https://storeware.io/trustsync/talk-with-expert",
  },
];

function DashboardLinks() {
  const [iconHover, setIconHover] = useState<IconTone[]>(allLinksData.map(() => "subdued"));

  const handleMouseEnter = (indexNo: number) => {
    const newValue = [...iconHover];
    newValue[indexNo] = "base";
    setIconHover(newValue);
  };

  const handleMouseLeave = (indexNo: number) => {
    const newValue = [...iconHover];
    newValue[indexNo] = "subdued";
    setIconHover(newValue);
  };

  return (
    <Card background="bg-surface-secondary">
      <BlockStack gap="400">
        <BlockStack gap="200">
          <Text
            as="h2"
            variant="headingLg"
          >
            Stay tuned with TrustSync
          </Text>
          <Text
            as="p"
            variant="bodyLg"
          >
            Need more assistance? Check out our tutorial and documentation or knock our support team anytime.
          </Text>
        </BlockStack>
        <BlockStack gap="200">
          {allLinksData &&
            allLinksData.map((link, index) => (
              <Card key={index}>
                <InlineStack
                  gap="200"
                  align="space-between"
                  blockAlign="center"
                  wrap={false}
                >
                  <InlineStack
                    gap="200"
                    align="center"
                    blockAlign="center"
                    wrap={false}
                  >
                    <Box>
                      <div style={{ height: 40, width: 40 }}>{link.illustration}</div>
                    </Box>
                    <BlockStack>
                      <Text
                        as="p"
                        variant="headingMd"
                      >
                        {link.title}
                      </Text>
                      <Text
                        as="p"
                        variant="bodyMd"
                        tone="subdued"
                      >
                        {link.description}
                      </Text>
                    </BlockStack>
                  </InlineStack>
                  <div style={{ lineHeight: 0 }}>
                    <span
                      onMouseEnter={() => handleMouseEnter(index)}
                      onMouseLeave={() => handleMouseLeave(index)}
                    >
                      {/* For support link */}
                      {link.title === "Support" && (
                        <Link
                          onClick={() => {
                            window.$crisp.do("chat:open");
                          }}
                          monochrome
                        >
                          <span
                            style={{
                              background: "var(--p-color-bg-surface-secondary)",
                              padding: "var(--p-space-200)",
                              borderRadius: "var(--p-border-radius-full)",
                              display: "inline-block",
                              lineHeight: "0",
                            }}
                          >
                            <Icon
                              source={ArrowRightIcon}
                              tone={iconHover[index]}
                            />
                          </span>
                        </Link>
                      )}

                      {/* For all the others   */}
                      {link.title !== "Support" && (
                        <Link
                          target="_blank"
                          url={link.action}
                          monochrome
                        >
                          <span
                            style={{
                              background: "var(--p-color-bg-surface-secondary)",
                              padding: "var(--p-space-200)",
                              borderRadius: "var(--p-border-radius-full)",
                              display: "inline-block",
                              lineHeight: "0",
                            }}
                          >
                            <Icon
                              source={ArrowRightIcon}
                              tone={iconHover[index]}
                            />
                          </span>
                        </Link>
                      )}
                    </span>
                  </div>
                </InlineStack>
              </Card>
            ))}
        </BlockStack>
      </BlockStack>
    </Card>
  );
}

export default DashboardLinks;
