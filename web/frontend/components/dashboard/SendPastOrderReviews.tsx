import { useCallback, useEffect, useState } from "react";

import { Banner, BlockStack, Box, Button, Card, Select, SkeletonBodyText, Text } from "@shopify/polaris";
import { useQueryClient } from "storeware-tanstack-query";
import queryKeys from "../../../app/enums/queryKeys";

export default function SendPastOrderReviews() {
  const [loading, setLoading] = useState(false);
  const [selectPastOrder, setSelectPastOrder] = useState("10");
  const [pastOrderOptions, setPastOrderOptions] = useState<{ label: string; value: string }[]>([]);
  const queryClient = useQueryClient();

  const handleSelectChange = useCallback((value: string) => {
    setSelectPastOrder(value);
  }, []);

  const [isSavingInProgress, setIsSavingInProgress] = useState(false);

  const handlePastOrder = async () => {
    try {
      setIsSavingInProgress(true);

      const response = await fetch(`/api/v1/past-orders/schedule`, {
        method: "POST",
        body: JSON.stringify({ selectPastOrder }),
        headers: { "Content-Type": "application/json" },
      });

      await response.json();

      queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_SHOP_INFO] });

      shopify.toast.show("Schdule past orders", {
        duration: 5000,
      });
    } catch (error) {
      console.error("Error when process past orders:", error);
    } finally {
      setIsSavingInProgress(false);
    }
  };

  const loadPastOrders = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/v1/past-orders`);

      const { data } = await response.json();

      setPastOrderOptions(data);
    } catch (error) {
      console.error("Error saving custom sender email:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPastOrders();
  }, []);

  if (loading) {
    return <SkeletonBodyText />;
  }

  return pastOrderOptions?.length > 0 ? (
    <Card>
      <BlockStack gap="300">
        <BlockStack gap="100">
          <Text
            as="h4"
            variant="headingSm"
          >
            Easy way to get more reviews
          </Text>
          <Text
            as="p"
            variant="bodyMd"
            tone="subdued"
          >
            Send review request from people who already bought from you, even before this app was installed.
          </Text>
        </BlockStack>
        <BlockStack gap="200">
          <Select
            label=""
            options={pastOrderOptions}
            onChange={handleSelectChange}
            value={selectPastOrder}
            placeholder={loading ? "Loading..." : undefined}
            disabled={loading}
          />
          <Banner tone="success">
            <Text
              as="p"
              variant="bodyMd"
            >
              Up to 77% of your past customers will leave a review!
            </Text>
          </Banner>
        </BlockStack>
        <Box>
          <Button
            onClick={handlePastOrder}
            loading={isSavingInProgress}
          >
            Send review request email
          </Button>
        </Box>
      </BlockStack>
    </Card>
  ) : null;
}
