import { useNavigate } from "react-router-dom";

import { BlockStack, Button, Card, Icon, InlineStack, ProgressBar, Text, Tooltip } from "@shopify/polaris";

import { InfoIcon } from "@shopify/polaris-icons";
import { differenceInDays } from "date-fns";
import { useAppContext } from "../../providers/AppProvider";
import { calculatePercentage, capitalizeFirstLetter } from "../../utils/helper";

export default function SubscribePlanOverview() {
  const { subscription } = useAppContext();

  const navigate = useNavigate();

  const upgradeSubscriptionPlan = () => {
    navigate("/subscription");
  };

  const resetDayCount = differenceInDays(new Date(subscription?.resetDate || ""), new Date());

  return (
    <Card>
      <BlockStack gap="400">
        <InlineStack
          blockAlign="center"
          align="space-between"
          gap="400"
          wrap={false}
        >
          <InlineStack
            blockAlign="center"
            gap="100"
          >
            <Text
              as="h4"
              variant="headingSm"
            >
              Email <PERSON>t (Current Plan: {capitalizeFirstLetter(subscription?.plan || "")} - {subscription?.limitEmails}{" "}
              emails/Month)
            </Text>

            <Tooltip
              content="Your email will stop sending until your monthly limit resets. You can upgrade to lift your limits"
              dismissOnMouseOut
            >
              <Icon source={InfoIcon} />
            </Tooltip>
          </InlineStack>
          <Button onClick={upgradeSubscriptionPlan}>Upgrade your plan</Button>
        </InlineStack>
        <BlockStack gap="100">
          <InlineStack
            align="space-between"
            blockAlign="center"
          >
            <Text
              as="p"
              variant="bodyMd"
              tone="subdued"
            >
              {subscription?.emails}/{subscription?.limitEmails} emails
            </Text>
            <Text
              as="p"
              variant="bodyMd"
              tone="subdued"
            >
              {resetDayCount === 0
                ? "Monthly usage resets today"
                : resetDayCount === 1
                  ? "Monthly usage resets tomorrow"
                  : `Monthly usage resets in ${resetDayCount} days`}
            </Text>
          </InlineStack>
          <ProgressBar
            progress={calculatePercentage(subscription?.emails || 0, subscription?.limitEmails || 0)}
            size="small"
            tone="success"
          />
        </BlockStack>
      </BlockStack>
    </Card>
  );
}
