import { Card, Text } from "@shopify/polaris";
import Skeleton from "./Skeleton";

interface StatisticCardProps {
  content: string;
}

export default function StatisticCard({ content }: StatisticCardProps) {
  return (
    <Card>
      <Skeleton style={{ height: 23, width: 40, marginBottom: 15 }} />
      <Text
        as="p"
        variant="bodySm"
      >
        {content}
      </Text>
    </Card>
  );
}
