import { InlineStack, Text } from "@shopify/polaris";
import Skeleton from "./Skeleton";

interface OnboardListItemProps {
  content: string;
}

export default function OnboardListItem({ content }: OnboardListItemProps) {
  return (
    <InlineStack gap="300">
      <Skeleton style={{ height: 20, width: 20, borderRadius: 20 }} />

      <div style={{ flex: 1 }}>
        <Text
          as="h3"
          variant="bodyMd"
          fontWeight="semibold"
        >
          {content}
        </Text>
      </div>
    </InlineStack>
  );
}
