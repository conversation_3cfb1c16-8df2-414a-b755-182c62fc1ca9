import { <PERSON>, <PERSON>S<PERSON><PERSON>, <PERSON>, <PERSON>ton, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Text } from "@shopify/polaris";
import { PhoneIcon } from "@shopify/polaris-icons";
import { useEffect, useState } from "react";
import DnsRecordItem from "./DnsRecordItem";

interface DomainVerificationModalProps {
  domainRecord: any;
  domainName: string;
  openVerifiedModal: boolean;
  setOpenVerifiedModal: (open: boolean) => void;
  mailgunDomainMessage: string;
  setMailgunDomainMessage: (message: string) => void;
}

export default function DomainVerificationModal({
  domainRecord,
  domainName,
  openVerifiedModal,
  setOpenVerifiedModal,
  mailgunDomainMessage,
  setMailgunDomainMessage,
}: DomainVerificationModalProps) {
  const [sendingDnsRecords, setSendingDnsRecords] = useState([]);

  useEffect(() => {
    setSendingDnsRecords(domainRecord?.sending_dns_records || []);
  }, [domainRecord]);

  const [isVerifiedDomainInProgress, setIsVerifiedDomainInProgress] = useState(false);

  const verifiedSenderCustomDomain = async () => {
    setMailgunDomainMessage("");
    setIsVerifiedDomainInProgress(true);
    setSendingDnsRecords([]);

    try {
      const response = await fetch(`/api/v1/mailgun/domain-info?domain=${domainName}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const responseData = await response.json();

      if (!response.ok) {
        setMailgunDomainMessage(responseData.message);
      } else {
        const data = responseData?.data || {};

        setSendingDnsRecords(data?.sending_dns_records || []);

        shopify.toast.show(data.state === "active" ? "Verified" : "Not verified", {
          duration: 5000,
        });
      }
    } catch (error) {
      console.log(error);
      console.error("Error fetching data:", error);
    } finally {
      setIsVerifiedDomainInProgress(false);
    }
  };

  return (
    <div style={{ height: "500px" }}>
      <Frame>
        <Modal
          size="large"
          open={openVerifiedModal}
          onClose={() => {
            setOpenVerifiedModal(false);
          }}
          title={`Verify DNS Records for Domain: ${domainName}`}
          primaryAction={{
            content: "Check Domain",
            loading: isVerifiedDomainInProgress,
            onAction: verifiedSenderCustomDomain,
          }}
          secondaryActions={[
            {
              content: "Close",
              onAction: () => {
                setOpenVerifiedModal(false);
              },
            },
          ]}
        >
          <Modal.Section>
            <BlockStack>
              <Box padding="400">
                <BlockStack gap="400">
                  <Card padding="0">
                    <Banner
                      tone="warning"
                      title="Be aware that verifying your domain requires technical knowledge"
                    >
                      <BlockStack gap="200">
                        <Text
                          as="p"
                          variant="bodyMd"
                        >
                          You need to add the following records to your domain provider. Until this is done, we will
                          continue sending <NAME_EMAIL>. If you need help contact us using the live
                          chat.
                        </Text>
                        <Box>
                          <Button
                            url="https://storeware.io/trustsync/talk-with-expert"
                            target="_blank"
                            icon={PhoneIcon}
                          >
                            Contact support team
                          </Button>
                        </Box>
                      </BlockStack>
                    </Banner>
                  </Card>

                  {mailgunDomainMessage === "DOMAIN_NOT_FOUND" && (
                    <Card padding="0">
                      <Banner
                        tone="warning"
                        title="Your domain may be connected to another account."
                      >
                        <Text as="p">
                          Please use a different domain or subdomain to verify.If you need assistance, please contact
                          support.
                        </Text>
                      </Banner>
                    </Card>
                  )}

                  {sendingDnsRecords.map((record: any, index: number) => {
                    let recordName = record.name;
                    if (recordName === domainRecord.name) {
                      recordName = "@";
                    } else if (recordName.includes(`.${domainRecord.name}`)) {
                      recordName = recordName.replace(`.${domainRecord.name}`, "");
                    }

                    return (
                      <DnsRecordItem
                        key={index}
                        record={{ ...record, record_name: recordName }}
                      />
                    );
                  })}

                  {sendingDnsRecords?.length > 0 && (
                    <Card padding="0">
                      <Banner title="DNS records can take up to 72 hours to get verified. If you have any question, please contact us."></Banner>
                    </Card>
                  )}
                </BlockStack>
              </Box>
            </BlockStack>
          </Modal.Section>
        </Modal>
      </Frame>
    </div>
  );
}
