import {
  Badge,
  BlockStack,
  Box,
  Button,
  Card,
  Checkbox,
  Icon,
  InlineGrid,
  InlineStack,
  RadioButton,
  Select,
  Tag,
  Text,
  TextField,
} from "@shopify/polaris";
import { InfoIcon } from "@shopify/polaris-icons";

import { useAppQuery, useMutation, useQueryClient } from "storeware-tanstack-query";

import { useCallback, useEffect, useState } from "react";

import { secondWhenActionOptions, timeOptions, whenActionOptions, whenOptions } from "../../utils/config";

import { EmailSettingsDocument, SecondWhenToSend, WhenToSend } from "@trustsync/types";
import { omit } from "lodash";
import { GeneralSettingsFormData } from "types/index.js";
import queryKeys from "../../../app/enums/queryKeys";
import { fetchSettings, saveGeneralEmailSettings } from "../../apis/settings.api";
import useUnsavedChanges from "../../hooks/useUnsavedChanges";
import { useAppContext } from "../../providers/AppProvider";
import { showNotification, validateDomainName, validateEmail } from "../../utils/helper";
import CardNote from "../common/CardNote";
import ContextualSaveBar from "../common/ContextualSaveBar.jsx";
import ProPlanLink from "../common/ProPlanLink";
import SettingLoader from "../loader/SettingLoader";
import DomainVerificationModal from "./DomainVerificationModal";

interface ValidationMessage {
  sender?: string;
  domain?: string;
  selectedReplyToEmails?: string;
  tag?: string;
}

export default function GeneralSettings() {
  const { isShopUnderProPlan } = useAppContext();
  const queryClient = useQueryClient();

  const [active, setActive] = useState<boolean>(false);

  // Set Send Past Order
  const [sentPastOrders, setSentPastOrders] = useState<boolean>(false);

  // Domain verfication
  const [sender, setSender] = useState("hello");
  const [domain, setDomain] = useState("trustsync.io");
  const [isSenderDomainVerified, setIsSenderDomainVerified] = useState(false);

  const handleSendOnRepeat = useCallback((_: any, value: string) => {
    // setSendOnRepeat(value);
    setFormData((prevFormData) => ({
      ...prevFormData,
      sendOnRepeat: value,
    }));
  }, []);

  const [formData, setFormData] = useState({} as GeneralSettingsFormData);
  const [originalFormData, setOriginalFormData] = useState({} as GeneralSettingsFormData);

  const { hasUnsavedChanges, DiscardChangesModal, showDiscardModal } = useUnsavedChanges({
    originalData: originalFormData,
    currentData: formData,
    onDiscardAction: () => {
      setFormData(originalFormData);
    },
  });

  const removeBlacklistEmailTag = useCallback(
    (tag: string) => () => {
      setFormData((prev) => ({
        ...prev,
        selectedBlacklistEmails: (prev.selectedBlacklistEmails || []).filter((previousTag) => previousTag !== tag),
      }));
    },
    []
  );

  const blackistEmailTagList = formData?.selectedBlacklistEmails?.map((option, index) => (
    <Tag
      key={index}
      onRemove={removeBlacklistEmailTag(option)}
    >
      {option}
    </Tag>
  ));

  const [blacklistErrorMessage, setBlacklistErrorMessage] = useState("");
  // Create a separate state for the blacklisted email input
  const [blackListedEmailInput, setBlackListedEmailInput] = useState("");

  // Function to handle adding a blacklisted email tag
  const addBlackListEmailTag = (event: any) => {
    setBlacklistErrorMessage("");

    // Check if the Enter key is pressed
    if (event.keyCode !== 13) {
      return false; // Do nothing if it's not the Enter key
    }

    // If the input is empty, just return
    if (!blackListedEmailInput || blackListedEmailInput.trim() === "") {
      return false;
    }

    // Validate the entered email
    if (!validateEmail(blackListedEmailInput)) {
      setBlacklistErrorMessage("Please enter a valid email.");
      return false; // Stop execution if the email is invalid
    }

    // Check for duplicate email
    if (formData?.selectedBlacklistEmails?.includes(blackListedEmailInput)) {
      setBlacklistErrorMessage("This email is already blacklisted.");
      return false; // Stop execution if the email is a duplicate
    }

    // Add the validated email to the list of blacklisted emails
    setFormData((prev) => ({
      ...prev,
      selectedBlacklistEmails: [...(prev.selectedBlacklistEmails || []), blackListedEmailInput],
    }));

    // Clear the input field after adding the email
    setBlackListedEmailInput("");
  };

  const removeReplyToEmailTag = useCallback(
    (tag: string) => () => {
      setFormData((prev) => ({
        ...prev,
        selectedReplyToEmails: (prev.selectedReplyToEmails || []).filter((previousTag) => previousTag !== tag),
      }));
    },
    []
  );

  const replyToEmailTagList = formData?.selectedReplyToEmails?.map((option, index) => (
    <Tag
      key={index}
      onRemove={removeReplyToEmailTag(option)}
    >
      {option}
    </Tag>
  ));

  const [replyToErrorMessage, setReplyToErrorMessage] = useState("");
  // Create a separate state for the reply to email input
  const [replyToEmailInput, setReplyToEmailInput] = useState("");

  // Function to handle adding a reply to email tag
  const addReplyToEmailTag = (event: any) => {
    setReplyToErrorMessage("");

    // Check if the Enter key is pressed
    if (event.keyCode !== 13) {
      return false; // Do nothing if it's not the Enter key
    }

    // If the input is empty, just return
    if (!replyToEmailInput || replyToEmailInput.trim() === "") {
      return false;
    }

    // Validate the entered email
    if (!validateEmail(replyToEmailInput)) {
      setReplyToErrorMessage("Please enter a valid email.");
      return false; // Stop execution if the email is invalid
    }

    // Check for duplicate email
    if (formData?.selectedBlacklistEmails?.includes(replyToEmailInput)) {
      setReplyToErrorMessage("This email is already added.");
      return false; // Stop execution if the email is a duplicate
    }

    // Add the validated email to the list of reply to emails
    setFormData((prev) => ({
      ...prev,
      selectedReplyToEmails: [...(prev.selectedReplyToEmails || []), replyToEmailInput],
    }));

    // Clear the input field after adding the email
    setReplyToEmailInput("");
  };

  const [domainRecord, setDomainRecord] = useState([]);
  const [openVerifiedModal, setOpenVerifiedModal] = useState(false);
  const [isAddDomainInProgress, setIsAddDomainInProgress] = useState(false);
  const [isVerifiedDomainInProgress, setIsVerifiedDomainInProgress] = useState(false);
  const [mailgunDomainMessage, setMailgunDomainMessage] = useState("");

  const saveCustomDomainInMailgun = async () => {
    if (!validateDomainName(formData?.domain || domain)) {
      setDomainErrorMessage("Domain name is not valid.");
      return false;
    }

    setIsAddDomainInProgress(true);
    setDomainErrorMessage("");

    try {
      const response = await fetch(`/api/v1/mailgun/add-domain`, {
        method: "POST",
        body: JSON.stringify({ domain: formData?.domain || domain }),
        headers: { "Content-Type": "application/json" },
      });

      const { status, message } = await response.json();

      if ((formData?.domain || domain) != "trustsync.io") {
        if (status) {
          verifiedSenderCustomDomain();
        } else {
          setDomainErrorMessage(message);
        }
      }
    } catch (error) {
      console.error("Error saving custom sender email:", error);
    } finally {
      queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_SETTING] });
      queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_SHOP_INFO] });
      setIsAddDomainInProgress(false);
    }
  };

  const verifiedSenderCustomDomain = async () => {
    setIsVerifiedDomainInProgress(true);
    setDomainErrorMessage("");
    setMailgunDomainMessage("");
    setDomainRecord([]);

    try {
      const response = await fetch(`/api/v1/mailgun/domain-info?domain=${formData?.domain || domain}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const responseData = await response.json();

      if (!response.ok) {
        setMailgunDomainMessage(responseData.message);
      } else {
        const data = responseData?.data || {};

        setDomainRecord(data);
        setIsSenderDomainVerified(data?.state === "active");
      }

      setOpenVerifiedModal(true);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsVerifiedDomainInProgress(false);
    }
  };

  const [validationMessage, setValidationMessage] = useState<ValidationMessage>({});
  const [domainErrorMessage, setDomainErrorMessage] = useState("");

  const { mutate: saveGeneralEmailSetting, isPending: isSaving } = useMutation({
    mutationFn: () => saveGeneralEmailSettings(omit({ ...formData }, ["domain"])),
    onSuccess: (response) => {
      if (response?.status === 422) {
        setValidationMessage(response.errors);
      } else if (response?.status === 200) {
        queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_SETTING] });
        queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_SHOP_INFO] });

        showNotification({ message: "Settings updated" });
      }
    },
    onError: (err) => {
      console.log("error updating email settings", err);
    },
  });

  const { data: { shop, settings } = {}, isLoading } = useAppQuery({
    queryKey: [queryKeys.FETCH_SETTING],
    queryFn: fetchSettings,
    reactQueryOptions: {
      enabled: true,
      staleTime: 0,
    },
  });

  useEffect(() => {
    if (settings) {
      const baseFormData = {
        active: settings.active,
        sentPastOrders: shop?.sentPastOrders || false,

        whenToSend: {
          days: settings?.whenToSend?.days || "1",
          after: settings?.whenToSend?.after || "fulfilled",
          tag: settings?.whenToSend?.tag || "",
        },
        sendOnRepeat: settings?.sendOnRepeat || "no",
        secondEmail: settings?.secondEmail || false,
        secondWhenToSend: {
          days: settings?.secondWhenToSend?.days || "1",
          after: settings?.secondWhenToSend?.after || "email",
        },
        onlyOnlineStoreOrders: settings?.onlyOnlineStoreOrders || false,
        specificTimeActive: settings?.specificTimeActive || false,
        specificTime: settings?.specificTime,
        selectedBlacklistEmails: settings?.blacklistedEmails || [], // Initialize blacklisted emails
        sender: settings?.sender || "hello", // Initialize sender
        domain: settings?.domain || "trustsync.io", // Initialize domain
      };

      // Default replyToEmails logic
      let replyToEmails: string[] = [];
      if (shop) {
        replyToEmails = shop?.info?.email ? [shop.info.email] : ["<EMAIL>"];
        if (settings?.replyToEmails?.length) {
          replyToEmails = settings.replyToEmails;
        }
        setSentPastOrders(shop?.sentPastOrders as boolean);
      }

      // Finally set formData and originalFormData
      setFormData({ ...baseFormData, selectedReplyToEmails: replyToEmails });
      setOriginalFormData({ ...baseFormData, selectedReplyToEmails: replyToEmails });
    }
  }, [settings, shop]);

  useEffect(() => {
    if (settings) {
      // Set both local state and update formData
      setActive(settings?.active as boolean);
      const senderValue = settings?.sender || "hello";
      const domainValue = settings?.domain || "trustsync.io";
      setSender(senderValue);
      setDomain(domainValue);
      setIsSenderDomainVerified(settings?.domainVerified || false);
    }
  }, [settings]);

  useEffect(() => {
    setDomainErrorMessage(validationMessage?.sender || validationMessage?.domain || "");
    setReplyToErrorMessage(validationMessage?.selectedReplyToEmails || "");
  }, [validationMessage]);

  if (isLoading) {
    return <SettingLoader />;
  }

  const isValidDomainAndSender =
    (formData?.domain || domain) !== "" &&
    (formData?.sender || sender) !== "" &&
    (formData?.domain || domain) !== "trustsync.io";
  const isDomainVerified = isSenderDomainVerified && settings?.domain === (formData?.domain || domain);

  return (
    <Box
      paddingBlockStart="400"
      paddingBlockEnd="1000"
    >
      <BlockStack gap="400">
        {/* Email Sending Activation */}
        <Card>
          <InlineStack
            blockAlign="center"
            align="space-between"
          >
            <Text
              as="h3"
              variant="headingSm"
            >
              Email sending is {formData?.active ? "activated" : "deactived"}.
            </Text>
            <Button
              onClick={() => {
                const newActiveValue = !active;
                setActive(newActiveValue);
                setFormData((prevFormData) => ({
                  ...prevFormData,
                  active: newActiveValue,
                }));
              }}
            >
              {formData?.active ? "Deactivate" : "Active"}
            </Button>
          </InlineStack>
        </Card>

        {/* Past order */}
        <Card>
          <InlineStack
            blockAlign="center"
            align="space-between"
          >
            <Text
              as="h3"
              variant="headingSm"
            >
              Past order is {formData?.sentPastOrders ? "deactived" : "activated"}.
            </Text>
            <Button
              onClick={() => {
                const newSentPastOrdersValue = !sentPastOrders;
                setSentPastOrders(newSentPastOrdersValue);
                setFormData((prevFormData) => ({
                  ...prevFormData,
                  sentPastOrders: newSentPastOrdersValue,
                }));
              }}
            >
              {formData?.sentPastOrders ? "Active" : "Deactivate"}
            </Button>
          </InlineStack>
        </Card>

        {/* When to send email */}
        <Card>
          <BlockStack gap="400">
            <InlineGrid
              columns={["oneThird", "twoThirds"]}
              gap="300"
            >
              <Select
                label="When to send email"
                name="whenSend"
                value={formData?.whenToSend?.days}
                options={whenOptions}
                onChange={(value) => {
                  setFormData((prevFormData) => ({
                    ...prevFormData,
                    whenToSend: { ...prevFormData.whenToSend, days: value },
                  }));
                }}
              />
              <Select
                label="&nbsp;"
                value={formData?.whenToSend?.after}
                options={whenActionOptions}
                onChange={(value) => {
                  setFormData((prevFormData) => ({
                    ...prevFormData,
                    whenToSend: { ...prevFormData.whenToSend, after: value },
                  }));
                }}
              />
            </InlineGrid>

            {formData?.whenToSend?.after === "tagadded" && (
              <InlineGrid>
                <TextField
                  label="Tag"
                  value={formData?.whenToSend?.tag}
                  onChange={(value) => {
                    // Clear validation error when user starts typing
                    if (validationMessage?.tag) {
                      setValidationMessage((prev: any) => ({ ...prev, tag: null }));
                    }

                    // Update form data with new tag value
                    setFormData((prevFormData) => ({
                      ...prevFormData,
                      whenToSend: { ...prevFormData.whenToSend, tag: value },
                    }));
                  }}
                  placeholder="Enter a valid tag"
                  error={validationMessage?.tag}
                  autoComplete="off"
                />
              </InlineGrid>
            )}

            <BlockStack gap="100">
              <Text
                as="p"
                variant="bodySm"
              >
                On repeat orders
              </Text>
              <InlineStack
                blockAlign="center"
                gap="400"
              >
                <RadioButton
                  id="yes"
                  name="sendOnRepeat"
                  label="Ask again"
                  checked={formData?.sendOnRepeat === "yes"}
                  onChange={handleSendOnRepeat}
                />
                <RadioButton
                  id="no"
                  name="sendOnRepeat"
                  label="Don't ask again"
                  checked={formData?.sendOnRepeat === "no"}
                  onChange={handleSendOnRepeat}
                />
              </InlineStack>
              <Box paddingInlineStart="600">
                <Text
                  as="p"
                  variant="bodySm"
                  tone="subdued"
                >
                  If a review request is already sent for the product or store, the app will{" "}
                  {formData?.sendOnRepeat === "yes" ? "" : "not"} ask for another review.
                </Text>
              </Box>
            </BlockStack>
          </BlockStack>
        </Card>

        {/* Send 2nd Email */}
        <Card>
          <BlockStack gap="200">
            <Checkbox
              label={
                <InlineStack gap="100">
                  <Text as="p">Send a 2nd email asking for a review</Text>
                  {!isShopUnderProPlan && <ProPlanLink />}
                </InlineStack>
              }
              checked={formData?.secondEmail}
              disabled={!isShopUnderProPlan}
              onChange={(value) => {
                setFormData((prevFormData) => ({
                  ...prevFormData,
                  secondEmail: !!value,
                }));
              }}
            />
            {formData?.secondEmail && (
              <InlineGrid
                columns={2}
                gap="300"
              >
                <Select
                  label="When to send 2nd email"
                  labelHidden={true}
                  value={formData?.secondWhenToSend?.days}
                  disabled={!isShopUnderProPlan}
                  options={whenOptions}
                  // onChange={(value) => setSecondWhenOption(value)}
                  onChange={(value) => {
                    setFormData({ ...formData, secondWhenToSend: { ...formData.secondWhenToSend, days: value } });
                  }}
                />
                <Select
                  label="When to send 2nd email"
                  labelHidden={true}
                  value={formData?.secondWhenToSend?.after}
                  disabled={!isShopUnderProPlan}
                  options={secondWhenActionOptions}
                  onChange={(value) => {
                    setFormData({ ...formData, secondWhenToSend: { ...formData.secondWhenToSend, after: value } });
                  }}
                  // onChange={(value) => setSecondWhenAction(value)}
                />
              </InlineGrid>
            )}
          </BlockStack>
        </Card>

        {/* Online Stores Only & Specific Time Email */}
        <Card>
          <BlockStack gap="200">
            <Box>
              <Checkbox
                label={
                  <InlineStack gap="100">
                    <Text as="p">Send email for online store orders only.</Text>
                    {!isShopUnderProPlan && <ProPlanLink />}
                  </InlineStack>
                }
                checked={formData?.onlyOnlineStoreOrders}
                disabled={!isShopUnderProPlan}
                onChange={(value) => {
                  setFormData((prevFormData) => ({
                    ...prevFormData,
                    onlyOnlineStoreOrders: !!value,
                  }));
                }}
              />
            </Box>
            <Box>
              <Checkbox
                label={
                  <InlineStack gap="100">
                    <Text as="p">Send email at a specific time.</Text>
                    {!isShopUnderProPlan && <ProPlanLink />}
                  </InlineStack>
                }
                disabled={!isShopUnderProPlan}
                checked={formData?.specificTimeActive}
                onChange={(value) => {
                  setFormData({ ...formData, specificTimeActive: !!value });
                }}
                // onChange={(value) => setSpecificTimeActive(!!value)}
              />
              {formData?.specificTimeActive && (
                <Select
                  label=""
                  options={timeOptions}
                  disabled={!isShopUnderProPlan}
                  value={formData?.specificTime}
                  onChange={(value) => {
                    setFormData({ ...formData, specificTime: value });
                  }}
                  // onChange={(value) => setSpecificTime(value)}
                ></Select>
              )}
            </Box>
          </BlockStack>
        </Card>

        {/* Sender */}
        <Card>
          <BlockStack gap="200">
            <TextField
              label={
                <InlineStack
                  blockAlign="center"
                  gap="150"
                >
                  <Text as="p">Sender</Text>
                  {!isShopUnderProPlan ? (
                    <ProPlanLink />
                  ) : (
                    isValidDomainAndSender && (
                      <>
                        {isDomainVerified ? (
                          <Badge tone="success">Verified</Badge>
                        ) : (
                          <Badge tone="critical">Not Verified</Badge>
                        )}
                      </>
                    )
                  )}
                </InlineStack>
              }
              type="text"
              value={formData?.domain}
              onChange={(value) => {
                // Update both local state and formData
                setDomain(value || "trustsync.io");
                setFormData((prev) => ({
                  ...prev,
                  domain: value || "trustsync.io",
                }));
              }}
              autoComplete="off"
              placeholder="trustsync.io"
              disabled={!isShopUnderProPlan}
              prefix="@"
              error={domainErrorMessage}
              connectedLeft={
                <TextField
                  label=""
                  type="text"
                  value={formData.sender}
                  onChange={(value) => {
                    // Update both local state and formData
                    setSender(value || "hello");
                    setFormData((prev) => ({
                      ...prev,
                      sender: value || "hello",
                    }));
                  }}
                  placeholder="hello"
                  disabled={!isShopUnderProPlan}
                  autoComplete="off"
                />
              }
              connectedRight={
                <Button
                  disabled={
                    !isShopUnderProPlan ||
                    (formData?.domain || domain) === "" ||
                    (formData?.sender || sender) === "" ||
                    settings?.domain === (formData?.domain || domain)
                  }
                  onClick={saveCustomDomainInMailgun}
                  loading={isAddDomainInProgress}
                >
                  Add domain
                </Button>
              }
            />

            {(!isSenderDomainVerified || settings?.domain !== (formData?.domain || domain)) && (
              // <Banner>
              //   <Text
              //     as="p"
              //     variant="bodyMd"
              //   >
              //     As long as your domain is not verified, emails will be <NAME_EMAIL>
              //   </Text>
              // </Banner>
              <CardNote>
                <InlineStack gap="100">
                  <Box>
                    <Icon source={InfoIcon} />
                  </Box>
                  <Box>
                    <Text
                      as="p"
                      variant="bodyMd"
                    >
                      As long as your domain is not verified, emails will be <NAME_EMAIL>
                    </Text>
                  </Box>

                  {isValidDomainAndSender && !isDomainVerified && settings?.domain == (formData?.domain || domain) && (
                    <Button
                      variant="plain"
                      onClick={verifiedSenderCustomDomain}
                      loading={isVerifiedDomainInProgress}
                    >
                      Verify
                    </Button>
                  )}
                </InlineStack>
              </CardNote>
            )}
          </BlockStack>
        </Card>

        {/* Recive Emails */}
        <Card>
          <div onKeyUp={addReplyToEmailTag}>
            <TextField
              label="Receive Emails In"
              value={replyToEmailInput}
              onChange={(value) => {
                setReplyToEmailInput(value);
              }}
              clearButton
              onClearButtonClick={() => {
                setReplyToEmailInput("");
              }}
              helpText="Sometimes customers may reply to our review request email. You can choose where you prefer to receive those emails."
              placeholder="<EMAIL>"
              autoComplete="off"
              verticalContent={<InlineStack gap="200">{replyToEmailTagList}</InlineStack>}
              error={replyToErrorMessage}
            />
          </div>
        </Card>

        {/* BlackListed Email */}
        <Card>
          <div onKeyUp={addBlackListEmailTag}>
            <TextField
              label="Blacklisted emails"
              value={blackListedEmailInput}
              disabled={!isShopUnderProPlan}
              onChange={(value) => {
                setBlackListedEmailInput(value);
              }}
              clearButton
              onClearButtonClick={() => {
                setBlackListedEmailInput("");
              }}
              placeholder="<EMAIL>"
              autoComplete="off"
              verticalContent={<InlineStack gap="200">{blackistEmailTagList}</InlineStack>}
              error={blacklistErrorMessage}
            />
          </div>
        </Card>

        {/* Save Changes Button */}
        <InlineStack align="end">
          <Button
            variant="primary"
            onClick={saveGeneralEmailSetting}
            loading={isSaving}
            disabled={!hasUnsavedChanges}
          >
            Save changes
          </Button>
        </InlineStack>

        {openVerifiedModal && (
          <DomainVerificationModal
            domainName={formData?.domain || domain}
            domainRecord={domainRecord}
            openVerifiedModal={openVerifiedModal}
            setOpenVerifiedModal={setOpenVerifiedModal}
            mailgunDomainMessage={mailgunDomainMessage}
            setMailgunDomainMessage={setMailgunDomainMessage}
          />
        )}
      </BlockStack>

      <DiscardChangesModal />

      <ContextualSaveBar
        id="general-settings"
        open={hasUnsavedChanges}
        onSave={saveGeneralEmailSetting}
        isLoading={isSaving}
        onDiscard={showDiscardModal}
      />
    </Box>
  );
}
