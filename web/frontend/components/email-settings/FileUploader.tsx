import { Box, DropZone, InlineStack, Text, Thumbnail } from "@shopify/polaris";
import { ImageIcon } from "@shopify/polaris-icons";
import { useAppContext } from "../../providers/AppProvider";
import { validateImage } from "../../utils/helper";

export default function FileUploader({
  logo,
  setLogo,
  image,
  setImage,
}: {
  logo: string;
  setLogo: (logo: string) => void;
  image: File | null;
  setImage: (image: File) => void;
}) {
  const { shop } = useAppContext();
  const myShopifyDomain = shop?.shop || "";

  // const queryClient = useQueryClient();

  const handleDropZoneDrop = async (dropFiles: File[], acceptedFiles: File[], rejectedFiles: File[]) => {
    const isInValidImage = validateImage(acceptedFiles[0]);
    if (isInValidImage.length > 0) {
      shopify.toast.show(isInValidImage, {
        duration: 5000,
      });

      return;
    }

    setImage(acceptedFiles[0]);

    const { name: fileName } = acceptedFiles[0];
    const extension = fileName.split(".").pop();
    const timestamp = Date.now();
    const newFileName = `${myShopifyDomain}_${timestamp}.${extension}`;

    const formData = new FormData();
    formData.append("image", acceptedFiles[0], newFileName);

    try {
      const response = await fetch(`/api/v1/upload-logo`, {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Failed to upload file");
      }

      const { data } = await response.json();
      if (data?.logo) {
        setLogo(data?.logo);
      }

      shopify.toast.show("Logo uploaded", {
        duration: 5000,
      });
    } catch (error) {
      console.error("Error uploading file:", error);
    }
  };

  const validImageTypes = ["image/jpg", "image/jpeg", "image/png", "image/gif"];

  const fileUpload = !image && !logo && <DropZone.FileUpload actionHint="Accepts .jpg, .jpeg, .png, .gif" />;

  let uploadedFile = image && (
    <InlineStack gap="400">
      <Thumbnail
        size="large"
        alt={image.name}
        source={validImageTypes.includes(image.type) ? window.URL.createObjectURL(image) : ImageIcon}
      />
      <div style={{ padding: 10 }}>
        {image.name}{" "}
        <Text
          as="p"
          variant="bodySm"
        >
          {image.size} bytes
        </Text>
      </div>
    </InlineStack>
  );

  let oldPreviewFile = logo && (
    <InlineStack gap="400">
      <Thumbnail
        size="large"
        source={logo}
        alt="Logo preview"
      />
    </InlineStack>
  );

  return (
    <>
      <DropZone
        allowMultiple={false}
        onDrop={handleDropZoneDrop}
        label="Will appear at the top of emails you're sending"
        accept="image/*"
      >
        <Box padding="300">
          <InlineStack align="center">{image ? uploadedFile : oldPreviewFile}</InlineStack>
          <InlineStack align="center">{fileUpload}</InlineStack>
        </Box>
      </DropZone>
    </>
  );
}
