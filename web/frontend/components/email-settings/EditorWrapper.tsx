import { Box } from "@shopify/polaris";
import "quill/dist/quill.snow.css"; // Add css for snow theme
import React, { useMemo, useRef } from "react";
import ReactQuill, { Quill } from "react-quill-new";

// Define attributes for image formatting
const ImageFormatAttributesList = ["alt", "height", "width", "style"];
const BaseImageFormat = Quill.import("formats/image") as any;

// Extend Quill's image format to handle custom attributes
class ImageFormat extends BaseImageFormat {
  domNode: any;

  static formats(domNode: any) {
    return ImageFormatAttributesList.reduce((formats: any, attribute) => {
      if (domNode.hasAttribute(attribute)) {
        formats[attribute] = domNode.getAttribute(attribute);
      }
      return formats;
    }, {});
  }

  format(name: string, value: any) {
    if (ImageFormatAttributesList.indexOf(name) > -1) {
      if (value) {
        this.domNode.setAttribute(name, value);
      } else {
        this.domNode.removeAttribute(name);
      }
    } else {
      super.format(name, value);
    }
  }
}

// Register the custom image format with Quill
Quill.register(ImageFormat as any, true);

import QuillResizeImage from "quill-resize-image";
Quill.register("modules/resize", QuillResizeImage);

import { useAppContext } from "../../providers/AppProvider";
import { validateImage } from "../../utils/helper";

interface EditorWrapperProps {
  emailHTML: string;
  setEmailHTML: (html: string) => void;
}

export default function EditorWrapper({ emailHTML, setEmailHTML }: EditorWrapperProps) {
  const quillRef = useRef<ReactQuill>(null);
  const { shop } = useAppContext();
  const myShopifyDomain = shop?.shop;

  // Custom Image Handler
  const imageHandler = () => {
    const input = document.createElement("input");
    input.setAttribute("type", "file");
    input.setAttribute("accept", "image/*");
    input.click();

    input.onchange = async () => {
      const file = input.files?.[0];

      try {
        if (!file) {
          return;
        }
        const fileName = file.name;

        const isInValidImage = validateImage(file);
        if (isInValidImage.length > 0) {
          shopify.toast.show(isInValidImage, {
            duration: 5000,
          });

          return;
        }

        const extension = fileName.split(".").pop();
        const timestamp = Date.now();
        const newFileName = `${myShopifyDomain}_${timestamp}.${extension}`;

        const formData = new FormData();
        formData.append("image", file, newFileName);

        const response = await fetch(`/api/v1/upload-image`, {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          throw new Error("Failed to upload file");
        }

        const { data } = await response.json();
        if (data?.url) {
          const editor = quillRef.current?.getEditor();
          if (editor) {
            const range = editor.getSelection();
            if (range) {
              editor.insertEmbed(range.index, "image", data.url);
            }
          }

          shopify.toast.show("Image uploaded", {
            duration: 5000,
          });
        }
      } catch (error) {
        console.error("Error uploading file:", error);
      }
    };
  };

  // Quill Modules Configuration
  const modules = useMemo(
    () => ({
      toolbar: {
        handlers: { image: imageHandler },
        container: [
          [{ header: [1, 2, 3, false] }],
          ["bold", "italic", "underline"],
          [{ list: "ordered" }, { list: "bullet" }],
          ["link", "image"],
          ["clean"],
        ],
      },
      resize: {},
    }),
    []
  );

  return (
    <>
      <Box>
        <ReactQuill
          theme="snow"
          value={emailHTML}
          onChange={setEmailHTML}
          modules={modules}
        ></ReactQuill>
      </Box>

      {/* <Banner tone="info">
        <Text as="p">
          Please use <EditorTagCopy tag="[first.name]" />, <EditorTagCopy tag="[order.number]" />,{" "}
          <EditorTagCopy tag="[review.stars]" /> to customize your email.
        </Text>
      </Banner> */}
    </>
  );
}
