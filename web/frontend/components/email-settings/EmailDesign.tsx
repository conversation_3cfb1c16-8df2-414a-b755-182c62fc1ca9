import { lazy, Suspense, useEffect, useState } from "react";

import {
  BlockStack,
  Box,
  Button,
  Card,
  Icon,
  InlineGrid,
  InlineStack,
  Link,
  Select,
  Text,
  TextField,
} from "@shopify/polaris";
import { InfoIcon } from "@shopify/polaris-icons";

import ColorPickerWrapper from "../common/ColorPickerWrapper.jsx";
import ProPlanLink from "../common/ProPlanLink.jsx";
import FileUploader from "./FileUploader.jsx";
const EditorWrapper = lazy(() => import("./EditorWrapper.jsx"));

import { designOptions } from "../../utils/config.js";

import { useAppQuery, useMutation, useQueryClient } from "storeware-tanstack-query";
import queryKeys from "../../../app/enums/queryKeys";
import { sentTestEmail } from "../../apis/email.api";
import { fetchSettings, saveEmailDesignSettings } from "../../apis/settings.api";
import editorPrevieImage from "../../assets/img/preview/index.js";
import useUnsavedChanges from "../../hooks/useUnsavedChanges";
import { useAppContext } from "../../providers/AppProvider";
import { showNotification } from "../../utils/helper";
import CardNote from "../common/CardNote.jsx";
import ContextualSaveBar from "../common/ContextualSaveBar.jsx";
import EditorTagCopy from "../common/EditorTagCopy.jsx";
import SettingLoader from "../loader/SettingLoader.jsx";

interface EmailDesignFormData {
  from?: string;
  subject?: string;
  emailHTML?: string;
  designTemplate?: string;
  lowestText?: string;
  highestText?: string;
  logo?: string;
  logoHeight?: number;
  bgColor?: string;
  borderColor?: string;
  image?: any;
}

interface ValidationMessage {
  from?: string;
  subject?: string;
  designTemplate?: string;
  lowestText?: string;
  highestText?: string;
  logoHeight?: string;
  bgColor?: string;
  borderColor?: string;
  email?: string;
}

export default function EmailDesign() {
  const { isShopUnderProPlan } = useAppContext();
  const queryClient = useQueryClient();

  const [testEmail, setTestEmail] = useState("");
  const [previewEmailHTML, setPreviewEmailHTML] = useState("");

  const [formData, setFormData] = useState<EmailDesignFormData>({});
  const [originalFormData, setOriginalFormData] = useState<EmailDesignFormData>({});
  const [validationMessage, setValidationMessage] = useState<ValidationMessage>({});
  // const isFirstEditorChange = useRef(true);

  const { hasUnsavedChanges, DiscardChangesModal, showDiscardModal } = useUnsavedChanges({
    originalData: originalFormData,
    currentData: formData,
    onDiscardAction: () => {
      setFormData(originalFormData);
    },
  });

  const { data: { shop, settings } = {}, isLoading } = useAppQuery({
    queryKey: [queryKeys.FETCH_SETTING],
    queryFn: fetchSettings,
    reactQueryOptions: {
      enabled: true,
      staleTime: 0,
    },
  });

  const { mutate: handleSaveEmailDesign, isPending: isSaving } = useMutation({
    mutationFn: () => saveEmailDesignSettings(formData),
    onSuccess: (response) => {
      if (response?.status === 422) {
        setValidationMessage(response.errors);
      } else if (response?.status === 200) {
        queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_SETTING] });

        showNotification({ message: "Settings updated" });
      }
    },

    onError: (err) => {
      console.log("error updating email design", err);
    },
  });

  const { mutate: handleSendTestEmail, isPending: isSendingTestEmail } = useMutation({
    mutationFn: () => sentTestEmail({ email: testEmail }),
    onSuccess: (response) => {
      if (response?.status === 422) {
        setValidationMessage(response.errors);
      } else if (response?.status === 200) {
        showNotification({ message: "Email send" });
      }
    },
    onError: (err) => {
      console.log("Error sending test email:", err);
    },
  });

  useEffect(() => {
    if (settings) {
      // Reset the first editor change flag when settings change
      // isFirstEditorChange.current = true;

      const newFormData = {
        from: settings?.from || "",
        subject: settings?.subject || "",
        emailHTML: settings?.emailHTML || "",
        designTemplate: settings?.designTemplate || "boxedGray",
        lowestText: settings?.lowestText || "very dissatisfied",
        highestText: settings?.highestText || "very satisfied",
        logo: settings?.logo || "",
        logoHeight: settings?.logoHeight || 100,
        bgColor: settings?.bgColor || "#EAF4FF",
        borderColor: settings?.borderColor || "#3996d9",
      };

      // Set formData
      setFormData(newFormData);
      setOriginalFormData(newFormData);

      setPreviewEmailHTML(settings?.emailHTML || "");
    }

    if (shop) {
      setTestEmail(shop?.info?.email || "");
    }
  }, [settings, shop]);

  useEffect(() => {
    let prevEmailTemp = '<div class="editor-email-section">';

    if (formData?.logo) {
      prevEmailTemp += `<div class="editor-preview-logo"><img src='${formData?.logo}' class="editor-preview-logo-image"/></div><br/>`;
    }

    if (formData?.emailHTML) {
      prevEmailTemp += formData?.emailHTML.replaceAll(
        "[review.stars]",
        `<br/><div>
      <img src='${editorPrevieImage[formData?.designTemplate as keyof typeof editorPrevieImage]}' class="editor-preview-rating-image" alt="Editor Preview Image" /></div>`
      );
    }

    if (!shop?.verified) {
      prevEmailTemp += `
      <br/>
      <div class="powered-by-section">
        <div class="editor-preview-powered-logo">
          Powered by <img src="${editorPrevieImage["poweredByLogo"]}" alt="poweredByLogo"/> <a href="https://trustsync.io" target="_blank">TrustSync</a>
        </div>
      </div>`;
    }
    prevEmailTemp += "</div>";

    setPreviewEmailHTML(prevEmailTemp);
  }, [shop, settings, formData?.emailHTML, formData?.designTemplate, formData?.logo]);

  const handleClick = () => {
    if (window.$crisp) {
      window.$crisp.do("chat:open");
      window.$crisp.push([
        "do",
        "message:send",
        [
          "text",
          "Hi, I’d like to verify my store to remove the TrustSync branding from email and double my email limit. Can you please help me with this?",
        ],
      ]);
    }
  };

  if (isLoading) {
    return <SettingLoader />;
  }

  return (
    <Box
      paddingBlockStart="400"
      paddingBlockEnd="1000"
    >
      <InlineGrid
        columns={2}
        gap="400"
        alignItems="start"
      >
        {/* Email Design Section */}
        <BlockStack gap="400">
          <Card>
            <BlockStack gap="400">
              {/* From */}
              <TextField
                label="From"
                value={formData?.from}
                onChange={(value) => {
                  setFormData((prevFormData) => ({ ...prevFormData, from: value }));
                }}
                placeholder={formData?.from}
                error={validationMessage?.from}
                autoComplete="off"
              />

              {/* Subject */}
              <TextField
                label="Subject"
                value={formData?.subject}
                onChange={(value) => {
                  setFormData((prevFormData) => ({ ...prevFormData, subject: value }));
                }}
                placeholder="We need 10 seconds of your time"
                error={validationMessage?.subject}
                autoComplete="off"
              />

              {/* Email HTML*/}
              <Suspense fallback={<h2>Loading...</h2>}>
                <EditorWrapper
                  emailHTML={formData?.emailHTML as string}
                  setEmailHTML={(value) => {
                    setFormData((prevFormData) => ({ ...prevFormData, emailHTML: value }));
                  }}
                />
              </Suspense>

              {/* Editor Tag Copy */}
              <Box>
                <InlineStack
                  gap="100"
                  align="start"
                  blockAlign="start"
                  wrap={false}
                >
                  <Box>
                    <Icon source={InfoIcon} />
                  </Box>

                  <Box>
                    Please use <EditorTagCopy tag="[first.name]" />, <EditorTagCopy tag="[order.number]" />,{" "}
                    <EditorTagCopy tag="[review.stars]" /> to customize your email.
                  </Box>
                </InlineStack>
              </Box>
            </BlockStack>
          </Card>

          <Card>
            <BlockStack gap="400">
              {/* Design */}
              <Select
                options={designOptions}
                label="Design"
                onChange={(value) => {
                  setFormData((prevFormData) => ({ ...prevFormData, designTemplate: value }));
                }}
                value={formData?.designTemplate}
                error={validationMessage?.designTemplate}
              ></Select>

              {/* Lowest Rating */}
              <TextField
                label="Lowest rating text"
                value={formData?.lowestText}
                onChange={(value) => {
                  setFormData((prevFormData) => ({ ...prevFormData, lowestText: value }));
                }}
                placeholder="very dissatisfied"
                helpText="Lowest rating text will be shown in live emails and not in the preview."
                error={validationMessage?.lowestText}
                autoComplete="off"
              />

              {/* Highest Rating */}
              <TextField
                label="Highest rating text"
                value={formData?.highestText}
                onChange={(value) => {
                  setFormData((prevFormData) => ({ ...prevFormData, highestText: value }));
                }}
                placeholder="Very satisfied"
                helpText="Highest rating text will be shown in live emails and not in the preview."
                error={validationMessage?.highestText}
                autoComplete="off"
              />
            </BlockStack>
          </Card>

          <Card>
            {/* File Uploader */}
            <BlockStack gap="400">
              <FileUploader
                logo={formData?.logo || ""}
                setLogo={(value: string | undefined) => {
                  setFormData((prevFormData) => ({ ...prevFormData, logo: value }));
                }}
                image={formData?.image || null}
                setImage={(value: File | null) => {
                  setFormData((prevFormData) => ({ ...prevFormData, image: value }));
                }}
              />
              {/* Logo Height */}
              <TextField
                label="Size (height) of your logo"
                value={String(formData?.logoHeight)}
                onChange={(value) => {
                  setFormData((prevFormData) => ({
                    ...prevFormData,
                    logoHeight: Number(value) > 100 ? 100 : Number(value),
                  }));
                }}
                max="100"
                type="number"
                error={validationMessage?.logoHeight}
                autoComplete="off"
              />
            </BlockStack>
          </Card>

          <Card>
            <BlockStack gap="100">
              <InlineGrid
                columns={2}
                gap="400"
              >
                {/* Email Background color  */}
                <TextField
                  label="Email background color "
                  type="text"
                  placeholder="#000000"
                  value={formData?.bgColor}
                  disabled={!isShopUnderProPlan}
                  suffix={
                    <ColorPickerWrapper
                      color={formData?.bgColor as string}
                      setColor={(value) => {
                        setFormData((prevFormData) => ({ ...prevFormData, bgColor: value }));
                      }}
                      disabled={!isShopUnderProPlan}
                    />
                  }
                  error={validationMessage?.bgColor}
                  autoComplete="off"
                />
                {/* Top Border color */}
                <TextField
                  label="Top border color"
                  type="text"
                  placeholder="#000000"
                  value={formData?.borderColor}
                  disabled={!isShopUnderProPlan}
                  suffix={
                    <ColorPickerWrapper
                      color={formData?.borderColor as string}
                      setColor={(value) => {
                        setFormData((prevFormData) => ({ ...prevFormData, borderColor: value }));
                      }}
                      disabled={!isShopUnderProPlan}
                    />
                  }
                  error={validationMessage?.borderColor}
                  autoComplete="off"
                />
              </InlineGrid>
              {!isShopUnderProPlan && (
                <Box>
                  <ProPlanLink />
                </Box>
              )}
            </BlockStack>
          </Card>

          {/* Save Changes button */}
          <InlineStack>
            <Button
              variant="primary"
              onClick={handleSaveEmailDesign}
              loading={isSaving}
              disabled={!hasUnsavedChanges}
            >
              Save changes
            </Button>
          </InlineStack>
        </BlockStack>

        {/* Email Preview Section */}
        <div style={{ position: "sticky", top: 16 }}>
          <BlockStack gap="400">
            <Card>
              <BlockStack gap="300">
                <Text
                  as="h4"
                  variant="headingMd"
                >
                  Email preview
                </Text>

                <TextField
                  label=""
                  disabled
                  prefix={<Text as="p">From:</Text>}
                  value={formData?.from}
                  autoComplete="off"
                />

                <TextField
                  label=""
                  disabled
                  prefix={<Text as="p">Subject:</Text>}
                  value={formData?.subject}
                  autoComplete="off"
                />

                <div
                  style={{
                    padding: "16px",
                    borderRadius: "12px",
                    background: `${formData?.bgColor}`,
                  }}
                >
                  <div
                    style={{
                      padding: 12,
                      background: "#fff",
                      borderTop: `8px solid ${formData?.borderColor}`,
                    }}
                  >
                    <BlockStack gap="400">
                      <div dangerouslySetInnerHTML={{ __html: previewEmailHTML }}></div>
                    </BlockStack>
                  </div>
                </div>
                <CardNote>
                  <InlineStack gap="100">
                    <Box>
                      <Icon source={InfoIcon} />
                    </Box>
                    <Box>
                      <Text as="p">
                        To remove credits for FREE, please{" "}
                        <Link
                          monochrome
                          onClick={() => handleClick()}
                        >
                          contact support
                        </Link>
                      </Text>
                    </Box>
                  </InlineStack>
                </CardNote>
              </BlockStack>
            </Card>

            <Card>
              <BlockStack gap="300">
                <Text
                  as="h4"
                  variant="headingMd"
                >
                  Send test email
                </Text>

                <TextField
                  label=""
                  prefix={<Text as="p">To:</Text>}
                  value={testEmail}
                  onChange={(value) => {
                    setTestEmail(value);
                  }}
                  placeholder="<EMAIL>"
                  error={validationMessage?.email}
                  autoComplete="off"
                />

                <InlineStack>
                  <Button
                    onClick={handleSendTestEmail}
                    loading={isSendingTestEmail}
                  >
                    Send test email
                  </Button>
                </InlineStack>
              </BlockStack>
            </Card>
          </BlockStack>
        </div>
      </InlineGrid>

      <DiscardChangesModal />

      <ContextualSaveBar
        id="email-design"
        open={hasUnsavedChanges}
        onSave={handleSaveEmailDesign}
        isLoading={isSaving}
        onDiscard={showDiscardModal}
      />
    </Box>
  );
}
