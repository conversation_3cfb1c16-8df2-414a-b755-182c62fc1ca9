import { Bad<PERSON>, Bleed, <PERSON>, Card, Divider, InlineStack, Text } from "@shopify/polaris";
import DomainTextCopy from "../common/DomainTextCopy";

function DnsRecordItem({ record }: { record: any }) {
  return (
    <Card>
      <InlineStack>
        <Box minWidth="150px">
          <Text
            as="h4"
            variant="headingSm"
          >
            Record type
          </Text>
        </Box>
        <Text as="p">{record.record_type}</Text>
      </InlineStack>
      <Bleed marginInline="400">
        <Box paddingBlock="300">
          <Divider />
        </Box>
      </Bleed>
      <InlineStack>
        <Box minWidth="150px">
          <Text
            as="h4"
            variant="headingSm"
          >
            Record name
          </Text>
        </Box>
        <div style={{ wordBreak: "break-all", flex: 1 }}>
          <DomainTextCopy text={record.record_name}></DomainTextCopy>
        </div>
      </InlineStack>

      <Bleed marginInline="400">
        <Box paddingBlock="300">
          <Divider />
        </Box>
      </Bleed>

      <InlineStack wrap={false}>
        <Box minWidth="150px">
          <Text
            as="h4"
            variant="headingSm"
          >
            Record value
          </Text>
        </Box>
        <div style={{ wordBreak: "break-all", flex: 1 }}>
          <DomainTextCopy text={record.value}></DomainTextCopy>
        </div>
      </InlineStack>
      <Bleed marginInline="400">
        <Box paddingBlock="300">
          <Divider />
        </Box>
      </Bleed>
      <InlineStack>
        <Box minWidth="150px">
          <Text
            as="h4"
            variant="headingSm"
          >
            Status
          </Text>
        </Box>
        {record.valid === "valid" ? (
          <Badge
            tone="success"
            aria-label="Verified"
          >
            Verified
          </Badge>
        ) : (
          <Badge
            tone="warning"
            aria-label="Not Verified"
          >
            Not Verified
          </Badge>
        )}
      </InlineStack>
    </Card>
  );
}

export default DnsRecordItem;
