import { Button, <PERSON>con, InlineStack, <PERSON>, Text, Tooltip } from "@shopify/polaris";
import { ClipboardIcon } from "@shopify/polaris-icons";
import React from "react";

interface DomainTextCopyProps {
  text: string;
}

function DomainTextCopy({ text }: DomainTextCopyProps): React.ReactElement {
  const handleDomainTextCopy = (): void => {
    navigator.clipboard.writeText(text);

    (window as any).shopify.toast.show("Copied", { duration: 1000 });
  };

  return (
    <>
      <InlineStack
        wrap={false}
        gap="025"
        align="space-between"
        blockAlign="start"
      >
        <Text
          fontWeight="semibold"
          as="span"
        >
          {text}
        </Text>
        <Tooltip content="Click to copy">
          <Button
            icon={ClipboardIcon}
            variant="monochromePlain"
            onClick={handleDomainTextCopy}
          />
        </Tooltip>
      </InlineStack>
    </>
  );
}

export default DomainTextCopy;
