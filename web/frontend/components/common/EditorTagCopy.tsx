import { Button, Text, Tooltip } from "@shopify/polaris";
import React from "react";

interface EditorTagCopyProps {
  tag: string;
}

function EditorTagCopy({ tag }: EditorTagCopyProps): React.ReactElement {
  const handleEditorTagCopy = (): void => {
    navigator.clipboard.writeText(tag);

    (window as any).shopify.toast.show("Copied", { duration: 1000 });
  };

  return (
    <>
      <Tooltip content="Click to copy">
        <Button
          id="tag-copy"
          variant="monochromePlain"
          onClick={handleEditorTagCopy}
        >
          {tag}
        </Button>
      </Tooltip>
    </>
  );
}

export default EditorTagCopy;
