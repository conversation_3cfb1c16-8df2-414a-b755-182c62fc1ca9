import { Bleed, Box } from "@shopify/polaris";
import React from "react";

interface CardNoteProps {
  children: React.ReactNode;
}

export default function CardNote({ children }: CardNoteProps): React.ReactElement {
  return (
    <Bleed
      marginBlockEnd="400"
      marginInline="400"
    >
      <Box
        background="bg-surface-secondary"
        padding="400"
      >
        {children}
      </Box>
    </Bleed>
  );
}
