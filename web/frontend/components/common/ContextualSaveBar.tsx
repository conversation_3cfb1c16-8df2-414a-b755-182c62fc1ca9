import { SaveBar } from "@shopify/app-bridge-react";
import { useEffect } from "react";
import { useBlocker } from "react-router-dom";

export default function ContextualSaveBar({
  open,
  onSave,
  onDiscard,
  isLoading,
  disabled = false,
  ...props
}: {
  open: boolean;
  onSave: () => void;
  onDiscard: () => void;
  isLoading: boolean;
  disabled?: boolean;
  [key: string]: any;
}) {
  const blocker = useBlocker(
    ({ currentLocation, nextLocation }) => open && currentLocation.pathname !== nextLocation.pathname
  );

  // Leave confirmation
  useEffect(() => {
    (async () => {
      if (blocker.state === "blocked") {
        await shopify.saveBar.leaveConfirmation();
      }
    })();
  }, [blocker]);

  return (
    <SaveBar
      open={open}
      {...props}
    >
      {/* Discard action */}
      <button
        onClick={onDiscard}
        disabled={isLoading ? true : false}
      ></button>
      {/* Save action */}
      <button
        variant="primary"
        loading={isLoading ? "" : false}
        onClick={onSave}
        disabled={disabled}
      ></button>
    </SaveBar>
  );
}
