import { Crisp } from "crisp-sdk-web";
import React, { useEffect } from "react";
import { useAppContext } from "../../providers/AppProvider";

// Declare global $crisp variable
declare global {
  interface Window {
    $crisp: any;
  }
}

export default function CrispChat(): null {
  const { shop, subscription } = useAppContext();

  useEffect(() => {
    // Delay Crisp configuration by 1 seconds
    const timeout = setTimeout(() => {
      Crisp.configure("57c11e5d-c42e-4758-a784-9a30be2fac1a");

      if (shop) {
        window.$crisp.push(["set", "user:nickname", [shop.shop]]);
        window.$crisp.push(["set", "session:data", ["shop", shop.shop]]);
      }

      if (subscription) {
        window.$crisp.push(["set", "session:data", ["plan", subscription.plan]]);
      }

      if (shop) {
        window.$crisp.push(["set", "session:data", ["verified", shop.verified ? "Yes" : "No"]]);

        window.$crisp.push([
          "set",
          "session:data",
          ["am_item", `https://app.adminmate.io/review/production/data/shops/all/item/${shop._id}/details`],
        ]);
      }
    }, 1000);

    // Cleanup function to clear the timeout if the component unmounts
    return () => clearTimeout(timeout);
  }, [shop, subscription]);

  return null;
}
