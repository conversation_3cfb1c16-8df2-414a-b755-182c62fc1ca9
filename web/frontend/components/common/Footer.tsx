import { Link, Text } from "@shopify/polaris";
import React, { lazy, Suspense, useEffect, useState } from "react";
const CrispChat = lazy(() => import("../common/CrispChat.jsx"));

import { FooterLogo } from "./SvgIcons";

export default function Footer(): React.ReactElement {
  const [load3rdParty, setLoad3rdParty] = useState(false);
  const currentYear = new Date().getFullYear();

  useEffect(() => {
    setTimeout(() => {
      setLoad3rdParty(true);

      (function (c: Window & { [key: string]: any }, l: Document, a: string, r: string, i: string) {
        c[a] =
          c[a] ||
          function () {
            (c[a].q = c[a].q || []).push(arguments);
          };
        const t = l.createElement(r) as HTMLScriptElement;
        t.async = true;
        t.src = "https://www.clarity.ms/tag/" + i;
        const y = l.getElementsByTagName(r)[0] as HTMLElement;
        if (y && y.parentNode) {
          y.parentNode.insertBefore(t, y);
        }
      })(window, document, "clarity", "script", "q1g8vg6n4i");
    }, 3500);
  }, []);

  return (
    <>
      <footer className="footer">
        <div className="footer__logo">
          <Link
            url="https://trustsync.io"
            target="_blank"
          >
            <FooterLogo />
          </Link>
        </div>
        <Text
          as="p"
          tone="subdued"
        >
          Copyright © {currentYear} |{" "}
          <Link
            removeUnderline
            monochrome
            url="https://trustsync.io"
            target={"_blank"}
          >
            <Text
              as="strong"
              tone="subdued"
            >
              TrustSync
            </Text>
          </Link>{" "}
          | Version{" "}
          <Link
            removeUnderline
            monochrome
            url="https://trustsync.io/changelog"
            target={"_blank"}
          >
            <Text
              as="strong"
              tone="subdued"
            >
              {process.env.VITE_APP_VERSION}
            </Text>
          </Link>
        </Text>
        <div
          className="footer-language d__flex align__center"
          style={{ zIndex: 99, width: "100px" }}
        ></div>
      </footer>

      <Suspense fallback={null}>{load3rdParty && <CrispChat />}</Suspense>
    </>
  );
}
