import {
  <PERSON><PERSON><PERSON>ck,
  Box,
  <PERSON>ton,
  DatePicker,
  Icon,
  InlineGrid,
  InlineStack,
  OptionList,
  Popover,
  Scrollable,
  Select,
  TextField,
  useBreakpoints,
} from "@shopify/polaris";
import { ArrowRightIcon, CalendarIcon } from "@shopify/polaris-icons";
import React, { useEffect, useRef, useState } from "react";

interface DateRange {
  title: string;
  alias: string;
  period: {
    since: Date;
    until: Date;
  };
}

interface DateRangePickerProps {
  handlePayloadUpdate: (payload: any) => void;
}

export const DateRangePicker = ({ handlePayloadUpdate }: DateRangePickerProps): React.ReactElement => {
  const { mdDown, lgUp } = useBreakpoints();
  const shouldShowMultiMonth = lgUp;

  const today = new Date(new Date().setHours(0, 0, 0, 0));

  const yesterday = new Date(new Date(new Date().setDate(today.getDate() - 1)).setHours(0, 0, 0, 0));

  const ranges = [
    {
      title: "Today",
      alias: "today",
      period: {
        since: today,
        until: today,
      },
    },
    {
      title: "Yesterday",
      alias: "yesterday",
      period: {
        since: yesterday,
        until: yesterday,
      },
    },
    {
      title: "Last 7 days",
      alias: "last7days",
      period: {
        since: new Date(new Date(new Date().setDate(today.getDate() - 7)).setHours(0, 0, 0, 0)),
        until: today,
      },
    },
    {
      title: "Last 15 days",
      alias: "last15days",
      period: {
        since: new Date(new Date(new Date().setDate(today.getDate() - 15)).setHours(0, 0, 0, 0)),
        until: today,
      },
    },
    {
      title: "Last 30 days",
      alias: "last30days",
      period: {
        since: new Date(new Date(new Date().setDate(today.getDate() - 30)).setHours(0, 0, 0, 0)),
        until: today,
      },
    },
    {
      title: "Last 60 days",
      alias: "last60days",
      period: {
        since: new Date(new Date(new Date().setDate(today.getDate() - 60)).setHours(0, 0, 0, 0)),
        until: today,
      },
    },
    {
      title: "Last 90 days",
      alias: "last90days",
      period: {
        since: new Date(new Date(new Date().setDate(today.getDate() - 90)).setHours(0, 0, 0, 0)),
        until: today,
      },
    },
    {
      title: "Last 180 days",
      alias: "last180days",
      period: {
        since: new Date(new Date(new Date().setDate(today.getDate() - 180)).setHours(0, 0, 0, 0)),
        until: today,
      },
    },
    {
      title: "Last 365 days",
      alias: "last365days",
      period: {
        since: new Date(new Date(new Date().setDate(today.getDate() - 365)).setHours(0, 0, 0, 0)),
        until: today,
      },
    },

    {
      title: "All Times",
      alias: "allTimes",
      period: {
        since: new Date(new Date(new Date().setDate(today.getDate() - 5000)).setHours(0, 0, 0, 0)),
        until: today,
      },
    },
  ];

  const [popoverActive, setPopoverActive] = useState<boolean>(false);
  const [activeDateRange, setActiveDateRange] = useState<DateRange>(ranges[4]);
  const [inputValues, setInputValues] = useState<any>({});
  const [{ month, year }, setDate] = useState<{ month: number; year: number }>({
    month: activeDateRange.period.since.getMonth(),
    year: activeDateRange.period.since.getFullYear(),
  });

  const datePickerRef = useRef<HTMLDivElement>(null);
  const VALID_YYYY_MM_DD_DATE_REGEX = /^\d{4}-\d{1,2}-\d{1,2}/;

  function isDate(date: any): boolean {
    return !isNaN(new Date(date).getDate());
  }

  function isValidYearMonthDayDateString(date: string): boolean {
    return VALID_YYYY_MM_DD_DATE_REGEX.test(date) && isDate(date);
  }

  function isValidDate(date: string): boolean {
    return date.length === 10 && isValidYearMonthDayDateString(date);
  }

  function parseYearMonthDayDateString(input: string) {
    // Date-only strings (e.g. "1970-01-01") are treated as UTC, not local time
    // when using new Date()
    // We need to split year, month, day to pass into new Date() separately
    // to get a localized Date
    const [year, month, day] = input.split("-");
    return new Date(Number(year), Number(month) - 1, Number(day));
  }

  function formatDateToYearMonthDayDateString(date: Date) {
    const year = String(date.getFullYear());
    let month = String(date.getMonth() + 1);
    let day = String(date.getDate());
    if (month.length < 2) {
      month = String(month).padStart(2, "0");
    }
    if (day.length < 2) {
      day = String(day).padStart(2, "0");
    }
    return [year, month, day].join("-");
  }

  function formatDate(date: Date) {
    return formatDateToYearMonthDayDateString(date);
  }

  function nodeContainsDescendant(rootNode: Node, descendant: Node) {
    if (rootNode === descendant) {
      return true;
    }
    let parent = descendant.parentNode;
    while (parent != null) {
      if (parent === rootNode) {
        return true;
      }
      parent = parent.parentNode;
    }
    return false;
  }

  function isNodeWithinPopover(node: Node) {
    return datePickerRef?.current ? nodeContainsDescendant(datePickerRef.current, node) : false;
  }

  function handleStartInputValueChange(value: string) {
    setInputValues((prevState: any) => {
      return { ...prevState, since: value };
    });

    if (isValidDate(value)) {
      const newSince = parseYearMonthDayDateString(value);
      setActiveDateRange((prevState) => {
        const newPeriod =
          prevState.period && newSince <= prevState.period.until
            ? { since: newSince, until: prevState.period.until }
            : { since: newSince, until: newSince };
        return {
          ...prevState,
          period: newPeriod,
        };
      });
    }
  }

  function handleEndInputValueChange(value: string) {
    setInputValues((prevState: any) => ({ ...prevState, until: value }));
    if (isValidDate(value)) {
      const newUntil = parseYearMonthDayDateString(value);
      setActiveDateRange((prevState: any) => {
        const newPeriod =
          prevState.period && newUntil >= prevState.period.since
            ? { since: prevState.period.since, until: newUntil }
            : { since: newUntil, until: newUntil };
        return {
          ...prevState,
          period: newPeriod,
        };
      });
    }
  }

  function handleInputBlur(event: React.FocusEvent) {
    const isRelatedTargetWithinPopover = event.relatedTarget != null && isNodeWithinPopover(event.relatedTarget);
    // If focus moves from the TextField to the Popover
    // we don't want to close the popover
    if (isRelatedTargetWithinPopover) {
      return;
    }
    setPopoverActive(false);
  }

  function handleMonthChange(month: number, year: number) {
    setDate({ month, year });
  }

  function handleCalendarChange({ start, end }: { start: Date; end: Date }) {
    const newDateRange = ranges.find((range) => {
      return range.period.since.valueOf() === start.valueOf() && range.period.until.valueOf() === end.valueOf();
    }) || {
      alias: "custom",
      title: "Custom",
      period: {
        since: start,
        until: end,
      },
    };
    setActiveDateRange(newDateRange);
  }

  const applyFilter = () => {
    setPopoverActive(false);
    handlePayloadUpdate({ ...inputValues });
  };

  const cancelFilter = () => {
    setActiveDateRange(ranges[4]);

    handlePayloadUpdate({
      since: formatDate(new Date(new Date(new Date().setDate(today.getDate() - 30)).setHours(0, 0, 0, 0))),
      until: formatDate(today),
    });

    setPopoverActive(false);
  };

  useEffect(() => {
    if (activeDateRange) {
      setInputValues({
        since: formatDate(activeDateRange.period.since),
        until: formatDate(activeDateRange.period.until),
      });
      function monthDiff(referenceDate: { year: number; month: number }, newDate: { year: number; month: number }) {
        return newDate.month - referenceDate.month + 12 * (referenceDate.year - newDate.year);
      }
      const monthDifference = monthDiff(
        { year, month },
        {
          year: activeDateRange.period.until.getFullYear(),
          month: activeDateRange.period.until.getMonth(),
        }
      );
      if (monthDifference > 1 || monthDifference < 0) {
        setDate({
          month: activeDateRange.period.until.getMonth(),
          year: activeDateRange.period.until.getFullYear(),
        });
      }
    }
  }, [activeDateRange]);

  const buttonValue =
    activeDateRange.title === "Custom"
      ? activeDateRange.period.since.toDateString() + " - " + activeDateRange.period.until.toDateString()
      : activeDateRange.title;

  return (
    <Popover
      active={popoverActive}
      autofocusTarget="none"
      preferredAlignment="left"
      preferredPosition="below"
      fluidContent
      sectioned={false}
      fullHeight
      activator={
        <Button
          size="slim"
          icon={CalendarIcon}
          onClick={() => setPopoverActive(!popoverActive)}
        >
          {buttonValue}
        </Button>
      }
      onClose={() => {
        // handlePayloadUpdate({ ...inputValues });
        setPopoverActive(false);
      }}
    >
      <Popover.Pane fixed>
        <InlineGrid
          columns={{
            xs: "1fr",
            md: "max-content max-content",
          }}
          gap="0"
          // ref={datePickerRef}
        >
          <Box
            maxWidth={mdDown ? "516px" : "212px"}
            width={mdDown ? "100%" : "212px"}
            padding={{ xs: "500", md: "0" }}
            paddingBlockEnd={{ xs: "100", md: "0" }}
          >
            {mdDown ? (
              <Select
                label="dateRangeLabel"
                labelHidden
                onChange={(value) => {
                  const result = ranges.find(({ title, alias }) => title === value || alias === value);
                  setActiveDateRange(result || ranges[4]);
                }}
                value={activeDateRange?.title || activeDateRange?.alias || ""}
                options={ranges.map(({ alias, title }) => title || alias)}
              />
            ) : (
              <Scrollable style={{ height: "334px" }}>
                <OptionList
                  options={ranges.map((range) => ({
                    value: range.alias,
                    label: range.title,
                  }))}
                  selected={[activeDateRange.alias]}
                  onChange={(value) => {
                    console.log(ranges.find((range) => range.alias === value[0]));
                    setActiveDateRange(ranges.find((range) => range.alias === value[0]) || ranges[4]);
                  }}
                />
              </Scrollable>
            )}
          </Box>
          <Box
            padding={{ xs: "500" }}
            maxWidth={mdDown ? "320px" : "516px"}
          >
            <BlockStack gap="400">
              <InlineStack gap="200">
                <div style={{ flexGrow: 1 }}>
                  <TextField
                    role="combobox"
                    label={"Since"}
                    labelHidden
                    prefix={<Icon source={CalendarIcon} />}
                    value={inputValues.since}
                    onChange={handleStartInputValueChange}
                    onBlur={handleInputBlur}
                    autoComplete="off"
                  />
                </div>
                <Icon source={ArrowRightIcon} />
                <div style={{ flexGrow: 1 }}>
                  <TextField
                    role="combobox"
                    label={"Until"}
                    labelHidden
                    prefix={<Icon source={CalendarIcon} />}
                    value={inputValues.until}
                    onChange={handleEndInputValueChange}
                    onBlur={handleInputBlur}
                    autoComplete="off"
                  />
                </div>
              </InlineStack>
              <div>
                <DatePicker
                  month={month}
                  year={year}
                  selected={{
                    start: activeDateRange.period.since,
                    end: activeDateRange.period.until,
                  }}
                  onMonthChange={handleMonthChange}
                  onChange={handleCalendarChange}
                  multiMonth={shouldShowMultiMonth}
                  allowRange
                />
              </div>
            </BlockStack>
          </Box>
        </InlineGrid>
      </Popover.Pane>
      <Popover.Pane fixed>
        <Popover.Section>
          <Box paddingBlockEnd="200">
            <InlineStack
              align="end"
              gap="200"
            >
              <Button onClick={cancelFilter}>Cancel</Button>
              <Button
                variant="primary"
                onClick={applyFilter}
              >
                Apply
              </Button>
            </InlineStack>
          </Box>
        </Popover.Section>
      </Popover.Pane>
    </Popover>
  );
};
