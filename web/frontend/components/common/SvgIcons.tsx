import React from "react";

export const TutorialSvg = (): React.ReactElement => {
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="40"
        height="40"
        rx="20"
        fill="#0094D5"
      />
      <path
        d="M13.7499 13.7396H26.3107C26.542 13.7396 26.7674 13.7717 26.9861 13.825C26.7432 13.0596 26.0349 12.5 25.1899 12.5H14.8082C13.9582 12.5 13.2461 13.0667 13.0078 13.8392C13.2466 13.775 13.4953 13.7396 13.7499 13.7396Z"
        fill="white"
      />
      <path
        d="M12.5006 15.8332H27.5006C27.7514 15.8332 27.9931 15.8712 28.2214 15.9403C28.1852 15.8341 28.1431 15.7295 28.0897 15.6307C27.7352 14.9782 27.0543 14.5732 26.3122 14.5732H13.7514C13.0072 14.5732 12.3252 14.9803 11.9714 15.6357C11.9218 15.7278 11.8822 15.8245 11.8477 15.9232C12.056 15.8666 12.2743 15.8337 12.5006 15.8337V15.8332Z"
        fill="white"
      />
      <path
        d="M27.4987 16.6665H12.4987C11.5783 16.6665 10.832 17.4128 10.832 18.3332V26.2498C10.832 27.1703 11.5783 27.9165 12.4987 27.9165H27.4987C28.4191 27.9165 29.1654 27.1703 29.1654 26.2498V18.3332C29.1654 17.4128 28.4191 16.6665 27.4987 16.6665ZM22.8795 23.374L19.1295 25.539C18.9337 25.6519 18.7191 25.7086 18.5045 25.7086C18.2899 25.7086 18.0749 25.6523 17.8795 25.539C17.4883 25.3132 17.2545 24.9086 17.2545 24.4565V20.1265C17.2545 19.6744 17.4883 19.2698 17.8795 19.044C18.2708 18.8182 18.7383 18.8182 19.1295 19.044L22.8795 21.209C23.2708 21.4348 23.5045 21.8398 23.5045 22.2915C23.5045 22.7432 23.2708 23.1482 22.8795 23.374Z"
        fill="white"
      />
    </svg>
  );
};

export const DocumentationSvg = (): React.ReactElement => {
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="40"
        height="40"
        rx="20"
        fill="#29845A"
      />
      <path
        d="M22.8906 15.8594C22.1368 15.8594 21.5234 15.2461 21.5234 14.4922V10H14.5312C13.3466 10 12.3828 10.9638 12.3828 12.1484V27.8516C12.3828 29.0362 13.3466 30 14.5312 30H25.4688C26.6534 30 27.6172 29.0362 27.6172 27.8516V15.8594H22.8906ZM15.5859 24.0625H18.4266C18.7502 24.0625 19.0125 24.3248 19.0125 24.6484C19.0125 24.972 18.7502 25.2344 18.4266 25.2344H15.5859C15.2623 25.2344 15 24.972 15 24.6484C15 24.3248 15.2623 24.0625 15.5859 24.0625ZM15 21.5234C15 21.1998 15.2623 20.9375 15.5859 20.9375H24.1797C24.5033 20.9375 24.7656 21.1998 24.7656 21.5234C24.7656 21.847 24.5033 22.1094 24.1797 22.1094H15.5859C15.2623 22.1094 15 21.847 15 21.5234ZM24.1797 17.8125C24.5033 17.8125 24.7656 18.0748 24.7656 18.3984C24.7656 18.722 24.5033 18.9844 24.1797 18.9844H15.5859C15.2623 18.9844 15 18.722 15 18.3984C15 18.0748 15.2623 17.8125 15.5859 17.8125H24.1797Z"
        fill="white"
      />
      <path
        d="M22.6953 14.4923C22.6953 14.6 22.7829 14.6877 22.8906 14.6877H27.356C27.2488 14.4898 27.1109 14.308 26.9453 14.1512L23.1788 10.5879C23.0326 10.4496 22.8698 10.3343 22.6954 10.2432V14.4923H22.6953Z"
        fill="white"
      />
    </svg>
  );
};

export const SupportSvg = (): React.ReactElement => {
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="40"
        height="40"
        rx="20"
        fill="#998A00"
      />
      <path
        d="M29.6783 20.7281C29.7092 21.4665 29.4459 22.187 28.9463 22.7316C28.4466 23.2761 27.7514 23.6002 27.0131 23.6328C26.9699 23.6348 26.9268 23.6358 26.8838 23.6358C26.6876 23.6357 26.492 23.6144 26.3004 23.5725C25.7318 24.4504 24.9862 25.2 24.1113 25.7733C23.2364 26.3465 22.2515 26.7309 21.2196 26.9018C21.0976 27.2603 20.8541 27.565 20.5312 27.7631C20.2084 27.9612 19.8264 28.0402 19.4515 27.9866C19.0765 27.933 18.7321 27.75 18.4777 27.4693C18.2234 27.1887 18.075 26.828 18.0584 26.4496C18.0418 26.0712 18.1579 25.6988 18.3866 25.397C18.6154 25.0951 18.9424 24.8826 19.3112 24.7963C19.68 24.7099 20.0674 24.7552 20.4064 24.9242C20.7454 25.0931 21.0147 25.3753 21.1677 25.7217C22.6205 25.453 23.933 24.6831 24.8764 23.546C25.8197 22.409 26.3342 20.9769 26.33 19.4995C26.33 16.0091 23.4904 13.1694 20 13.1694C16.5095 13.1694 13.6699 16.0091 13.6699 19.4995C13.6697 20.5788 13.9455 21.6402 14.4712 22.5829C14.489 22.614 14.5024 22.6474 14.5109 22.6822C14.5617 22.8234 14.5563 22.9786 14.4957 23.1158C14.4352 23.2531 14.3243 23.3618 14.1859 23.4195C13.8469 23.5621 13.4828 23.6356 13.1151 23.6355C13.0718 23.6355 13.0284 23.6345 12.985 23.6325C12.247 23.5995 11.5521 23.2752 11.0529 22.7307C10.5536 22.1862 10.2907 21.4659 10.3216 20.7278C10.3341 20.4335 10.3272 20.1709 10.3204 19.9169C10.314 19.6778 10.3074 19.4306 10.3183 19.1726C10.3507 18.4519 10.6607 17.7717 11.1834 17.2745C11.7061 16.7772 12.4009 16.5015 13.1223 16.5051C14.2807 13.8546 16.9273 11.9976 20 11.9976C23.0726 11.9976 25.7193 13.8546 26.8777 16.5051C27.5992 16.501 28.2942 16.7765 28.817 17.2737C29.3397 17.771 29.6496 18.4514 29.6816 19.1721C29.6925 19.4305 29.6859 19.6778 29.6795 19.9169C29.6728 20.1708 29.6658 20.4336 29.6783 20.7281ZM24.9142 19.4995C24.9139 20.2524 24.7407 20.9951 24.408 21.6705C24.0752 22.3458 23.5918 22.9357 22.995 23.3947C22.3982 23.8536 21.704 24.1694 20.9659 24.3176C20.2277 24.4658 19.4654 24.4425 18.7377 24.2495L16.7486 25.3982C16.6482 25.4562 16.5329 25.4831 16.4173 25.4755C16.3016 25.4679 16.1908 25.4262 16.0989 25.3557C16.0069 25.2851 15.938 25.1889 15.9007 25.0791C15.8635 24.9694 15.8596 24.8511 15.8896 24.7391L16.398 22.8427C15.5561 21.9327 15.0877 20.7392 15.0859 19.4995C15.0859 16.789 17.2902 14.5841 20 14.5841C22.7097 14.5841 24.9142 16.789 24.9142 19.4995ZM18.6338 19.4995C18.6338 19.3441 18.572 19.1951 18.4621 19.0852C18.3523 18.9753 18.2032 18.9136 18.0478 18.9136H18.0468C17.931 18.9138 17.8178 18.9483 17.7216 19.0129C17.6254 19.0774 17.5505 19.169 17.5063 19.276C17.4621 19.3831 17.4507 19.5009 17.4734 19.6145C17.4961 19.7281 17.552 19.8324 17.634 19.9142C17.716 19.9961 17.8204 20.0517 17.934 20.0743C18.0477 20.0968 18.1654 20.0851 18.2724 20.0407C18.3794 19.9964 18.4709 19.9213 18.5352 19.8249C18.5995 19.7286 18.6339 19.6154 18.6339 19.4995H18.6338ZM20.586 19.4995C20.586 19.4803 20.5849 19.4612 20.5829 19.4421C20.5811 19.4229 20.5782 19.4039 20.5743 19.3851C20.5708 19.3663 20.5661 19.3478 20.5602 19.3296C20.5548 19.3112 20.5482 19.2933 20.5411 19.2753C20.5341 19.2573 20.5255 19.2405 20.5165 19.2237C20.5076 19.2068 20.4978 19.1904 20.4872 19.1745C20.4766 19.1584 20.4651 19.1429 20.4528 19.128C20.4408 19.1131 20.4279 19.0989 20.4142 19.0854C20.4009 19.0718 20.3865 19.0589 20.3716 19.0464C20.3567 19.0347 20.3411 19.0229 20.3251 19.0124C20.3092 19.0018 20.2928 18.992 20.2759 18.9831C20.2591 18.9741 20.2415 18.9659 20.2239 18.9585C20.2063 18.9511 20.1883 18.9447 20.17 18.9394C20.1335 18.9276 20.0958 18.92 20.0575 18.9167C20.0002 18.9112 19.9423 18.9139 19.8857 18.9249C19.8668 18.9287 19.8482 18.9336 19.8299 18.9394C19.8115 18.9447 19.7935 18.9511 19.7759 18.9585C19.7584 18.9659 19.7408 18.9741 19.724 18.9831C19.7072 18.9921 19.6908 19.0019 19.6747 19.0124C19.6587 19.0229 19.6431 19.0347 19.6287 19.0464C19.6134 19.0589 19.5994 19.0718 19.5857 19.0854C19.572 19.0989 19.5591 19.1131 19.547 19.128C19.5349 19.1429 19.5236 19.1585 19.5131 19.1745C19.5023 19.1904 19.4924 19.2068 19.4834 19.2237C19.4744 19.2405 19.4663 19.2577 19.4591 19.2753C19.4517 19.2931 19.4452 19.3112 19.4396 19.3296C19.4341 19.3476 19.4295 19.3663 19.4256 19.3851C19.4217 19.4039 19.4188 19.4229 19.4169 19.4421C19.415 19.4612 19.4142 19.4804 19.4142 19.4995C19.4142 19.5187 19.415 19.5382 19.4169 19.5573C19.4188 19.5763 19.4217 19.5952 19.4256 19.614C19.4295 19.6327 19.4341 19.6515 19.4396 19.6698C19.4452 19.6881 19.4517 19.7061 19.4591 19.7237C19.4663 19.7415 19.4744 19.7588 19.4834 19.7757C19.4923 19.7925 19.5025 19.8089 19.5131 19.8249C19.5236 19.8408 19.5349 19.8562 19.547 19.871C19.5592 19.886 19.5721 19.9003 19.5857 19.914C19.5994 19.9272 19.6134 19.9405 19.6287 19.9526C19.6434 19.9648 19.6587 19.9762 19.6747 19.9866C19.6908 19.9972 19.7072 20.0073 19.724 20.0163C19.7579 20.034 19.7933 20.0486 19.8299 20.0601C19.8482 20.0655 19.8669 20.0702 19.8857 20.0741C19.9233 20.0818 19.9617 20.0856 20.0001 20.0854C20.1553 20.085 20.3041 20.0234 20.4142 19.914C20.4412 19.8866 20.4656 19.8567 20.4872 19.8249C20.4978 19.8089 20.5075 19.7925 20.5165 19.7757C20.5255 19.7589 20.5337 19.7413 20.5411 19.7237C20.5486 19.7062 20.5548 19.6878 20.5602 19.6698C20.5661 19.6515 20.5708 19.6329 20.5743 19.614C20.5782 19.5952 20.581 19.5763 20.5829 19.5573C20.5849 19.5381 20.5859 19.5188 20.5859 19.4994L20.586 19.4995ZM22.5391 19.4995C22.5391 19.3441 22.4774 19.1951 22.3675 19.0852C22.2576 18.9753 22.1086 18.9136 21.9532 18.9136H21.9521C21.8362 18.9138 21.7231 18.9484 21.6269 19.0129C21.5307 19.0774 21.4557 19.169 21.4116 19.2761C21.3674 19.3832 21.3559 19.5009 21.3787 19.6145C21.4014 19.7281 21.4573 19.8324 21.5393 19.9142C21.6213 19.9961 21.7257 20.0518 21.8393 20.0743C21.9529 20.0968 22.0707 20.0851 22.1777 20.0407C22.2847 19.9964 22.3761 19.9213 22.4405 19.8249C22.5048 19.7286 22.5391 19.6154 22.5391 19.4995Z"
        fill="white"
      />
    </svg>
  );
};

export const FeatureSvg = (): React.ReactElement => {
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="40"
        height="40"
        rx="20"
        fill="#8051FF"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M25 18.75H23.75C22.7144 18.75 21.875 19.5894 21.875 20.625V28.125H26.25C27.2856 28.125 28.125 27.2856 28.125 26.25C28.125 24.7225 28.125 22.1525 28.125 20.625C28.125 19.5894 27.2856 18.75 26.25 18.75V12.5C26.25 12.155 25.97 11.875 25.625 11.875H21.3331C21.0662 11.875 20.8263 12.0356 20.7238 12.2819C20.6219 12.5288 20.6781 12.8119 20.8669 13.0006L22.2413 14.375L20.8669 15.7494C20.6781 15.9381 20.6219 16.2213 20.7238 16.4681C20.8263 16.7144 21.0662 16.875 21.3331 16.875H25V18.75ZM20.625 20.625H18.75C18.2525 20.625 17.7756 20.8225 17.4244 21.1744C17.0725 21.5256 16.875 22.0025 16.875 22.5V28.125H20.625V20.625ZM15.625 22.5H13.75C12.7144 22.5 11.875 23.3394 11.875 24.375V26.25C11.875 27.2856 12.7144 28.125 13.75 28.125H15.625V22.5Z"
        fill="white"
      />
    </svg>
  );
};

export const CheckIcon = (): React.ReactElement => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_2371_59230)">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10ZM15.2738 7.94051C15.6644 7.54998 15.6644 6.91682 15.2738 6.52629C14.8833 6.13577 14.2502 6.13577 13.8596 6.52629L8.7334 11.6525L6.77384 9.69296C6.38331 9.30243 5.75015 9.30243 5.35962 9.69296C4.9691 10.0835 4.9691 10.7166 5.35962 11.1072L8.02629 13.7738C8.41682 14.1644 9.04998 14.1644 9.44051 13.7738L15.2738 7.94051Z"
        fill="#1A1A1A"
      />
    </g>
    <defs>
      <clipPath id="clip0_2371_59230">
        <rect
          width="20"
          height="20"
          fill="white"
        />
      </clipPath>
    </defs>
  </svg>
);

export const StarIcon = (): React.ReactElement => (
  <svg
    width="28"
    height="27"
    viewBox="0 0 28 27"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.2562 2.24562C15.3506 0.347816 12.6492 0.347813 11.7436 2.24562L8.96455 8.06964L2.56679 8.91299C0.48203 9.1878 -0.352752 11.757 1.17233 13.2047L5.85252 17.6475L4.67757 23.9927C4.29471 26.0604 6.48018 27.6482 8.32832 26.6452L13.9999 23.5669L19.6715 26.6452C21.5197 27.6482 23.7051 26.0604 23.3223 23.9927L22.1473 17.6475L26.8275 13.2047C28.3526 11.757 27.5178 9.1878 25.4331 8.91299L19.0353 8.06964L16.2562 2.24562Z"
      fill="#FFB800"
    />
  </svg>
);

export const EmptyStateIcon = (): React.ReactElement => (
  <svg
    width="156"
    height="148"
    viewBox="0 0 156 148"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_605_15553)">
      <path
        d="M148.5 74C148.5 84.99 146.1 95.43 141.79 104.81C130.11 130.3 104.37 148 74.5 148C44.63 148 18.89 130.3 7.21 104.81C2.9 95.43 0.5 84.99 0.5 74C0.5 33.13 33.63 0 74.5 0C115.37 0 148.5 33.13 148.5 74Z"
        fill="#F0F1F2"
      />
      <path
        d="M63.0813 18.4697H108.261V88.8797H36.5312V45.0297C36.5312 30.3697 48.4313 18.4797 63.0813 18.4797V18.4697Z"
        fill="#CD5747"
      />
      <path
        d="M155.447 42.2502C155.447 29.1102 144.797 18.4702 131.667 18.4702H60.6172V44.3902H155.447V42.2502Z"
        fill="#399C97"
      />
      <path
        d="M155.451 44.3901H58.0312V88.8701H155.451V44.3901Z"
        fill="#208B84"
      />
      <path
        d="M8.81375 85.7202H155.454V88.8802H8.81375C8.00375 88.8802 7.34375 88.2202 7.34375 87.4102V87.1902C7.34375 86.3802 8.00375 85.7202 8.81375 85.7202Z"
        fill="#59B2AC"
      />
      <mask
        id="mask0_605_15553"
        style={{ maskType: "luminance" }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="149"
        height="148"
      >
        <path
          d="M148.5 74C148.5 84.99 146.1 95.43 141.79 104.81C130.11 130.3 104.37 148 74.5 148C44.63 148 18.89 130.3 7.21 104.81C2.9 95.43 0.5 84.99 0.5 74C0.5 33.13 33.63 0 74.5 0C115.37 0 148.5 33.13 148.5 74Z"
          fill="white"
        />
      </mask>
      <g mask="url(#mask0_605_15553)">
        <path
          d="M65.8013 18.47H105.551C107.051 18.47 108.261 19.69 108.261 21.18V86.16C108.261 87.66 107.041 88.87 105.551 88.87H39.2413C37.7413 88.87 36.5312 87.65 36.5312 86.16V47.73C36.5312 31.58 49.6413 18.46 65.8013 18.46V18.47Z"
          fill="#CD5747"
        />
        <path
          d="M161.671 42.2497C161.671 29.1097 151.021 18.4697 137.891 18.4697H61.1406V44.3897H161.671V42.2497Z"
          fill="#399C97"
        />
        <path
          d="M155.451 44.3901H58.0312V88.8701H155.451V44.3901Z"
          fill="#208B84"
        />
        <path
          d="M100.456 124.91H101.076V148.49H92.0156V133.35C92.0156 128.69 95.7956 124.91 100.456 124.91Z"
          fill="#05614E"
        />
        <path
          d="M38.0312 87.3797V42.7297C38.0312 30.1797 48.2413 19.9697 60.7913 19.9697C73.3413 19.9697 83.5513 30.1797 83.5513 42.7297V87.3797H38.0312Z"
          fill="#42474B"
        />
        <path
          d="M60.7913 21.4697C72.5113 21.4697 82.0513 31.0097 82.0513 42.7297V85.8797H39.5312V42.7297C39.5312 31.0097 49.0713 21.4697 60.7913 21.4697ZM60.7913 18.4697C47.3913 18.4697 36.5312 29.3297 36.5312 42.7297V88.8797H85.0513V42.7297C85.0513 29.3297 74.1913 18.4697 60.7913 18.4697Z"
          fill="#59B2AC"
        />
        <path
          d="M95.1209 88.29H82.7109V164.56H95.1209V88.29Z"
          fill="#D9942E"
        />
        <path
          d="M95.1347 88.29H85.0547V164.56H95.1347V88.29Z"
          fill="#E9AF58"
        />
        <path
          d="M8.81375 85.7197H155.454V88.8797H8.81375C8.00375 88.8797 7.34375 88.2197 7.34375 87.4097V87.1897C7.34375 86.3797 8.00375 85.7197 8.81375 85.7197Z"
          fill="#59B2AC"
        />
        <path
          d="M87.0837 148.49H79.8838V134.2H80.3838C84.0838 134.2 87.0938 137.21 87.0938 140.91V148.49H87.0837Z"
          fill="#EC856F"
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_605_15553">
        <rect
          width="155"
          height="148"
          fill="white"
          transform="translate(0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const FooterLogo = (): React.ReactElement => (
  <svg
    width="123"
    height="26"
    viewBox="0 0 123 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_974_58797)">
      <path
        d="M45.4023 19.3556V10.4379H47.6318V11.4442H47.9558C48.0874 11.0848 48.3067 10.8205 48.6125 10.6535C48.9183 10.4854 49.2752 10.4014 49.6821 10.4014H50.7615V12.4152H49.6468C49.0718 12.4152 48.5979 12.5674 48.2263 12.8732C47.8547 13.179 47.6683 13.6493 47.6683 14.2852V19.3556H45.4023Z"
        fill="#AFAFAF"
      />
      <path
        d="M55.9227 19.4989C55.2271 19.4989 54.6192 19.3406 54.0978 19.0226C53.5764 18.7046 53.1719 18.2648 52.8844 17.7008C52.5969 17.138 52.4531 16.4898 52.4531 15.7589V10.4375H54.7191V15.5798C54.7191 16.2511 54.8836 16.7542 55.2137 17.0904C55.5439 17.4267 56.0141 17.5936 56.6257 17.5936C57.3213 17.5936 57.8598 17.3633 58.2435 16.9016C58.6273 16.4399 58.8186 15.7954 58.8186 14.9682V10.4375H61.0845V19.3552H58.8551V18.1869H58.531C58.3873 18.4866 58.1181 18.7802 57.7221 19.0677C57.3262 19.3552 56.7268 19.4989 55.924 19.4989H55.9227Z"
        fill="#AFAFAF"
      />
      <path
        d="M67.1481 19.6078C65.9858 19.6078 65.0319 19.3557 64.29 18.8525C63.5469 18.3494 63.0973 17.6294 62.9414 16.695L65.0271 16.1553C65.1111 16.5744 65.2512 16.9045 65.4498 17.1445C65.6472 17.3845 65.8933 17.5551 66.1869 17.6574C66.4805 17.7597 66.8009 17.8097 67.1493 17.8097C67.6768 17.8097 68.0666 17.7171 68.3176 17.5307C68.5686 17.3455 68.6953 17.1141 68.6953 16.8387C68.6953 16.5634 68.5759 16.3502 68.3359 16.2004C68.0959 16.0505 67.7121 15.9275 67.1858 15.8312L66.6827 15.7411C66.0589 15.6217 65.49 15.456 64.9747 15.2465C64.4594 15.0369 64.0452 14.7458 63.7345 14.3742C63.4226 14.0026 63.2667 13.5226 63.2667 12.9354C63.2667 12.0485 63.5907 11.3687 64.2376 10.8948C64.8858 10.4209 65.7361 10.1846 66.7911 10.1846C67.7852 10.1846 68.6136 10.4063 69.2715 10.8497C69.9306 11.2932 70.3619 11.8743 70.5665 12.5943L68.4626 13.2412C68.3663 12.7856 68.1714 12.4615 67.8778 12.2702C67.5842 12.079 67.2212 11.9827 66.7899 11.9827C66.3586 11.9827 66.0285 12.0583 65.8007 12.2081C65.5729 12.358 65.4583 12.5651 65.4583 12.8282C65.4583 13.1157 65.5777 13.3289 65.8177 13.4666C66.0577 13.6042 66.3806 13.709 66.7887 13.7809L67.2918 13.871C67.9631 13.9904 68.571 14.15 69.1168 14.3474C69.6614 14.5447 70.0938 14.8274 70.4118 15.1929C70.7298 15.5583 70.8881 16.0529 70.8881 16.6767C70.8881 17.6111 70.5495 18.3335 69.8721 18.8428C69.1948 19.352 68.2871 19.6066 67.1481 19.6066V19.6078Z"
        fill="#AFAFAF"
      />
      <path
        d="M76.2809 19.3558C75.6937 19.3558 75.2173 19.173 74.8519 18.8075C74.4864 18.4421 74.3036 17.9535 74.3036 17.342V12.3081H72.0742V10.4381H74.3036V7.66895H76.5684V10.4381H79.0135V12.3081H76.5684V16.9472C76.5684 17.3066 76.7365 17.4869 77.0715 17.4869H78.7978V19.357H76.2809V19.3558Z"
        fill="#AFAFAF"
      />
      <path
        d="M85.359 19.6076C84.388 19.6076 83.5316 19.4334 82.7885 19.0862C82.0453 18.739 81.4642 18.2407 81.0439 17.5938C80.6248 16.9469 80.4141 16.1672 80.4141 15.2559V14.7528H82.7507V15.2559C82.7507 16.0113 82.9846 16.5778 83.4524 16.9554C83.9202 17.3331 84.5549 17.5219 85.3578 17.5219C86.1606 17.5219 86.7807 17.3599 87.1827 17.037C87.5835 16.713 87.7846 16.3 87.7846 15.7969C87.7846 15.4496 87.6859 15.167 87.4885 14.9514C87.2899 14.7357 87.0024 14.5591 86.6248 14.4214C86.2471 14.2838 85.789 14.1546 85.2494 14.0352L84.8364 13.9451C83.9726 13.7538 83.2331 13.5102 82.6155 13.2166C81.9978 12.923 81.5251 12.5368 81.195 12.0568C80.866 11.5768 80.7004 10.9543 80.7004 10.1867C80.7004 9.41924 80.8831 8.76382 81.2486 8.21803C81.614 7.67225 82.1294 7.25317 82.7945 6.95957C83.4597 6.66596 84.2418 6.51855 85.1409 6.51855C86.04 6.51855 86.8404 6.67084 87.5409 6.97662C88.2426 7.28241 88.7933 7.73804 89.1953 8.34351C89.5973 8.94899 89.7983 9.70675 89.7983 10.618V11.1577H87.4605V10.618C87.4605 10.138 87.3679 9.75183 87.1827 9.45823C86.9963 9.16462 86.7308 8.94899 86.3823 8.81133C86.0339 8.67366 85.6209 8.60422 85.1421 8.60422C84.4222 8.60422 83.8922 8.73945 83.5511 9.00869C83.21 9.27792 83.0382 9.64706 83.0382 10.1149C83.0382 10.4267 83.1198 10.6899 83.2819 10.9055C83.4439 11.1212 83.6863 11.3015 84.0104 11.4452C84.3332 11.589 84.7474 11.7145 85.2506 11.8229L85.6648 11.913C86.5639 12.1043 87.346 12.3504 88.0112 12.6501C88.6763 12.9498 89.1941 13.3457 89.5669 13.8367C89.9384 14.3276 90.1248 14.9575 90.1248 15.725C90.1248 16.4925 89.9299 17.1662 89.5401 17.7473C89.1502 18.3284 88.5984 18.784 87.8857 19.1142C87.173 19.4443 86.33 19.6088 85.3602 19.6088L85.359 19.6076Z"
        fill="#AFAFAF"
      />
      <path
        d="M92.9283 22.9515V20.9743H97.7831C98.1194 20.9743 98.2863 20.794 98.2863 20.4346V18.1869H97.9634C97.8672 18.3903 97.7173 18.595 97.5139 18.7984C97.3104 19.0019 97.0339 19.17 96.6867 19.3016C96.3395 19.4332 95.896 19.499 95.3563 19.499C94.6607 19.499 94.0528 19.3406 93.5314 19.0226C93.01 18.7046 92.6055 18.2648 92.318 17.7008C92.0305 17.138 91.8867 16.4898 91.8867 15.7589V10.4375H94.1527V15.5798C94.1527 16.2511 94.3172 16.7542 94.6473 17.0904C94.9762 17.4267 95.4477 17.5936 96.0593 17.5936C96.7549 17.5936 97.2934 17.3633 97.6771 16.9016C98.0609 16.4399 98.2521 15.7954 98.2521 14.9682V10.4375H100.517V20.9377C100.517 21.5493 100.337 22.0378 99.9772 22.4033C99.6178 22.7688 99.1378 22.9515 98.5384 22.9515H92.9283Z"
        fill="#AFAFAF"
      />
      <path
        d="M102.723 19.3556V10.4379H104.952V11.6063H105.276C105.42 11.2944 105.69 10.9983 106.085 10.7157C106.481 10.4343 107.08 10.293 107.883 10.293C108.579 10.293 109.187 10.4513 109.708 10.7693C110.23 11.0873 110.634 11.5246 110.922 12.0814C111.209 12.6393 111.353 13.2887 111.353 14.0318V19.3532H109.088V14.2109C109.088 13.5396 108.924 13.0365 108.593 12.7003C108.265 12.3652 107.793 12.1971 107.181 12.1971C106.486 12.1971 105.947 12.4274 105.564 12.8891C105.18 13.3508 104.989 13.9953 104.989 14.8225V19.3532H102.723V19.3556Z"
        fill="#AFAFAF"
      />
      <path
        d="M117.676 19.6078C116.812 19.6078 116.031 19.4275 115.329 19.0682C114.627 18.7088 114.073 18.1873 113.666 17.5039C113.258 16.8205 113.055 15.9933 113.055 15.0223V14.7701C113.055 13.7992 113.258 12.972 113.666 12.2885C114.074 11.6051 114.629 11.0836 115.329 10.7243C116.031 10.3649 116.813 10.1846 117.676 10.1846C118.538 10.1846 119.258 10.3344 119.868 10.6341C120.48 10.9338 120.975 11.3444 121.352 11.8658C121.73 12.3872 121.978 12.978 122.099 13.6371L119.905 14.1049C119.857 13.7456 119.749 13.4215 119.582 13.134C119.414 12.8465 119.178 12.6187 118.872 12.4505C118.566 12.2824 118.186 12.1984 117.73 12.1984C117.275 12.1984 116.864 12.297 116.499 12.4956C116.133 12.693 115.845 12.9866 115.635 13.3764C115.425 13.7663 115.321 14.2426 115.321 14.8054V14.9857C115.321 15.5486 115.425 16.0261 115.635 16.4148C115.845 16.8046 116.132 17.0982 116.499 17.2956C116.864 17.4929 117.275 17.5928 117.73 17.5928C118.414 17.5928 118.932 17.4162 119.286 17.0629C119.639 16.7096 119.865 16.2442 119.961 15.6692L122.154 16.1906C121.998 16.8253 121.731 17.404 121.354 17.9254C120.976 18.4468 120.481 18.8574 119.87 19.1571C119.258 19.4568 118.527 19.6066 117.677 19.6066H117.676V19.6078Z"
        fill="#AFAFAF"
      />
      <path
        d="M34.625 6.76953V8.92708H36.1356C37.3283 8.92708 38.2932 7.96221 38.2932 6.76953H34.625Z"
        fill="#AFAFAF"
      />
      <path
        d="M38.293 6.76953V19.3554H40.6661V8.92708H44.3343V6.76953H38.293Z"
        fill="#AFAFAF"
      />
      <path
        d="M20.4691 12.7764L15.9372 4.72247C15.1843 3.3836 13.8174 2.57467 12.2812 2.55762H12.2325C10.7157 2.55762 9.35369 3.33365 8.57888 4.64085L4.19922 12.0369L5.00449 13.321L6.57727 13.3051L10.896 6.01261C11.309 5.31577 11.9791 5.24754 12.2519 5.24998C12.5212 5.25242 13.1937 5.33648 13.5908 6.04307L18.1228 14.097C18.3701 14.5356 18.8269 14.7829 19.2972 14.7829C19.5213 14.7829 19.7479 14.7269 19.9562 14.6099C20.6044 14.2456 20.8334 13.4245 20.4691 12.7764Z"
        fill="#AFAFAF"
      />
      <path
        d="M3.95125 17.0381L5.82494 17.7897L5.18048 15.8746L6.47549 14.3262L4.45805 14.3469L3.38476 12.6377L2.7805 14.5638L0.820312 15.0559L2.46619 16.2267L2.32974 18.2417L3.95125 17.0381Z"
        fill="#AFAFAF"
      />
      <path
        d="M19.0779 21.7503L10.5573 21.715C9.74596 21.7126 9.34637 21.1656 9.21236 20.9316C9.07835 20.6965 8.81034 20.0752 9.21845 19.3747L13.8722 11.3902C14.2474 10.7482 14.0294 9.92341 13.3874 9.54941C12.7453 9.1754 11.9206 9.39225 11.5466 10.0343L6.89279 18.0188C6.12041 19.3467 6.1131 20.9353 6.87573 22.2681C7.63837 23.6009 9.01013 24.4013 10.5464 24.4074L19.1997 24.4427L19.8941 23.1014L19.0779 21.7503Z"
        fill="#AFAFAF"
      />
      <path
        d="M22.0987 24.4086L23.4948 25.8206L23.6642 23.8421L25.4392 22.9528L23.6093 22.1804L23.3121 20.2178L22.011 21.7187L20.0508 21.3946L21.0778 23.0953L20.1641 24.8594L22.0975 24.4086H22.0987Z"
        fill="#AFAFAF"
      />
      <path
        d="M26.5757 12.7094L22.1546 5.11719L20.6988 5.32064L20.0385 6.83372L24.2488 14.0641C24.657 14.7646 24.3889 15.3859 24.2549 15.6198C24.1209 15.855 23.7213 16.4008 22.91 16.4032L13.6694 16.4373C12.9263 16.4397 12.3257 17.0452 12.3281 17.7884C12.3306 18.5303 12.9336 19.1297 13.6743 19.1297H13.6731L13.678 19.1309L22.9197 19.0968C24.456 19.0907 25.8289 18.2915 26.5916 16.9587C27.3542 15.6247 27.3481 14.0373 26.5757 12.7094Z"
        fill="#AFAFAF"
      />
      <path
        d="M18.2356 4.55917L19.1432 6.42433L19.9729 4.52384L22.0256 4.23633L20.4748 2.8609L20.8378 0.820312L19.0494 1.87046L17.2184 0.895845L17.6642 2.9206L16.1719 4.35937L18.2356 4.56038V4.55917Z"
        fill="#AFAFAF"
      />
    </g>
    <defs>
      <clipPath id="clip0_974_58797">
        <rect
          width="121.333"
          height="25"
          fill="white"
          transform="translate(0.820312 0.820312)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const VerifyBadgeIcon = (): React.ReactElement => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.1876 2.47107C12.2394 2.4876 12.2845 2.52012 12.3166 2.56393C12.3487 2.60774 12.366 2.6606 12.3661 2.7149V5.3392C12.3661 12.3999 7.60405 13.6407 7.05151 13.7611C7.01723 13.7686 6.98173 13.7686 6.94745 13.7611C6.39491 13.6407 1.63281 12.3999 1.63281 5.3392V2.7149C1.63295 2.6606 1.6503 2.60774 1.68238 2.56393C1.71445 2.52012 1.75959 2.4876 1.81131 2.47107C4.77021 1.5074 6.40985 0.557734 6.86648 0.2719C6.90628 0.246749 6.9524 0.233398 6.99948 0.233398C7.04656 0.233398 7.09268 0.246749 7.13248 0.2719C7.58911 0.5568 9.22875 1.5074 12.1876 2.47107Z"
      fill="#0F8060"
    ></path>
    <path
      d="M7.11511 12.5343C7.04058 12.559 6.96005 12.559 6.88551 12.5343C5.84018 12.1924 2.80078 10.7329 2.80078 5.33898V3.62724C2.8012 3.55171 2.82497 3.47816 2.86883 3.41667C2.91269 3.35518 2.9745 3.30876 3.04578 3.28378C4.34784 2.83625 5.61274 2.28716 6.82881 1.64158C6.88151 1.61276 6.9406 1.59766 7.00066 1.59766C7.06072 1.59766 7.11982 1.61276 7.17251 1.64158C8.38866 2.28719 9.65364 2.83627 10.9558 3.28378C11.0271 3.30876 11.0889 3.35518 11.1327 3.41667C11.1766 3.47816 11.2004 3.55171 11.2008 3.62724V5.33898C11.2008 10.7306 8.16511 12.1908 7.11511 12.5343Z"
      fill="#0F8060"
    ></path>
    <path
      d="M5.75683 8.96205L4.56147 7.72539C4.47863 7.63949 4.43273 7.52456 4.43361 7.40523C4.43448 7.28591 4.48205 7.17166 4.56614 7.08699L4.87997 6.77175C4.92274 6.72864 4.97378 6.69462 5.03003 6.67173C5.08627 6.64884 5.14657 6.63755 5.20729 6.63856C5.26801 6.63956 5.3279 6.65283 5.38336 6.67756C5.43882 6.70229 5.48871 6.73798 5.53004 6.78249L6.01187 7.29979L8.4152 4.36585C8.45281 4.31978 8.49917 4.2816 8.5516 4.25353C8.60404 4.22546 8.66151 4.20805 8.72071 4.2023C8.7799 4.19656 8.83965 4.20258 8.89651 4.22004C8.95336 4.2375 9.0062 4.26604 9.05197 4.30402L9.40197 4.59289C9.49456 4.66989 9.55298 4.78035 9.56452 4.90022C9.57605 5.02009 9.53977 5.13967 9.46357 5.23292L6.43023 8.93452C6.3899 8.98377 6.33959 9.02393 6.28263 9.05235C6.22566 9.08076 6.16332 9.0968 6.09971 9.0994C6.03611 9.102 5.97267 9.09111 5.91357 9.06744C5.85447 9.04376 5.80106 9.00785 5.75683 8.96205Z"
      fill="white"
    ></path>
  </svg>
);
