import { Popover } from "@shopify/polaris";
import React, { useCallback, useState } from "react";
import { ChromePicker } from "react-color";

interface ColorPickerWrapperProps {
  color: string;
  setColor: (color: string) => void;
  disabled?: boolean;
}

const ColorPickerWrapper = ({ color, setColor, disabled }: ColorPickerWrapperProps): React.ReactElement => {
  const [popoverActive, setPopoverActive] = useState(false);

  const handleChangeComplete = useCallback(
    (color: { hex: string }) => {
      setColor(color.hex);
    },
    [setColor]
  );

  const togglePopoverActive = useCallback(() => setPopoverActive((popoverActive) => !popoverActive), []);

  const activator = (
    <div
      style={{
        height: 22,
        width: 22,
        background: disabled ? "rgba(0, 0, 0, 0.05)" : color,
        border: "1px solid #CCCCCC",
        borderRadius: 3,
        cursor: "pointer",
      }}
      onClick={togglePopoverActive}
    ></div>
  );
  return (
    <Popover
      active={disabled ? false : popoverActive}
      activator={activator}
      onClose={togglePopoverActive}
    >
      <ChromePicker
        color={color}
        onChangeComplete={handleChangeComplete}
      />
    </Popover>
  );
};

export default ColorPickerWrapper;
