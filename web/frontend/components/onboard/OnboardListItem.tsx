import { Box, Collapsible, InlineStack, Text } from "@shopify/polaris";
import React, { useState } from "react";
import { IOnboardingItem } from "types";
import { CheckIcon } from "../common/SvgIcons";

function OnboardListItem({
  item,
  handleToggleOnboardItem,
}: {
  item: IOnboardingItem;
  handleToggleOnboardItem: (id: string) => void;
}) {
  const [isHovered, setIsHovered] = useState(false);
  const [isCircleHovered, setIsCircleHovered] = useState(false);

  return (
    <div
      onClick={() => handleToggleOnboardItem(item.id)}
      style={{
        cursor: "pointer",
        padding: 8,
        borderRadius: 12,
        background: item.active || isHovered ? "rgba(243, 243, 243, 1)" : "",
        border: item.active ? "1px solid rgba(204, 204, 204, 1)" : "1px solid transparent",
        boxShadow: item.active ? "rgba(26, 26, 26, 0.07) 0px 1px 0px 0px" : "",
        transition: "all 0.3s ease",
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Box>
        <InlineStack gap="300">
          <div
            style={{
              cursor: "pointer",
              height: 20,
              width: 20,
              borderRadius: 50,
              border:
                item.isCompleted || item.loading ? "0" : isCircleHovered ? "1px solid #8A8A8A" : "1px solid #8A8A8A",
            }}
            onMouseEnter={() => setIsCircleHovered(true)}
            onMouseLeave={() => setIsCircleHovered(false)}
          >
            {item.isCompleted && <CheckIcon />}
          </div>

          <div style={{ flex: 1 }}>
            <Text
              as="h3"
              variant="bodyMd"
              fontWeight="semibold"
            >
              {item.title}
            </Text>
          </div>
        </InlineStack>
      </Box>
      <Collapsible
        open={item.active}
        id="basic-collapsible2"
        transition={{ duration: "500ms", timingFunction: "ease-in-out" }}
        expandOnPrint
      >
        <Box paddingInlineStart="800">
          <Box paddingBlockStart="100">{item.children}</Box>
        </Box>
      </Collapsible>
    </div>
  );
}

export default OnboardListItem;
