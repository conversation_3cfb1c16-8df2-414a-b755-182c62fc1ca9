import { <PERSON>Stack, Box, Button, InlineStack, Text } from "@shopify/polaris";
import React from "react";

export default function Plan() {
  const isSavingInProgress = false;

  const pickOnboardPlan = () => {
    console.log("pickOnboardPlan");
  };

  return (
    <Box>
      <InlineStack
        align="space-between"
        blockAlign="start"
      >
        <BlockStack gap="300">
          <Text
            as="p"
            variant="bodySm"
            tone="subdued"
          >
            Choose a Shopify plan with the right features for your new business.
          </Text>
          <Box>
            <Button
              onClick={pickOnboardPlan}
              variant="primary"
              loading={isSavingInProgress}
            >
              Pick a plan
            </Button>
          </Box>
        </BlockStack>
        <img
          src={process.env.VITE_STORAGE_CDN_BASE_URL + "/assets/onboard-plans.png"}
          alt="hello"
        />
      </InlineStack>
    </Box>
  );
}
