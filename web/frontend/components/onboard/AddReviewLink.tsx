import { useEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";

import { BlockStack, Box, Button, Card, Icon, InlineStack, Link, Select, Text, TextField } from "@shopify/polaris";

import { DeleteIcon, InfoIcon, PlusIcon } from "@shopify/polaris-icons";

import { demoLink } from "../../utils/demo-link";
import { helpTextForAutoPublish } from "../../utils/helper";

import { ReviewLink } from "@trustsync/types";
import { useQueryClient } from "storeware-tanstack-query";
import queryKeys from "../../../app/enums/queryKeys";
import onboardReviewLinkValidation from "../../../app/validations/onboardReviewLinkValidation";
import { saveOnboardReviewLinkSetting } from "../../apis/onboard.api";
import { autoPublishOptions, percentageReviewOptions, reviewLinkOptions } from "../../utils/config";

interface AddReviewLinkProps {
  emailSetting: any;
}

interface IValidationMessage {
  autoPublish?: string;
  reviewLinks?: string;
}

export default function AddReviewLink({ emailSetting }: AddReviewLinkProps) {
  const [language, setLanguage] = useState("english");
  const [autoPublish, setAutoPublish] = useState(4);
  const [reviewLinks, setReviewLinks] = useState<ReviewLink[]>([]);
  const [totalPercentage, setTotalPercentage] = useState(0);

  const queryClient = useQueryClient();

  const [validationMessage, setValidationMessage] = useState<IValidationMessage>({});
  const [reviewLinkErrorMessage, setReviewLinkErrorMessage] = useState("");

  useEffect(() => {
    if (validationMessage?.reviewLinks) {
      setReviewLinkErrorMessage(validationMessage?.reviewLinks || "");
    }
  }, [validationMessage]);

  useEffect(() => {
    let percentage = 0;
    for (const reviewLink of reviewLinks) {
      if (reviewLink.percentage) {
        percentage += parseInt(reviewLink.percentage, 10);
      }
    }

    setReviewLinkErrorMessage("");
    setTotalPercentage(percentage);

    if (percentage != 100 && reviewLinks?.length > 0) {
      setReviewLinkErrorMessage("The total of all percentages must equal 100%");
    }
  }, [reviewLinks]);

  useEffect(() => {
    setAutoPublish(parseInt(emailSetting?.autoPublish || 4));
    setReviewLinks(emailSetting?.reviewLinks || []);
  }, [emailSetting]);

  const handleAddReview = () => {
    setReviewLinkErrorMessage("");

    if (reviewLinks.length == 0) {
      setReviewLinks([
        {
          id: uuidv4(),
          platform: reviewLinkOptions.filter((x) => !reviewLinks.map((review) => review.platform).includes(x.value))[0]
            .value,
          url: "",
          percentage: "100%",
        },
      ]);
    } else if (reviewLinks.length == 1) {
      setReviewLinks([
        { ...reviewLinks[0], percentage: "50%" },
        {
          id: uuidv4(),
          platform: reviewLinkOptions.filter((x) => !reviewLinks.map((review) => review.platform).includes(x.value))[0]
            .value,
          url: "",
          percentage: "50%",
        },
      ]);
    } else if (reviewLinks.length == 2) {
      setReviewLinks([
        { ...reviewLinks[0], percentage: "60%" },
        { ...reviewLinks[1], percentage: "20%" },
        {
          id: uuidv4(),
          platform: reviewLinkOptions.filter((x) => !reviewLinks.map((review) => review.platform).includes(x.value))[0]
            .value,
          url: "",
          percentage: "20%",
        },
      ]);
    } else {
      setReviewLinks([
        ...reviewLinks,
        {
          id: uuidv4(),
          platform: reviewLinkOptions.filter((x) => !reviewLinks.map((review) => review.platform).includes(x.value))[0]
            .value,
          url: "",
          percentage: "20%",
        },
      ]);
    }
  };

  const handleChangeURL = (id: string, url: string) => {
    const newReviewLinks = reviewLinks.map((review) => {
      if (review.id === id) {
        return { ...review, url };
      }
      return review;
    });

    setReviewLinks(newReviewLinks);
  };

  const handleChangePercentage = (id: string, percentage: string) => {
    const newReviewLinks = reviewLinks.map((review) => {
      if (review.id === id) {
        return { ...review, percentage };
      }
      return review;
    });
    setReviewLinks(newReviewLinks);
  };

  const handleChangePlatform = (id: string, platform: string) => {
    const newReviewLinks = reviewLinks.map((review) => {
      if (review.id === id) {
        return { ...review, platform };
      }
      return review;
    });
    setReviewLinks(newReviewLinks);
  };

  const handleRemoveLink = (id: string) => {
    if (reviewLinks.length == 2) {
      const newReviewLinks = reviewLinks.filter((review) => review.id !== id);
      newReviewLinks[0].percentage = "100%";
      setReviewLinks(newReviewLinks);
    } else if (reviewLinks.length == 3) {
      const newReviewLinks = reviewLinks.filter((review) => review.id !== id);
      newReviewLinks[0].percentage = "60%";
      newReviewLinks[1].percentage = "40%";
      setReviewLinks(newReviewLinks);
    } else if (reviewLinks.length == 4) {
      const newReviewLinks = reviewLinks.filter((review) => review.id !== id);
      newReviewLinks[0].percentage = "50%";
      newReviewLinks[1].percentage = "30%";
      newReviewLinks[2].percentage = "20%";
      setReviewLinks(newReviewLinks);
    } else {
      const newReviewLinks = reviewLinks.filter((review) => review.id !== id);
      setReviewLinks(newReviewLinks);
    }
  };

  const [isSavingInProgress, setSavingInProgress] = useState(false);

  const saveAddReviewLink = async () => {
    setReviewLinkErrorMessage("");

    const response = onboardReviewLinkValidation.validateForm({ autoPublish, reviewLinks });

    if (!response?.isValid && response?.reviewLinks?.length) {
      setReviewLinkErrorMessage(response?.reviewLinks);
      return false;
    }

    try {
      setSavingInProgress(true);

      const response = await saveOnboardReviewLinkSetting({ language, autoPublish, reviewLinks });

      if (response?.status === 422) {
        setValidationMessage(response?.errors);
      } else if (response?.status === 200) {
        queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_SETTING] });

        shopify.toast.show("Settings updated", {
          duration: 5000,
        });
      }
    } finally {
      setSavingInProgress(false);
    }
  };

  return (
    <BlockStack gap="300">
      <Text
        as="p"
        variant="bodySm"
        tone="subdued"
      >
        Add at least one review link to send review request emails
      </Text>

      <Select
        label="Auto-publish new reviews"
        options={autoPublishOptions}
        value={String(autoPublish)}
        onChange={(value) => setAutoPublish(parseInt(value))}
        helpText={
          !validationMessage?.autoPublish && (
            <Text as="p">
              {helpTextForAutoPublish(autoPublish)} star ratings will be directed to an online form (
              <a
                href="https://review.trustsync.io/review/632885f2a61eb87e5c19e12a?rating=1"
                target="_blank"
              >
                view example
              </a>
              ). Nobody except you will see such negative feedback.
            </Text>
          )
        }
        error={validationMessage?.autoPublish}
      />

      {(reviewLinks?.length > 0 || reviewLinkErrorMessage) && (
        <BlockStack gap="400">
          {reviewLinks.map((link: ReviewLink, index: number) => {
            return (
              <Card key={index}>
                <BlockStack gap="300">
                  <TextField
                    label={`Review link #${index + 1}`}
                    type="text"
                    value={link.url}
                    onChange={(value) => handleChangeURL(link.id, value)}
                    placeholder={`https://www.${link.platform}.com/review/...`}
                    autoComplete="off"
                    connectedLeft={
                      <div style={{ minWidth: 140 }}>
                        <Select
                          label=""
                          options={reviewLinkOptions.filter((option) => {
                            return (
                              option.value == link.platform ||
                              !reviewLinks
                                .filter((review) => review.id !== link.id)
                                .map((review) => review.platform)
                                .includes(option.value)
                            );
                          })}
                          onChange={(value) => handleChangePlatform(link.id, value)}
                          value={link.platform}
                          labelHidden
                        />
                      </div>
                    }
                    connectedRight={
                      <Button
                        icon={DeleteIcon}
                        onClick={() => handleRemoveLink(link.id)}
                      />
                    }
                    helpText={
                      demoLink[link.platform] &&
                      demoLink[link.platform].length > 0 && (
                        <Text as="span">
                          <a
                            href={demoLink[link.platform]}
                            target="_blank"
                          >
                            Click here
                          </a>{" "}
                          to know where to find your <Text as="p">{link.platform}</Text> link.
                        </Text>
                      )
                    }
                  />

                  <Select
                    label="Request review on this website"
                    options={percentageReviewOptions}
                    onChange={(value) => handleChangePercentage(link.id, value)}
                    value={link.percentage}
                  />
                </BlockStack>
              </Card>
            );
          })}

          {reviewLinkErrorMessage && (
            <InlineStack
              gap="200"
              wrap={false}
              blockAlign="start"
            >
              <Box>
                <Icon
                  source={InfoIcon}
                  tone="textCritical"
                ></Icon>
              </Box>
              <Text
                as="p"
                tone="critical"
              >
                {reviewLinkErrorMessage}
              </Text>
            </InlineStack>
          )}
        </BlockStack>
      )}

      <InlineStack
        align="end"
        gap="300"
      >
        <Button
          icon={PlusIcon}
          onClick={handleAddReview}
        >
          Add review link
        </Button>
        <Button
          variant="primary"
          onClick={saveAddReviewLink}
          loading={isSavingInProgress}
          disabled={Number(totalPercentage) != 100}
        >
          Continue
        </Button>
      </InlineStack>
    </BlockStack>
  );
}
