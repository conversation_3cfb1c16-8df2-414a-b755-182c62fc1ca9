import { Bleed, BlockStack, Box, Card, Divider, InlineStack, ProgressBar, Text } from "@shopify/polaris";
import { useEffect, useState } from "react";

import { useAppQuery } from "storeware-tanstack-query";
import { IOnboardingItem } from "types";
import queryKeys from "../../../app/enums/queryKeys";
import { fetchSettings } from "../../apis/settings.api";
import AddReviewLink from "./AddReviewLink";
import EnableEmail from "./EnableEmail";
import OnboardListItem from "./OnboardListItem";
import SetUpEmail from "./SetUpEmail";

interface IOnboardingItemState {
  [key: string]: {
    active: boolean;
    loading: boolean;
    isCompleted: boolean;
  };
}

export default function OnboardingProcess({ isDashboard = false }) {
  const { data: { shop, settings: emailSetting } = {} } = useAppQuery({
    queryKey: [queryKeys.FETCH_SETTING],
    queryFn: fetchSettings,
    reactQueryOptions: {
      enabled: true,
      staleTime: 0,
    },
  });

  const [onboardingItems, setOnboardingItems] = useState<IOnboardingItem[]>([]);

  useEffect(() => {
    const onboardingSteps = shop?.onboardingSteps || {};

    const isEmailSendingEnabled = onboardingSteps?.EMAIL_SENDING || false;
    const hasReviewLinks = onboardingSteps?.REVIEW_LINK || false;
    const isEmailTriggerEnabled = onboardingSteps?.EMAIL_TRIGGER || false;

    const onboardItemsState: IOnboardingItemState = {
      enable_email_sending: {
        active: !isEmailSendingEnabled,
        loading: false,
        isCompleted: isEmailSendingEnabled,
      },
      add_review_link: {
        active: isEmailSendingEnabled && !hasReviewLinks,
        loading: false,
        isCompleted: hasReviewLinks,
      },
      set_up_email_triggers: {
        active: isEmailSendingEnabled && hasReviewLinks && !isEmailTriggerEnabled,
        loading: false,
        isCompleted: isEmailTriggerEnabled,
      },
      pick_plan: {
        active: false,
        loading: false,
        isCompleted: true,
      },
    };

    setOnboardingItems([
      {
        id: "enable_email_sending",
        title: "Enable email sending",
        children: <EnableEmail emailSetting={emailSetting} />,
        ...onboardItemsState["enable_email_sending"],
      },
      {
        id: "add_review_link",
        title: "Add a review link",
        children: <AddReviewLink emailSetting={emailSetting} />,
        ...onboardItemsState["add_review_link"],
      },
      {
        id: "set_up_email_triggers",
        title: "Set up email triggers",
        children: <SetUpEmail emailSetting={emailSetting} />,
        ...onboardItemsState["set_up_email_triggers"],
      },
    ]);
  }, [shop, emailSetting]);

  const handleToggleOnboardItem = (id: string) => {
    setOnboardingItems((prevItems: any) =>
      prevItems.map((item: any) => ({
        ...item,
        active: item.id === id,
      }))
    );
  };

  const totalSteps = 3; // Total number of steps
  const completedSteps = onboardingItems.filter((item: any) => item.isCompleted).length;

  return (
    <Card>
      <BlockStack gap="200">
        <BlockStack gap="100">
          <InlineStack
            align="space-between"
            blockAlign="center"
          >
            <Text
              as="h4"
              variant="headingSm"
            >
              {isDashboard ? "Incomplete Step" : "Welcome to TrustSync"}
            </Text>
          </InlineStack>
          <Text
            as="p"
            variant="bodyMd"
            tone="subdued"
          >
            Complete the onboarding process to get started
          </Text>
        </BlockStack>
        <InlineStack
          wrap={false}
          blockAlign="center"
          gap="400"
        >
          <div style={{ flex: "0 0 auto" }}>
            <Text
              as="p"
              variant="bodyMd"
            >
              {completedSteps} out of {totalSteps} steps are completed
            </Text>
          </div>
          <ProgressBar
            progress={completedSteps * 33.334}
            size="small"
            tone="success"
          />
        </InlineStack>
      </BlockStack>

      <Bleed marginInline="400">
        <Box paddingBlock="300">
          <Divider />
        </Box>
      </Bleed>

      <Box>
        <BlockStack gap="100">
          {onboardingItems.map((item: IOnboardingItem) => (
            <OnboardListItem
              key={item.id}
              item={item}
              handleToggleOnboardItem={() => handleToggleOnboardItem(item.id)}
            />
          ))}
        </BlockStack>
      </Box>
    </Card>
  );
}
