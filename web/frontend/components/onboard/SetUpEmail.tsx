import React, { useCallback, useEffect, useState } from "react";

import {
  BlockStack,
  Box,
  Button,
  InlineGrid,
  InlineStack,
  RadioButton,
  Select,
  Text,
  TextField,
} from "@shopify/polaris";

import { useQueryClient } from "storeware-tanstack-query";
import queryKeys from "../../../app/enums/queryKeys";
import { saveOnboardEmailTriggerSetting } from "../../apis/onboard.api";
import { whenActionOptions, whenOptions } from "../../utils/config";

interface SetUpEmailProps {
  emailSetting: any;
}

export default function SetUpEmail({ emailSetting }: SetUpEmailProps) {
  const [whenToSendDays, setWhenToSendDays] = useState("1");
  const [whenToSendAfter, setwhenToSendAfter] = useState("fulfilled");
  const [whenToSendTag, setwhenToSendTag] = useState("");
  const [sendOnRepeat, setSendOnRepeat] = useState("no");

  const queryClient = useQueryClient();

  const handleWhenToSendDays = (value: string) => {
    setWhenToSendDays(value);
  };

  const handleWhenToSendAfter = (value: string) => {
    setwhenToSendAfter(value);
  };

  const handleSendOnRepeat = useCallback((_: unknown, value: string) => {
    setSendOnRepeat(value);
  }, []);

  const [isSavingInProgress, setSavingInProgress] = useState(false);
  const [validationMessage, setValidationMessage] = useState<Record<string, string>>({});

  const saveEmailTrigger = async () => {
    try {
      setSavingInProgress(true);
      setValidationMessage({});

      const response = await saveOnboardEmailTriggerSetting({
        sendOnRepeat,
        whenToSend: {
          days: whenToSendDays,
          after: whenToSendAfter,
          tag: whenToSendTag,
        },
      });

      if (response.status === 422) {
        setValidationMessage(response.errors);
      } else if (response.status === 200) {
        queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_SETTING] });

        shopify.toast.show("Settings updated", {
          duration: 5000,
        });
      }
    } finally {
      setSavingInProgress(false);
    }
  };

  useEffect(() => {
    setWhenToSendDays(emailSetting?.whenToSend?.days);
    setwhenToSendAfter(emailSetting?.whenToSend?.after);
    setwhenToSendTag(emailSetting?.whenToSend?.tag || "");
    setSendOnRepeat(emailSetting?.sendOnRepeat || "no");
  }, [emailSetting]);

  return (
    <Box>
      <BlockStack gap="400">
        <Text
          as="p"
          variant="bodySm"
          tone="subdued"
        >
          Automatically send emails to your customers
        </Text>

        <InlineGrid
          columns={2}
          gap="300"
        >
          <Select
            label="When to send email"
            name="whenSend"
            value={whenToSendDays}
            options={whenOptions}
            onChange={handleWhenToSendDays}
          />
          <Select
            label="&nbsp;"
            value={whenToSendAfter}
            options={whenActionOptions}
            onChange={handleWhenToSendAfter}
          />
        </InlineGrid>

        {whenToSendAfter === "tagadded" && (
          <InlineGrid>
            <TextField
              label="Tag"
              value={whenToSendTag}
              onChange={(value) => {
                setwhenToSendTag(value);
              }}
              placeholder="Enter a valid tag"
              error={validationMessage?.tag}
              autoComplete="off"
            />
          </InlineGrid>
        )}

        <BlockStack gap="100">
          <Text
            as="p"
            variant="bodySm"
          >
            On repeat orders
          </Text>
          <InlineStack
            blockAlign="center"
            gap="400"
          >
            <RadioButton
              id="yes"
              name="sendOnRepeat"
              label="Ask again"
              checked={sendOnRepeat === "yes"}
              onChange={handleSendOnRepeat}
            />
            <RadioButton
              id="no"
              name="sendOnRepeat"
              label="Don’t ask again"
              checked={sendOnRepeat === "no"}
              onChange={handleSendOnRepeat}
            />
          </InlineStack>
          <Box paddingInlineStart="600">
            <Text
              as="p"
              variant="bodySm"
              tone="subdued"
            >
              If a review request is already sent for the product or store, the app will{" "}
              {sendOnRepeat === "yes" ? "" : "not"} ask for another review.
            </Text>
          </Box>
        </BlockStack>

        <InlineStack align="end">
          <Button
            variant="primary"
            onClick={saveEmailTrigger}
            loading={isSavingInProgress}
          >
            Continue
          </Button>
        </InlineStack>
      </BlockStack>
    </Box>
  );
}
