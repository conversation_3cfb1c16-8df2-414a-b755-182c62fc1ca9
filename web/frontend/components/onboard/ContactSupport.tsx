import { MediaCard } from "@shopify/polaris";
import { ChatIcon, PhoneIcon } from "@shopify/polaris-icons";

export default function ContactSupport() {
  return (
    <MediaCard
      title="Want to schedule a call with TrustSync experts?"
      size="small"
      primaryAction={{
        content: "Schedule a call",
        icon: PhoneIcon,
        url: "https://storeware.io/trustsync/talk-with-expert",
        target: "_blank",
      }}
      secondaryAction={{
        content: "Open live chat",
        icon: ChatIcon,
        onAction: () => {
          window.$crisp.do("chat:open");
        },
      }}
      description="We have some in-house Shopify experts ready to help if you need anything when using our app. Would you like to book a one-on-one session with them? It’s completely FREE!"
    >
      <img
        width="100%"
        height="100%"
        style={{
          objectFit: "cover",
          objectPosition: "center",
        }}
        src={process.env.VITE_STORAGE_CDN_BASE_URL + "/assets/support-expert.png"}
      />
    </MediaCard>
  );
}
