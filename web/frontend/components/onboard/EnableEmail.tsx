import { Block<PERSON>tack, Button, InlineStack, Text } from "@shopify/polaris";
import { useEffect, useState } from "react";
import { useQueryClient } from "storeware-tanstack-query";
import queryKeys from "../../../app/enums/queryKeys";
import { saveOnboardEnableEmailSetting } from "../../apis/onboard.api";

interface EnableEmailProps {
  emailSetting: any;
}

export default function EnableEmail({ emailSetting }: EnableEmailProps) {
  const queryClient = useQueryClient();
  const [isEmailSendingActive, setEmailSendingActive] = useState(false);

  useEffect(() => {
    setEmailSendingActive(emailSetting?.active);
  }, [emailSetting]);

  const [isSavingInProgress, setSavingInProgress] = useState(false);

  const handleEnabledEmailSending = async () => {
    try {
      setSavingInProgress(true);

      setEmailSendingActive((prevEmailSendingActive) => !prevEmailSendingActive);

      const response = await saveOnboardEnableEmailSetting({ active: isEmailSendingActive });

      if (response.status === 200) {
        queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_SETTING] });

        shopify.toast.show("Settings updated", {
          duration: 5000,
        });
      }
    } finally {
      setSavingInProgress(false);
    }
  };

  return (
    <BlockStack gap="200">
      <Text
        as="p"
        variant="bodySm"
        tone="subdued"
      >
        Send review request emails to your customers
      </Text>
      <InlineStack align="end">
        <Button
          variant="primary"
          onClick={handleEnabledEmailSending}
          loading={isSavingInProgress}
        >
          {isEmailSendingActive ? "Deactivate" : "Activate"}
        </Button>
      </InlineStack>
    </BlockStack>
  );
}
