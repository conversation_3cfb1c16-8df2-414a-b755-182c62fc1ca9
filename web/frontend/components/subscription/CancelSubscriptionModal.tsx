import { BlockStack, Frame, Modal, Text } from "@shopify/polaris";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useQueryClient } from "storeware-tanstack-query";
import queryKeys from "../../../app/enums/queryKeys";
import { freePlanSubscription } from "../../apis/subscription.api";

export default function CancelSubscriptionModal({
  showCancelModal,
  setShowCancelModal,
}: {
  showCancelModal: boolean;
  setShowCancelModal: (value: boolean) => void;
}) {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const [isLoadingProgress, setLoadingrogress] = useState(false);
  const hangleCancelSubscriptionPlan = async () => {
    try {
      setLoadingrogress(true);

      await freePlanSubscription();
    } catch (error) {
      console.log(error);
    } finally {
      setTimeout(() => {
        setLoadingrogress(false);

        queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_SHOP_INFO] });

        navigate("/");
      }, 3000);
    }
  };

  return (
    <div style={{ height: "500px" }}>
      <Frame>
        <Modal
          size="small"
          open={showCancelModal}
          onClose={() => {
            setShowCancelModal(false);
          }}
          title="Confirm"
          primaryAction={{
            content: "Yes Confirm!",
            onAction: hangleCancelSubscriptionPlan,
            destructive: true,
            loading: isLoadingProgress,
          }}
          secondaryActions={[
            {
              content: "Cancel",
              onAction: () => {
                setShowCancelModal(false);
              },
            },
          ]}
        >
          <Modal.Section>
            <BlockStack>
              <BlockStack
                gap="200"
                align="center"
              >
                <Text
                  as="p"
                  alignment="center"
                >
                  Are you sure you want to cancel your subscription?
                </Text>
                <Text
                  as="p"
                  alignment="center"
                >
                  Your store data will be deleted according to the plan.
                </Text>
              </BlockStack>
            </BlockStack>
          </Modal.Section>
        </Modal>
      </Frame>
    </div>
  );
}
