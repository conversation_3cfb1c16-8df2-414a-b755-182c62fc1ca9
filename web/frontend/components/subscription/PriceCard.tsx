import { useNavigate } from "react-router-dom";

import { Badge, Bleed, BlockStack, Box, Button, Card, Divider, InlineStack, Text } from "@shopify/polaris";

import { Plan, SubscriptionDocument } from "@trustsync/types";
import PlanItemList from "./PlanItemList";

interface PriceCardProps {
  plan: Plan;
  subscription: SubscriptionDocument;
}

export default function PriceCard({ plan, subscription }: PriceCardProps) {
  const isSelectedPlan = subscription?.plan === plan?.slug;

  const navigate = useNavigate();

  const hangleCheckoutSubscriptionPlan = () => {
    navigate(`/checkout/${plan.slug}`);
  };

  return (
    <Card>
      <InlineStack
        align="space-between"
        blockAlign="start"
      >
        <BlockStack gap="200">
          <Text
            as="h4"
            variant="headingMd"
          >
            {plan.name} {isSelectedPlan && <Badge tone="success">Current Plan</Badge>}
          </Text>
          <Text as="p">{plan.emails} Email/Month</Text>
        </BlockStack>
        <BlockStack>
          {plan.discountPrice !== plan.discountPrice && (
            <Text
              as="p"
              alignment="end"
              textDecorationLine="line-through"
            >
              ${plan.price}
            </Text>
          )}

          <Text
            as="p"
            variant="headingLg"
          >
            ${plan.discountPrice}
            <Text
              as="span"
              variant="bodySm"
            >
              /M
            </Text>
          </Text>
        </BlockStack>
      </InlineStack>
      <Bleed>
        <Box paddingBlock="400">
          <Divider />
        </Box>
      </Bleed>

      <BlockStack gap="150">
        {plan.options.map((option, index) => (
          <PlanItemList
            key={index}
            option={option}
          />
        ))}
      </BlockStack>

      <Box paddingBlockStart="300">
        <Button
          fullWidth
          variant="primary"
          disabled={isSelectedPlan}
          onBlur={hangleCheckoutSubscriptionPlan}
        >
          {isSelectedPlan ? "Selected" : "Choose"}
        </Button>
      </Box>
    </Card>
  );
}
