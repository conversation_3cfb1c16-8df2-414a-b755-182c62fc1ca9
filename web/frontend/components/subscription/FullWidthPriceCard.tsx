import { useNavigate } from "react-router-dom";

import { <PERSON><PERSON>, Bleed, BlockStack, Box, Button, Card, Divider, InlineStack, Text } from "@shopify/polaris";
import { Plan, SubscriptionDocument } from "@trustsync/types";
import PlanItemList from "./PlanItemList";

export const FullWidthPriceCard = ({
  plan,
  subscription,
  shop,
}: {
  plan: Plan;
  subscription: SubscriptionDocument;
  shop: any;
}) => {
  const navigate = useNavigate();

  const isSelectedPlan = subscription?.plan === plan?.slug;
  const isShopVeified = shop?.verified || subscription?.plan !== "free";

  const showVerifyButton = !isShopVeified && plan?.slug === "free";

  return (
    <Card>
      <InlineStack
        align="space-between"
        blockAlign="center"
      >
        <BlockStack gap="200">
          <Text
            as="h4"
            variant="headingMd"
          >
            {plan.name} {isSelectedPlan && <Badge tone="success">Current Plan</Badge>}
          </Text>
          <Text as="p">{plan.emails} Email/Month</Text>
        </BlockStack>
        <InlineStack
          gap="400"
          align="center"
          blockAlign="center"
        >
          {plan.price !== 0 ? (
            <Text
              as="p"
              variant="headingLg"
            >
              {plan.price}
              <Text
                as="span"
                variant="bodySm"
              >
                /M
              </Text>
            </Text>
          ) : null}
          <Button
            disabled={isSelectedPlan && !showVerifyButton}
            onClick={() => {
              navigate(showVerifyButton ? "/" : `/checkout/${plan.slug}`);
            }}
            variant={showVerifyButton ? "secondary" : "primary"}
          >
            {showVerifyButton ? "Continue With Free Plan" : "Choose"}
          </Button>
        </InlineStack>
      </InlineStack>
      <Bleed>
        <Box
          paddingBlockStart="400"
          paddingBlockEnd="200"
        >
          <Divider />
        </Box>
      </Bleed>
      <div className="full-width-plan-list">
        {plan.options.map((option, index) => (
          <Box
            paddingBlockStart="150"
            key={index}
          >
            <PlanItemList option={option} />
          </Box>
        ))}
      </div>
    </Card>
  );
};

export default FullWidthPriceCard;
