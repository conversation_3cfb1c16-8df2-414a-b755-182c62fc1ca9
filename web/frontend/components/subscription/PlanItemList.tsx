import { Box, Icon, InlineStack, Text } from "@shopify/polaris";
import { CheckIcon, XIcon } from "@shopify/polaris-icons";
import React from "react";

interface PlanItemListProps {
  option: {
    enable: boolean;
    label: string;
  };
}

function PlanItemList({ option }: PlanItemListProps) {
  return (
    <InlineStack
      gap="200"
      wrap={false}
      blockAlign="start"
    >
      <Box>
        <Icon source={option.enable ? CheckIcon : XIcon}></Icon>
      </Box>
      <Text as="p">{option?.label}</Text>
    </InlineStack>
  );
}

export default PlanItemList;
