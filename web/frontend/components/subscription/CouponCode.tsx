import { Button, InlineStack, Text, TextField } from "@shopify/polaris";
import { XIcon } from "@shopify/polaris-icons";
import React, { useCallback, useState } from "react";

function Checkout() {
  const [couponApplied, setCouponApplied] = useState(false);
  const [couponCode, setCouponCode] = useState("");

  const handleCouponCodeChange = useCallback((value: string) => {
    setCouponCode(value);
  }, []);

  const removeCouponCode = () => {
    setCouponApplied(false);
    setCouponCode("");
  };

  const handleCouponApply = () => {
    if (couponCode) {
      setCouponApplied(true);
    }
  };

  return (
    <>
      {couponApplied ? (
        <InlineStack
          gap="200"
          align="space-between"
        >
          <InlineStack
            gap="100"
            blockAlign="center"
          >
            <Text
              as="h3"
              variant="headingMd"
            >
              {couponCode}
            </Text>
            <Button
              variant="plain"
              tone="critical"
              onClick={() => removeCouponCode()}
              accessibilityLabel="Remove coupon"
              icon={XIcon}
            ></Button>
          </InlineStack>
          <Text
            as="h3"
            variant="headingMd"
          >
            -$10.00
          </Text>
        </InlineStack>
      ) : (
        <TextField
          label="Give your coupon code"
          value={couponCode}
          onChange={handleCouponCodeChange}
          placeholder="Coupon code"
          labelAction={{ content: "Close" }}
          autoComplete="off"
          connectedRight={
            <Button
              onClick={() => handleCouponApply()}
              disabled={!couponCode}
            >
              Apply
            </Button>
          }
        />
      )}
    </>
  );
}

export default Checkout;
