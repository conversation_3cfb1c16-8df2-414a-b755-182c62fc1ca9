import { Text } from "@shopify/polaris";
import "quill/dist/quill.snow.css"; // Add css for snow theme
import { useEffect, useRef, useState } from "react";
import ReactQuill from "react-quill-new";
import { useAppContext } from "../../providers/AppProvider";

interface EditorWrapperProps {
  emailHTML: string;
  setEmailHTML: (html: string) => void;
}

export default function EditorWrapper({ emailHTML, setEmailHTML }: EditorWrapperProps) {
  const { isShopUnderProPlan } = useAppContext();
  const isShopUnderFreePlan = !isShopUnderProPlan;
  const [showToolbar, setShowToolbar] = useState(false);
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isToolbarInteractionRef = useRef(false);

  const container = [[{ header: [1, 2, 3, false] }], ["bold", "italic", "underline"], ["clean"]];

  const modules = {
    toolbar: showToolbar
      ? {
          container: container,
          handlers: {},
        }
      : false,
  };

  const formats = ["header", "font", "size", "bold", "italic", "underline", "strike", "blockquote"];

  const handleSelectionChange = (range: any, _source: any, editor: any) => {
    // Clear any existing timeout
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = null;
    }

    if (range && range.length > 0) {
      setShowToolbar(true);
      isToolbarInteractionRef.current = false;
    } else {
      // Use timeout to allow toolbar interactions
      hideTimeoutRef.current = setTimeout(() => {
        // Only hide if we're not in the middle of a toolbar interaction
        if (!isToolbarInteractionRef.current) {
          // Double-check that there's still no selection
          const currentSelection = editor?.getSelection();
          if (!currentSelection || currentSelection.length === 0) {
            setShowToolbar(false);
          }
        }
        isToolbarInteractionRef.current = false;
      }, 200);
    }
  };

  const handleToolbarInteraction = (event: any) => {
    // Check if the interaction is with toolbar elements (including dropdowns)
    const isToolbarElement = event.target.closest(".ql-toolbar") || event.target.closest(".ql-picker-options");

    if (isToolbarElement) {
      isToolbarInteractionRef.current = true;
      // Clear any pending hide timeout
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
        hideTimeoutRef.current = null;
      }
    }
  };

  // Set up event listeners for toolbar interactions
  useEffect(() => {
    // Add event listeners to catch all toolbar interactions
    document.addEventListener("mousedown", handleToolbarInteraction);
    document.addEventListener("click", handleToolbarInteraction);

    return () => {
      // Cleanup
      document.removeEventListener("mousedown", handleToolbarInteraction);
      document.removeEventListener("click", handleToolbarInteraction);
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
      }
    };
  }, []);

  return (
    <>
      <Text
        as="h3"
        variant="headingSm"
        tone={isShopUnderFreePlan ? "disabled" : "base"}
      >
        Reply Feedback
      </Text>
      <div className="quill-wrapper">
        <div
          className={`${isShopUnderFreePlan} ? "quill-editor-disabled" : ""`}
          style={{ background: "white" }}
        >
          <ReactQuill
            theme="snow"
            value={emailHTML}
            onChange={setEmailHTML}
            modules={modules}
            formats={formats}
            onChangeSelection={handleSelectionChange}
            readOnly={isShopUnderFreePlan} // Set to read-only mode
            placeholder={isShopUnderFreePlan ? "Upgrade to enable further engagement" : "Write your response here…"}
            style={showToolbar ? { height: "160px" } : { height: "80px" }}
          ></ReactQuill>
        </div>
      </div>
    </>
  );
}
