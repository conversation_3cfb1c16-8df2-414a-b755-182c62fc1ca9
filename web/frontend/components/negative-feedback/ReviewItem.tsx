import { <PERSON>ge, Box, Button, Icon, IndexTable, InlineStack, Text } from "@shopify/polaris";
import React from "react";

import { DeleteIcon, StarFilledIcon, StarIcon, ViewIcon } from "@shopify/polaris-icons";
import { formatDateTime, truncateString } from "../../utils/helper";

const statusBadge = (status: string) => {
  if (status == "replied") {
    return <Badge tone="info">Replied</Badge>;
  } else if (status == "resolved") {
    return <Badge tone="success">Resolved</Badge>;
  }
  return <Badge tone="critical">Unresolved</Badge>;
};

interface ReviewItemProps {
  negativeReviews: any[];
  selectedResources: string[];
  handleReplyReview: (review: any) => void;
  deletingReviewId: string | null;
  handleDeleteReview: (id: string) => void;
}

const ReviewItem = ({
  negativeReviews,
  selectedResources,
  handleReplyReview,
  deletingReviewId,
  handleDeleteReview,
}: ReviewItemProps) => {
  return (
    <>
      {negativeReviews.map((negativeReview, index) => (
        <IndexTable.Row
          id={negativeReview._id}
          key={negativeReview._id}
          selected={selectedResources.includes(negativeReview._id)}
          position={index}
          onClick={() => {}}
        >
          <IndexTable.Cell>
            <Text
              variant="bodyMd"
              fontWeight="bold"
              as="span"
            >
              {negativeReview.name}
            </Text>
          </IndexTable.Cell>
          <IndexTable.Cell>{negativeReview.email}</IndexTable.Cell>
          <IndexTable.Cell>
            <InlineStack blockAlign="center">
              {[...Array(5)].map((_, index) => (
                <Box key={index}>
                  <Icon
                    source={index < negativeReview.rating ? StarFilledIcon : StarIcon}
                    tone="base"
                  />
                </Box>
              ))}
            </InlineStack>
          </IndexTable.Cell>
          <IndexTable.Cell>
            {negativeReview?.hasAttachment && (
              <InlineStack align="center">
                <Box
                // width="40px"
                // height="40px"
                >
                  <img
                    src={negativeReview.attachment}
                    alt="attachment"
                    style={{ width: "40px", height: "40px", borderRadius: "5px" }}
                  />
                </Box>
              </InlineStack>
            )}
          </IndexTable.Cell>
          <IndexTable.Cell>
            <InlineStack align="center">{statusBadge(negativeReview.status || "unresolved")}</InlineStack>
          </IndexTable.Cell>
          <IndexTable.Cell>{truncateString(negativeReview.message)}</IndexTable.Cell>
          <IndexTable.Cell>{formatDateTime(negativeReview.created_at)}</IndexTable.Cell>
          <IndexTable.Cell>
            <InlineStack
              align="center"
              gap="200"
            >
              <Button
                icon={ViewIcon}
                onClick={() => {
                  handleReplyReview(negativeReview);
                }}
              />

              <Button
                icon={DeleteIcon}
                onClick={() => {
                  handleDeleteReview(negativeReview._id);
                }}
              />
            </InlineStack>
          </IndexTable.Cell>
        </IndexTable.Row>
      ))}
    </>
  );
};

export default ReviewItem;
