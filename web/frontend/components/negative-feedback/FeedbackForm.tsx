import {
  <PERSON><PERSON>,
  <PERSON>,
  BlockStack,
  Box,
  <PERSON>ton,
  Card,
  Divider,
  InlineStack,
  Modal,
  RadioButton,
  Text,
} from "@shopify/polaris";

import { AttachmentIcon, LockIcon } from "@shopify/polaris-icons";
import { MessageDocument } from "@trustsync/types";
import { lazy, Suspense, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useMutation, useQueryClient } from "storeware-tanstack-query";
import queryKeys from "../../../app/enums/queryKeys";
import { negativeFeedbackReply } from "../../apis/analytics.api";
import "../../assets/css/custom.css";
import { useAppContext } from "../../providers/AppProvider";
import { convertDateTime, stripTags } from "../../utils/helper";
const EditorWrapper = lazy(() => import("./EditorWrapper.jsx"));

interface FeedbackFormProps {
  replyReview: any;
  activeModal: boolean;
  setActiveModal: (active: boolean) => void;
}

export default function FeedbackForm({ replyReview, activeModal, setActiveModal }: FeedbackFormProps) {
  const queryClient = useQueryClient();
  const { isShopUnderProPlan } = useAppContext();
  const isShopUnderFreePlan = !isShopUnderProPlan;

  const navigate = useNavigate();

  const [reply, setReply] = useState("");
  const [status, setStatus] = useState("");
  const [shownAttachments, setShownAttachments] = useState(new Set());

  useEffect(() => {
    if (replyReview) {
      // setReply(replyReview?.reply || "");
      setStatus(replyReview?.status || "unresolved");
    }
    setActiveModal(true);
  }, []);

  const { mutate: handleNegativeReply, isPending } = useMutation({
    mutationFn: (payload: any) => negativeFeedbackReply(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_NEGATIVE_FEEDBACK] });

      shopify.toast.show("Negative feedback updated.", {
        duration: 5000,
        isError: false,
      });
    },
    onError: (error) => {
      console.log(error);
    },
    onSettled: () => {
      setActiveModal(false);
      navigate("/negative-feedbacks", { replace: true });
    },
  });

  const replyText = stripTags(reply || "");

  const handleModalClose = () => {
    setActiveModal(false);
    setShownAttachments(new Set());
    navigate("/negative-feedbacks", { replace: true });
  };

  const handleSubmitReply = () => {
    handleNegativeReply({ id: replyReview._id, reply, status });
  };

  const toggleMainAttachment = () => {
    setShownAttachments((prev) => {
      const newSet = new Set(prev);
      if (newSet.has("main-attachment")) {
        newSet.delete("main-attachment");
      } else {
        newSet.add("main-attachment");
      }
      return newSet;
    });
  };

  const toggleConversationAttachment = (messageId: string) => {
    setShownAttachments((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(messageId)) {
        newSet.delete(messageId);
      } else {
        newSet.add(messageId);
      }
      return newSet;
    });
  };

  const handleStatusChange = (newStatus: string) => {
    setStatus(newStatus);
  };

  return (
    <Modal
      open={activeModal}
      onClose={handleModalClose}
      title="Feedback Details"
      noScroll={true}
      primaryAction={{
        content: "Submit",
        onAction: handleSubmitReply,
        disabled: isShopUnderFreePlan || !replyText || !status,
        loading: isPending,
      }}
    >
      <div className="negative-feedback-modal">
        <Modal.Section>
          <BlockStack gap={"0"}>
            <div className="modal-scroll">
              <BlockStack gap="400">
                {/* First Message from customer - recieving from feedback form */}
                {replyReview?.message && (
                  <Card roundedAbove="sm">
                    <BlockStack gap="200">
                      <InlineStack align="space-between">
                        <Text
                          as="h2"
                          variant="headingSm"
                        >
                          {replyReview?.name}
                        </Text>

                        <Badge
                          tone={
                            replyReview?.status === "unresolved"
                              ? "warning"
                              : replyReview?.status === "replied"
                                ? "info"
                                : "success"
                          }
                        >
                          {replyReview?.status}
                        </Badge>
                      </InlineStack>

                      <InlineStack align="space-between">
                        <Text
                          as="p"
                          variant="bodySm"
                          tone="subdued"
                        >
                          {replyReview?.email}
                        </Text>

                        <Text
                          as="p"
                          variant="bodySm"
                          tone="subdued"
                        >
                          {convertDateTime(replyReview?.created_at)}
                        </Text>
                      </InlineStack>

                      <Divider />

                      <Text as="p">{stripTags(replyReview?.message)}</Text>
                    </BlockStack>

                    {/* Main Attachment Section - Original feedback attachment */}
                    {replyReview?.hasAttachment && (
                      <Box paddingBlockStart="200">
                        <Button
                          icon={AttachmentIcon}
                          onClick={toggleMainAttachment}
                        >
                          {shownAttachments.has("main-attachment") ? "Hide Attachment" : "View Attachment"}
                        </Button>
                      </Box>
                    )}

                    {shownAttachments.has("main-attachment") && replyReview?.attachment && (
                      <Box paddingBlockStart="400">
                        <img
                          src={replyReview.attachment}
                          alt="attachment"
                          style={{ width: "60px", height: "60px", borderRadius: "5px", objectFit: "cover" }}
                        />
                      </Box>
                    )}
                  </Card>
                )}

                {/* Pro Plan Upgrade Banner - Shows for free plan users */}
                {isShopUnderFreePlan && (
                  <BlockStack>
                    <Banner
                      title="Responding to Negative Feedback is a PRO feature."
                      action={{
                        content: "Upgrade to TrustSync PRO",
                        onAction: () => {
                          navigate("/subscription");
                        },
                      }}
                      icon={LockIcon}
                      tone="warning"
                    >
                      <Text
                        as="p"
                        fontWeight="regular"
                      >
                        Upgrade to unlock full management capabilities, including handling feedback, tracking statuses,
                        and controlling customer interactions.
                      </Text>
                    </Banner>
                  </BlockStack>
                )}

                {/* Legacy Reply Section - For backwards compatibility with old reply format */}
                {replyReview?.reply && (
                  <Card
                    roundedAbove="sm"
                    background="bg-fill-secondary"
                  >
                    <BlockStack gap="200">
                      <InlineStack align="space-between">
                        <Text
                          as="h2"
                          variant="headingSm"
                        >
                          You
                        </Text>

                        <Badge
                          tone={
                            replyReview?.status === "unresolved"
                              ? "warning"
                              : replyReview?.status === "replied"
                                ? "info"
                                : "success"
                          }
                        >
                          {replyReview?.status}
                        </Badge>
                      </InlineStack>

                      <InlineStack align="end">
                        <Text
                          as="p"
                          variant="bodySm"
                          tone="subdued"
                        >
                          {convertDateTime(replyReview?.created_at)}
                        </Text>
                      </InlineStack>

                      <Divider borderColor="border-tertiary" />

                      <Text as="p">{stripTags(replyReview?.reply)}</Text>
                    </BlockStack>
                  </Card>
                )}

                {/* Conversation Thread - Modern conversation system with messages from both shop and customer */}
                {replyReview?.conversation &&
                  replyReview.conversation.map((reply: MessageDocument) => {
                    const isShop = reply?.from === "shop";
                    return (
                      <Card
                        key={reply?.messageId}
                        roundedAbove="sm"
                        background={isShop ? "bg-fill-secondary" : "bg-surface"}
                      >
                        <BlockStack gap="200">
                          <InlineStack align="space-between">
                            <Text
                              as="h2"
                              variant="headingSm"
                            >
                              {isShop ? "You" : replyReview?.name}
                            </Text>

                            <Badge
                              tone={
                                reply?.status === "unresolved"
                                  ? "warning"
                                  : reply?.status === "replied"
                                    ? "info"
                                    : "success"
                              }
                            >
                              {reply?.status}
                            </Badge>
                          </InlineStack>

                          <InlineStack align={isShop ? "end" : "space-between"}>
                            {reply?.from === "customer" ? (
                              <Text
                                as="p"
                                variant="bodySm"
                                tone="subdued"
                              >
                                {replyReview?.email}
                              </Text>
                            ) : (
                              ""
                            )}

                            <Text
                              as="p"
                              variant="bodySm"
                              tone="subdued"
                            >
                              {convertDateTime(reply?.created_at)}
                            </Text>
                          </InlineStack>

                          <Divider borderColor={isShop ? "border-tertiary" : "border-secondary"} />

                          <Text as="p">{reply?.message}</Text>

                          {reply?.attachments && reply?.attachments.length > 0 && (
                            <Box>
                              <Button
                                icon={AttachmentIcon}
                                onClick={() => toggleConversationAttachment(reply.messageId)}
                              >
                                {shownAttachments.has(reply.messageId) ? "Hide Attachments" : `View Attachment`}
                              </Button>
                            </Box>
                          )}

                          {shownAttachments.has(reply.messageId) && (
                            <Box paddingBlockStart="300">
                              <InlineStack
                                gap="200"
                                wrap={false}
                              >
                                {/* Display multiple attachments if available */}
                                {reply?.attachments &&
                                  reply?.attachments.length > 0 &&
                                  reply.attachments.map((attachmentUrl, index) => (
                                    <img
                                      key={`${reply.messageId}-attachment-${index}`}
                                      src={attachmentUrl}
                                      alt={`attachment-${index + 1}`}
                                      style={{ width: "60px", height: "60px", borderRadius: "5px", objectFit: "cover" }}
                                    />
                                  ))}
                              </InlineStack>
                            </Box>
                          )}
                        </BlockStack>
                      </Card>
                    );
                  })}
              </BlockStack>
            </div>

            <div className="sticky-editor">
              {/* Rich Text Editor Section - For composing new replies */}
              <BlockStack>
                <Suspense fallback={<h2>Loading...</h2>}>
                  <EditorWrapper
                    emailHTML={reply}
                    setEmailHTML={(value) => setReply(value)}
                  />
                </Suspense>
              </BlockStack>

              {/* Status Selection Section - Radio buttons for setting feedback status */}
              <BlockStack gap="100">
                <Text
                  as="h3"
                  variant="headingSm"
                  tone={isShopUnderFreePlan ? "disabled" : "base"}
                >
                  Status
                </Text>
                <InlineStack gap="400">
                  <RadioButton
                    label="Unresolved"
                    checked={status === "unresolved"}
                    id="unresolved"
                    name="accounts"
                    onChange={() => handleStatusChange("unresolved")}
                    disabled={isShopUnderFreePlan}
                  />
                  <RadioButton
                    label="Replied"
                    checked={status === "replied"}
                    id="replied"
                    name="accounts"
                    onChange={() => handleStatusChange("replied")}
                    disabled={isShopUnderFreePlan}
                  />
                  <RadioButton
                    label="Resolved"
                    checked={status === "resolved"}
                    id="resolved"
                    name="accounts"
                    onChange={() => handleStatusChange("resolved")}
                    disabled={isShopUnderFreePlan}
                  />
                </InlineStack>
              </BlockStack>
            </div>
          </BlockStack>
        </Modal.Section>
      </div>
    </Modal>
  );
}
