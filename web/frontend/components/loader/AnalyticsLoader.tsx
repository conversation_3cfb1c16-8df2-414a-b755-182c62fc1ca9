import { BlockStack, Box, Card, IndexTable, InlineGrid, InlineStack, Page, Text } from "@shopify/polaris";
import React from "react";
import Skeleton from "../skeleton/Skeleton";
import StatisticCard from "../skeleton/StatisticCard";

interface AnalyticsLoaderProps {
  rows?: number;
}

export default function AnalyticsLoader({ rows = 10 }: AnalyticsLoaderProps): React.ReactElement {
  const resourceName = {
    singular: "email",
    plural: "emails",
  };

  // Dynamically create an array of emails from 1 to 10
  const emails = Array.from({ length: rows }, (_, index) => index + 1);

  return (
    <Page
      title="Analytics"
      // backAction={{ url: "#", disabled: true }}
    >
      <Box
        paddingBlockStart="400"
        paddingBlockEnd="1000"
      >
        <BlockStack gap="400">
          <BlockStack gap="300">
            <InlineStack
              blockAlign="center"
              align="space-between"
            >
              <Text
                as="h3"
                variant="headingLg"
              >
                Email Statistics
              </Text>
              <InlineStack gap="300">
                <Skeleton style={{ height: 28, width: 100 }} />
                <Skeleton style={{ height: 28, width: 100 }} />
              </InlineStack>
            </InlineStack>
            <InlineGrid
              columns={{ sm: 1, md: 2, lg: 4 }}
              gap="400"
            >
              <StatisticCard content="All Emails Sent" />
              <StatisticCard content="Total Sent" />
              <StatisticCard content="Opened" />
              <StatisticCard content="Clicked" />
            </InlineGrid>
          </BlockStack>

          <Card padding="0">
            <Box padding="400">
              <InlineStack
                blockAlign="center"
                align="space-between"
              >
                <InlineStack
                  blockAlign="center"
                  gap="300"
                >
                  <Skeleton style={{ height: 28, width: 100 }} />
                  <Skeleton style={{ height: 28, width: 100 }} />
                </InlineStack>
                <Skeleton style={{ height: 28, width: 28 }} />
              </InlineStack>
            </Box>

            <IndexTable
              resourceName={resourceName}
              itemCount={emails.length}
              headings={[
                { title: "Name" },
                { title: "Email" },
                { title: "Date & Time" },
                { title: "Review Link" },
                { title: "Order Link" },
              ]}
              selectable={false}
              // indexed={false}
            >
              {emails.map((order, index) => {
                return (
                  <IndexTable.Row
                    id={String(index)}
                    key={index}
                    position={index}
                  >
                    <IndexTable.Cell>
                      <Box paddingBlock="300">
                        <Skeleton style={{ height: 10, width: 100 }} />
                      </Box>
                    </IndexTable.Cell>
                    <IndexTable.Cell>
                      <Skeleton style={{ height: 10, width: 100 }} />
                    </IndexTable.Cell>
                    <IndexTable.Cell>
                      <Skeleton style={{ height: 10, width: 100 }} />
                    </IndexTable.Cell>
                    <IndexTable.Cell>
                      <Skeleton style={{ height: 10, width: 100 }} />
                    </IndexTable.Cell>
                    <IndexTable.Cell>
                      <Skeleton style={{ height: 10, width: 100 }} />
                    </IndexTable.Cell>
                  </IndexTable.Row>
                );
              })}
            </IndexTable>
          </Card>
        </BlockStack>
      </Box>
    </Page>
  );
}
