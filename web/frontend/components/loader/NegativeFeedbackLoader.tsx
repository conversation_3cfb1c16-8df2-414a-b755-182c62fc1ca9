import { BlockStack, Box, Card, IndexTable, InlineStack, Page } from "@shopify/polaris";
import React from "react";
import Skeleton from "../skeleton/Skeleton";

interface NegativeFeedbackLoaderProps {
  rows?: number;
}

export default function NegativeFeedbackLoader({ rows = 6 }: NegativeFeedbackLoaderProps): React.ReactElement {
  const resourceName = {
    singular: "negative feedback",
    plural: "negative feedback",
  };

  // Dynamically create an array of feedbacks from 1 to rows
  const feedbacks = Array.from({ length: rows }, (_, index) => index + 1);

  return (
    <Page
      title="Negative Feedbacks"
      // backAction={{ url: "#", disabled: true }}
      fullWidth
    >
      <BlockStack gap="400">
        <Card padding="0">
          <Box padding="400">
            <InlineStack
              blockAlign="center"
              align="space-between"
            >
              <InlineStack
                blockAlign="center"
                gap="300"
              >
                <Skeleton style={{ height: 28, width: 100 }} />
                <Skeleton style={{ height: 28, width: 100 }} />
              </InlineStack>
              <Skeleton style={{ height: 28, width: 28 }} />
            </InlineStack>
          </Box>

          <IndexTable
            resourceName={resourceName}
            itemCount={feedbacks.length}
            headings={[
              { title: "Name" },
              { title: "Email" },
              { title: "Review" },
              { title: "Attachment" },
              { title: "Status" },
              { title: "Message" },
              { title: "Date" },
              { title: "Action" },
            ]}
            selectable={false}
            // indexed={false}
          >
            {feedbacks.map((_, index) => {
              return (
                <IndexTable.Row
                  id={String(index)}
                  key={index}
                  position={index}
                >
                  <IndexTable.Cell>
                    <Box paddingBlock="300">
                      <Skeleton style={{ height: 10, width: 100 }} />
                    </Box>
                  </IndexTable.Cell>
                  <IndexTable.Cell>
                    <Skeleton style={{ height: 10, width: 100 }} />
                  </IndexTable.Cell>
                  <IndexTable.Cell>
                    <Skeleton style={{ height: 10, width: 100 }} />
                  </IndexTable.Cell>
                  <IndexTable.Cell>
                    <Skeleton style={{ height: 10, width: 100 }} />
                  </IndexTable.Cell>
                  <IndexTable.Cell>
                    <Skeleton style={{ height: 10, width: 100 }} />
                  </IndexTable.Cell>
                  <IndexTable.Cell>
                    <Skeleton style={{ height: 10, width: 100 }} />
                  </IndexTable.Cell>
                  <IndexTable.Cell>
                    <Skeleton style={{ height: 10, width: 100 }} />
                  </IndexTable.Cell>
                  <IndexTable.Cell>
                    <Skeleton style={{ height: 10, width: 100 }} />
                  </IndexTable.Cell>
                </IndexTable.Row>
              );
            })}
          </IndexTable>
        </Card>
      </BlockStack>
    </Page>
  );
}
