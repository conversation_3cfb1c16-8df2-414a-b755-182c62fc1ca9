import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, InlineGrid, InlineStack, Page } from "@shopify/polaris";
import React from "react";
import Skeleton from "../skeleton/Skeleton";

export default function SubscriptionLoader(): React.ReactElement {
  const plans = [1, 2, 3];
  const items = [1, 2, 3, 4, 5, 6, 7, 8];

  return (
    <Page
      title="Subscription Plans"
      // backAction={{ url: "#", disabled: true }}
    >
      <BlockStack gap="400">
        <Card>
          <InlineStack gap={"200"}>
            <Skeleton style={{ width: 20, height: 20 }} />
            <Skeleton style={{ width: 150, height: 20 }} />
          </InlineStack>
        </Card>
        <Card>
          <BlockStack gap="400">
            <InlineStack
              gap="400"
              align="space-between"
            >
              <BlockStack gap="300">
                <Skeleton style={{ width: 100, height: 15 }} />
                <Skeleton style={{ width: 80, height: 10 }} />
              </BlockStack>
              <InlineStack
                gap="400"
                align="end"
                blockAlign="center"
              >
                <div style={{ display: "flex", flexDirection: "column", alignItems: "end", gap: 8 }}>
                  <Skeleton style={{ width: "100px", height: "20px" }} />
                </div>

                <Button
                  variant="primary"
                  disabled
                >
                  Choose
                </Button>
              </InlineStack>
            </InlineStack>
            <Divider />
            <InlineGrid
              columns={{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }}
              gap="100"
            >
              {items.map((item, index) => (
                <Box
                  key={index}
                  paddingBlock="100"
                >
                  <Skeleton style={{ width: Math.floor(Math.random() * 101) + 100, height: 10 }} />
                </Box>
              ))}
            </InlineGrid>
          </BlockStack>
        </Card>

        <div className="pricing-wrap">
          <Grid columns={{ xs: 1, sm: 1, md: 2, lg: 3 }}>
            {plans.map((item, index) => (
              <Grid.Cell key={index}>
                <Card>
                  <BlockStack gap="400">
                    <InlineStack
                      gap="400"
                      align="space-between"
                    >
                      <BlockStack gap="300">
                        <Skeleton style={{ width: 100, height: 15 }} />
                        <Skeleton style={{ width: 80, height: 10 }} />
                      </BlockStack>
                      <Skeleton style={{ width: "60px", height: "20px" }} />
                    </InlineStack>
                    <Divider />
                    <BlockStack gap="100">
                      {items.map((item, index) => (
                        <Box
                          key={index}
                          paddingBlock="100"
                        >
                          <Skeleton style={{ width: Math.floor(Math.random() * 101) + 100, height: 10 }} />
                        </Box>
                      ))}
                    </BlockStack>
                    <BlockStack gap="400">
                      <Button
                        variant="primary"
                        disabled
                        fullWidth
                      >
                        Choose
                      </Button>
                    </BlockStack>
                  </BlockStack>
                </Card>
              </Grid.Cell>
            ))}
          </Grid>
        </div>

        <Card>
          <BlockStack gap="400">
            <InlineStack
              gap="400"
              align="space-between"
            >
              <BlockStack gap="300">
                <Skeleton style={{ width: 100, height: 15 }} />
                <Skeleton style={{ width: 80, height: 10 }} />
              </BlockStack>
              <InlineStack
                gap="400"
                align="end"
                blockAlign="center"
              >
                <Button
                  variant="primary"
                  disabled
                >
                  Continue With Free Plan
                </Button>
              </InlineStack>
            </InlineStack>
            <Divider />
            <InlineGrid
              columns={{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }}
              gap="100"
            >
              {items.map((item, index) => (
                <Box
                  key={index}
                  paddingBlock="100"
                >
                  <Skeleton style={{ width: Math.floor(Math.random() * 101) + 100, height: 10 }} />
                </Box>
              ))}
            </InlineGrid>
          </BlockStack>
        </Card>
      </BlockStack>
    </Page>
  );
}
