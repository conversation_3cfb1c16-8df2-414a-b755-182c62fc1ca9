import React from "react";
import { useNavigate } from "react-router-dom";

import { BlockStack, Box, Page } from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";

import EmailAnalyticsOverview from "../components/analytics/EmailAnalyticsOverview";
import SettingEmailList from "../components/analytics/SettingEmailList";

import { useAppQuery } from "storeware-tanstack-query";
import queryKeys from "../../app/enums/queryKeys";
import { fetchEmailAnalyticsInfo } from "../apis/analytics.api";
import { formatDate } from "../utils/helper";

// Type definitions for analytics page
interface AnalyticsPayload {
  q: string;
  platform: string;
  since: string;
  until: string;
  selected: number;
}

interface AnalyticsStates {
  totalPending: number;
  totalSent: number;
  totalOpen: number;
  totalClick: number;
}

interface AnalyticsPagination {
  page: number;
  pageSize: number;
  totalItems: number;
}

export default function AnalyticsPage(): React.ReactElement {
  const navigate = useNavigate();

  const [emails, setEmails] = useState<any[]>([]);
  const [states, setStates] = useState<AnalyticsStates>({
    totalPending: 0,
    totalSent: 0,
    totalOpen: 0,
    totalClick: 0,
  });
  const [selected, setSelected] = useState<number>(0);

  const [payload, setPayload] = useState<AnalyticsPayload>({
    q: "",
    platform: "",
    since: "",
    until: "",
    selected: 0,
  });

  const [pagination, setPagination] = useState<AnalyticsPagination>({
    page: 1,
    pageSize: 10,
    totalItems: 0,
  });

  const { data, isLoading } = useAppQuery({
    queryKey: [queryKeys.FETCH_ANALYTICS, payload, pagination.page, pagination.pageSize],
    queryFn: () => fetchEmailAnalyticsInfo({ ...payload, page: pagination.page, pageSize: pagination.pageSize }),
    reactQueryOptions: {
      enabled: true,
      staleTime: 0,
      refetchInterval: 60000,
    },
  });

  useEffect(() => {
    if (data) {
      setEmails(data.emails);
      setStates(data.states);
      setPagination(data.pagination);
    }
  }, [data]);

  useEffect(() => {
    const today = new Date();
    const since = new Date();
    since.setDate(today.getDate() - 30);

    setPayload((prev) => ({
      ...prev,
      since: formatDate(since),
      until: formatDate(today),
    }));
  }, []);

  const handlePayloadUpdate = (obj: Partial<AnalyticsPayload>): void => {
    setPayload((prev) => ({ ...prev, ...obj }));
  };

  return (
    <Page
      title="Analytics"
      backAction={{
        id: "dashboard",
        content: "Dashboard",
        onAction: () => {
          navigate("/");
        },
      }}
    >
      <Box
        paddingBlockStart="400"
        paddingBlockEnd="1000"
      >
        <BlockStack gap="400">
          <EmailAnalyticsOverview
            states={states}
            handlePayloadUpdate={handlePayloadUpdate}
          />

          <SettingEmailList
            isLoading={isLoading}
            emails={emails}
            selected={selected}
            setSelected={setSelected}
            pagination={pagination}
            setPagination={setPagination}
            payload={payload}
            handlePayloadUpdate={handlePayloadUpdate}
          />
        </BlockStack>
      </Box>
    </Page>
  );
}
