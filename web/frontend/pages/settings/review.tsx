import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { v4 as uuidv4 } from "uuid";

import {
  Banner,
  BlockStack,
  Box,
  Button,
  Card,
  Checkbox,
  Icon,
  InlineStack,
  Link,
  Page,
  Select,
  Text,
  TextField,
} from "@shopify/polaris";

import { DeleteIcon, InfoIcon, PlusIcon } from "@shopify/polaris-icons";

import ProPlanLink from "../../components/common/ProPlanLink.jsx";

import { useAppQuery, useMutation, useQueryClient } from "storeware-tanstack-query";
import queryKeys from "../../../app/enums/queryKeys";
import { fetchSettings, saveReviewSettings } from "../../apis/settings.api";
import ContextualSaveBar from "../../components/common/ContextualSaveBar.jsx";
import PageLoader from "../../components/loader/PageLoader.jsx";
import useUnsavedChanges from "../../hooks/useUnsavedChanges";
import { useAppContext } from "../../providers/AppProvider";
import { autoPublishOptions, percentageReviewOptions, reviewLinkOptions } from "../../utils/config.js";
import { demoLink } from "../../utils/demo-link.js";
import { helpTextForAutoPublish, showNotification } from "../../utils/helper";

// Type definitions for review settings
interface ReviewLink {
  id: string;
  platform: string;
  url: string;
  percentage: string;
}

interface ReviewFormData {
  autoPublish?: number;
  reviewLinks?: ReviewLink[];
  customersPick?: boolean;
  customNegativeForm?: boolean;
  customNegativeLink?: string;
}

interface ValidationMessage {
  autoPublish?: string;
  reviewLinks?: string;
  customNegativeLink?: string;
}

export default function ReviewSettings(): React.ReactElement {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const { isShopUnderProPlan } = useAppContext();

  // Using language directly from settings in the API call
  const language: string = "english";

  const [totalPercentage, setTotalPercentage] = useState<number>(0);
  const [reviewLinkErrorMessage, setReviewLinkErrorMessage] = useState<string>("");

  const [formData, setFormData] = useState<ReviewFormData>({});
  const [originalFormData, setOriginalFormData] = useState<ReviewFormData>({});
  const [validationMessage, setValidationMessage] = useState<ValidationMessage>({});

  const handleAddReview = (): void => {
    setReviewLinkErrorMessage("");
    const currentLinks = formData.reviewLinks || [];

    if (currentLinks.length == 0) {
      setFormData({
        ...formData,
        reviewLinks: [
          {
            id: uuidv4(),
            platform: reviewLinkOptions.filter(
              (x) => !currentLinks.map((review) => review.platform).includes(x.value)
            )[0].value,
            url: "",
            percentage: "100%",
          },
        ],
      });
    } else if (currentLinks.length == 1) {
      setFormData({
        ...formData,
        reviewLinks: [
          { ...currentLinks[0], percentage: "50%" },
          {
            id: uuidv4(),
            platform: reviewLinkOptions.filter(
              (x) => !currentLinks.map((review) => review.platform).includes(x.value)
            )[0].value,
            url: "",
            percentage: "50%",
          },
        ],
      });
    } else if (currentLinks.length == 2) {
      setFormData({
        ...formData,
        reviewLinks: [
          { ...currentLinks[0], percentage: "60%" },
          { ...currentLinks[1], percentage: "20%" },
          {
            id: uuidv4(),
            platform: reviewLinkOptions.filter(
              (x) => !currentLinks.map((review) => review.platform).includes(x.value)
            )[0].value,
            url: "",
            percentage: "20%",
          },
        ],
      });
    } else {
      setFormData({
        ...formData,
        reviewLinks: [
          ...currentLinks,
          {
            id: uuidv4(),
            platform: reviewLinkOptions.filter(
              (x) => !currentLinks.map((review) => review.platform).includes(x.value)
            )[0].value,
            url: "",
            percentage: "20%",
          },
        ],
      });
    }
  };

  const handleChangeURL = (id: string, url: string): void => {
    const currentLinks: ReviewLink[] = formData.reviewLinks || [];
    const newReviewLinks: ReviewLink[] = currentLinks.map((review) => {
      if (review.id === id) {
        return { ...review, url };
      }
      return review;
    });
    setFormData({
      ...formData,
      reviewLinks: newReviewLinks,
    });
  };

  const handleChangePercentage = (id: string, percentage: string): void => {
    const currentLinks: ReviewLink[] = formData.reviewLinks || [];
    const newReviewLinks: ReviewLink[] = currentLinks.map((review) => {
      if (review.id === id) {
        return { ...review, percentage };
      }
      return review;
    });
    setFormData({
      ...formData,
      reviewLinks: newReviewLinks,
    });
  };

  const handleChangePlatform = (id: string, platform: string): void => {
    const currentLinks: ReviewLink[] = formData.reviewLinks || [];
    const newReviewLinks: ReviewLink[] = currentLinks.map((review) => {
      if (review.id === id) {
        return { ...review, platform };
      }
      return review;
    });
    setFormData({
      ...formData,
      reviewLinks: newReviewLinks,
    });
  };

  const handleRemoveLink = (id: string): void => {
    const currentLinks: ReviewLink[] = formData.reviewLinks || [];

    if (currentLinks.length == 2) {
      const newReviewLinks: ReviewLink[] = currentLinks.filter((review) => review.id !== id);
      newReviewLinks[0].percentage = "100%";
      setFormData({
        ...formData,
        reviewLinks: newReviewLinks,
      });
    } else if (currentLinks.length == 3) {
      const newReviewLinks: ReviewLink[] = currentLinks.filter((review) => review.id !== id);
      newReviewLinks[0].percentage = "60%";
      newReviewLinks[1].percentage = "40%";
      setFormData({
        ...formData,
        reviewLinks: newReviewLinks,
      });
    } else if (currentLinks.length == 4) {
      const newReviewLinks: ReviewLink[] = currentLinks.filter((review) => review.id !== id);
      newReviewLinks[0].percentage = "50%";
      newReviewLinks[1].percentage = "30%";
      newReviewLinks[2].percentage = "20%";
      setFormData({
        ...formData,
        reviewLinks: newReviewLinks,
      });
    } else {
      const newReviewLinks: ReviewLink[] = currentLinks.filter((review) => review.id !== id);
      setFormData({
        ...formData,
        reviewLinks: newReviewLinks,
      });
    }
  };

  const { hasUnsavedChanges, DiscardChangesModal, showDiscardModal } = useUnsavedChanges({
    originalData: originalFormData,
    currentData: formData,
    onDiscardAction: () => {
      setFormData(originalFormData);
    },
  });

  const { data: { settings } = {}, isLoading } = useAppQuery({
    queryKey: [queryKeys.FETCH_SETTING],
    queryFn: fetchSettings,
    reactQueryOptions: {
      enabled: true,
      staleTime: 0,
    },
  });

  const { mutate: handleReviewSettings, isPending: isSaving } = useMutation({
    mutationFn: () => saveReviewSettings({ ...formData, language }),
    onSuccess: (response) => {
      if (response.status === 422) {
        setValidationMessage(response.errors);
        setReviewLinkErrorMessage(response?.errors?.reviewLinks || "");
      } else if (response.status === 200) {
        queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_SETTING] });

        showNotification({ message: "Settings updated" });
      }
    },
    onError: (error) => {
      console.log(error);
    },
  });

  useEffect(() => {
    if (!formData.reviewLinks) return;

    let percentage = 0;
    for (const reviewLink of formData.reviewLinks) {
      if (reviewLink.percentage) {
        percentage += parseInt(reviewLink.percentage, 10);
      }
    }

    setReviewLinkErrorMessage("");
    setTotalPercentage(percentage);

    if (percentage !== 100) {
      setReviewLinkErrorMessage("The total of all percentages must equal 100%");
    }
  }, [formData.reviewLinks]);

  useEffect(() => {
    if (settings) {
      const baseFormData = {
        autoPublish: settings?.autoPublish || 4,
        reviewLinks: settings?.reviewLinks || [],
        customersPick: settings?.customersPick || false,
        customNegativeForm: settings?.customNegativeForm || false,
        customNegativeLink: settings?.customNegativeLink || "",
      };
      setFormData(baseFormData as ReviewFormData);
      setOriginalFormData(baseFormData as ReviewFormData);
    }
  }, [settings]);

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <Page
      title="Review Settings"
      backAction={{
        id: "dashboard",
        content: "Dashboard",
        onAction: () => {
          navigate("/");
        },
      }}
    >
      <Box paddingBlockEnd="800">
        <BlockStack gap="300">
          {/* Auto Publish and Review Links */}
          <Card>
            <Select
              label="Auto-publish new reviews"
              options={autoPublishOptions}
              value={formData?.autoPublish ? String(formData?.autoPublish) : ""}
              onChange={(value: string) => {
                setFormData((prevFormData) => ({
                  ...prevFormData,
                  autoPublish: parseInt(value),
                }));
              }}
              helpText={
                !validationMessage?.autoPublish && (
                  <Text as="p">
                    {helpTextForAutoPublish(formData?.autoPublish ? formData.autoPublish : "")} star ratings will be
                    directed to an online form (
                    <a
                      href="https://review.trustsync.io/review/632885f2a61eb87e5c19e12a?rating=1"
                      target="_blank"
                    >
                      view example
                    </a>
                    ). Nobody except you will see such negative feedback.
                  </Text>
                )
              }
              error={validationMessage?.autoPublish}
            />
          </Card>

          {/* review platforms and links */}
          {formData?.reviewLinks?.length ? (
            <Card>
              <BlockStack gap="300">
                {formData?.reviewLinks.map((link, index) => {
                  return (
                    <Card
                      key={index}
                      background="bg-surface-hover"
                    >
                      <BlockStack gap="300">
                        <TextField
                          label={`Review link #${index + 1}`}
                          type="text"
                          value={link.url}
                          onChange={(value: string) => handleChangeURL(link.id, value)}
                          placeholder={`https://www.${link.platform}.com/review/...`}
                          autoComplete="off"
                          connectedLeft={
                            <div style={{ minWidth: 140 }}>
                              {/* Platform select dropdown */}
                              <Select
                                label=""
                                options={reviewLinkOptions.filter((option) => {
                                  return (
                                    option.value == link.platform ||
                                    !(formData.reviewLinks || [])
                                      .filter((review) => review.id !== link.id)
                                      .map((review) => review.platform)
                                      .includes(option.value)
                                  );
                                })}
                                onChange={(value: string) => handleChangePlatform(link.id, value)}
                                value={link.platform}
                                labelHidden
                              />
                            </div>
                          }
                          connectedRight={
                            <Button
                              icon={DeleteIcon}
                              onClick={() => handleRemoveLink(link.id)}
                            />
                          }
                          helpText={
                            demoLink[link.platform] &&
                            demoLink[link.platform].length > 0 && (
                              <Text as="span">
                                <Link
                                  url={demoLink[link?.platform]}
                                  target="_blank"
                                >
                                  Click here
                                </Link>{" "}
                                to know where to find your <Text as="strong">{link.platform}</Text> link.
                              </Text>
                            )
                          }
                        />

                        <Select
                          label="Request review on this website"
                          options={percentageReviewOptions}
                          onChange={(value: string) => handleChangePercentage(link.id, value)}
                          value={link.percentage}
                        />
                      </BlockStack>
                    </Card>
                  );
                })}
                {/* Validation error */}
                {reviewLinkErrorMessage && (
                  <InlineStack
                    gap="200"
                    wrap={false}
                    blockAlign="start"
                  >
                    <Box>
                      <Icon
                        source={InfoIcon}
                        tone="textCritical"
                      ></Icon>
                    </Box>
                    <Text
                      as="p"
                      tone="critical"
                    >
                      {reviewLinkErrorMessage}
                    </Text>
                  </InlineStack>
                )}

                <InlineStack
                  align="end"
                  gap="300"
                >
                  <Button
                    icon={PlusIcon}
                    onClick={handleAddReview}
                  >
                    Add review link
                  </Button>
                </InlineStack>
              </BlockStack>
            </Card>
          ) : (
            <BlockStack gap="400">
              <Banner
                tone="warning"
                title="No review links added!"
              >
                <Text
                  as="p"
                  fontWeight="regular"
                >
                  You don't have any review links added. Please add at least one review link.
                </Text>
              </Banner>

              <InlineStack
                align="end"
                gap="300"
              >
                <Button
                  onClick={handleAddReview}
                  icon={PlusIcon}
                >
                  Add review link
                </Button>
              </InlineStack>
            </BlockStack>
          )}

          {/*  Customers Pick and Custom Negatvie Form */}
          <Card>
            <InlineStack gap="200">
              <Checkbox
                label={
                  <Text as="p">
                    Let customers pick where they leave a review {!isShopUnderProPlan && <ProPlanLink />}
                  </Text>
                }
                checked={formData?.customersPick}
                onChange={(value: boolean) => {
                  // setFormData({ ...formData, customersPick: !!value });
                  setFormData((prevFormData) => ({
                    ...prevFormData,
                    customersPick: !!value,
                  }));
                }}
                disabled={!isShopUnderProPlan}
              />
            </InlineStack>

            <InlineStack gap="200">
              <Checkbox
                label={<Text as="p">Custom negative feedback form {!isShopUnderProPlan && <ProPlanLink />}</Text>}
                checked={formData?.customNegativeForm}
                onChange={(value: boolean) => {
                  setFormData((prevFormData) => ({
                    ...prevFormData,
                    customNegativeForm: !!value,
                  }));
                }}
                disabled={!isShopUnderProPlan}
              />
            </InlineStack>

            {/* showing custom negative form field */}
            {(formData?.customNegativeForm || !isShopUnderProPlan) && (
              <BlockStack>
                <TextField
                  label={<Text as="p">Custom negative feedback form</Text>}
                  labelHidden
                  type="text"
                  value={formData?.customNegativeLink}
                  onChange={(value: string) => {
                    setFormData((prevFormData) => ({
                      ...prevFormData,
                      customNegativeLink: value,
                    }));
                  }}
                  autoComplete="off"
                  placeholder="https://www.trustsync.io/review/..."
                  error={validationMessage?.customNegativeLink}
                  disabled={!isShopUnderProPlan}
                />
              </BlockStack>
            )}
          </Card>

          {/* Save changes button */}
          <InlineStack align="end">
            <Button
              variant="primary"
              onClick={handleReviewSettings}
              loading={isSaving}
              disabled={totalPercentage !== 100 || !(formData.reviewLinks || []).length || !hasUnsavedChanges}
            >
              Save changes
            </Button>
          </InlineStack>
        </BlockStack>
      </Box>

      <DiscardChangesModal />

      <ContextualSaveBar
        id="review-settings"
        open={hasUnsavedChanges}
        isLoading={isSaving}
        onSave={handleReviewSettings}
        onDiscard={showDiscardModal}
        disabled={totalPercentage !== 100}
      />
    </Page>
  );
}
