import React, { useCallback, useState } from "react";
import { useNavigate } from "react-router-dom";

import { Page, Tabs } from "@shopify/polaris";

import EmailDesign from "../../components/email-settings/EmailDesign";

import settingItemList from "../../utils/email-setting-tabs";

export default function EmailSettingPage(): React.ReactElement {
  const navigate = useNavigate();

  const handleSettingTabItemSwitch = useCallback((selectedTabIndex: number) => {
    const switchSettingItem = settingItemList.find((item, index) => {
      return selectedTabIndex === index;
    });

    const routePath: string = switchSettingItem?.route || 'settings';
    navigate(`/${routePath}`);
  }, [navigate]);

  return (
    <Page
      title="Email Configurations"
      backAction={{
        id: "dashboard",
        content: "Dashboard",
        onAction: () => {
          navigate('/');
        }
      }}
    >
      <Tabs
        tabs={settingItemList}
        selected={0}
        onSelect={handleSettingTabItemSwitch}
      >
        <EmailDesign />
      </Tabs>
    </Page>
  );
}