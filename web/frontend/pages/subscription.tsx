import React, { useState } from "react";
import { useNavigate } from "react-router-dom";

import { Banner, BlockStack, Box, Card, Icon, InlineGrid, InlineStack, Link, Page, Text } from "@shopify/polaris";
import { InfoIcon } from "@shopify/polaris-icons";

import SubscriptionLoader from "../components/loader/SubscriptionLoader";
import CancelSubscriptionModal from "../components/subscription/CancelSubscriptionModal";
import FullWidthPriceCard from "../components/subscription/FullWidthPriceCard";
import PriceCard from "../components/subscription/PriceCard";

import { useAppQuery } from "storeware-tanstack-query";
import queryKeys from "../../app/enums/queryKeys";
import { getPlan, getProPlan } from "../../app/utils/plans";
import { fetchSubscription } from "../apis/subscription.api";
import { useAppContext } from "../providers/AppProvider";
import { capitalizeFirstLetter } from "../utils/helper";

const freePlan = getPlan("free");
const enterprisePlan = getPlan("enterprise");
const packages = getProPlan();

const updatedPlanNames: string[] = ["free", "basic", "pro", "elite", "enterprise"];

export default function Subscription(): React.ReactElement {
  const { isShopUnderProPlan } = useAppContext();
  const [showCancelModal, setShowCancelModal] = useState<boolean>(false);

  const navigate = useNavigate();

  const { data: { shop, subscription } = {}, isLoading } = useAppQuery({
    queryKey: [queryKeys.FETCH_SUBSCRIPTION],
    queryFn: fetchSubscription,
    reactQueryOptions: {
      enabled: true,
      staleTime: 0,
    },
  });

  const isUpdatedPlan: boolean = subscription?.plan && updatedPlanNames.includes(subscription?.plan);

  if (isLoading) {
    return <SubscriptionLoader />;
  }

  return (
    <Page
      title="Subscription Plans"
      backAction={{
        id: "dashboard",
        content: "Dashboard",
        onAction: () => navigate("/"),
      }}
    >
      <Box paddingBlockEnd="800">
        <BlockStack gap="400">
          {!isUpdatedPlan && (
            <Card>
              <InlineStack
                gap="200"
                align="start"
                blockAlign="center"
              >
                <Box>
                  <Icon source={InfoIcon} />
                </Box>
                <Box>
                  You are on the <b>{capitalizeFirstLetter(subscription?.plan)}</b> plan
                </Box>
              </InlineStack>
            </Card>
          )}

          {/* <Banner
            title={
              <Text as="p">
                You are on the <b>{capitalizeFirstLetter(subscription?.plan)}</b> plan
              </Text>
            }
            tone="info"
          /> */}

          {freePlan && (
            <FullWidthPriceCard
              plan={freePlan}
              subscription={subscription}
              shop={shop}
            />
          )}

          <InlineGrid
            columns={3}
            gap="300"
          >
            {packages.map((plan, index) => (
              <PriceCard
                key={index}
                plan={plan}
                subscription={subscription}
              />
            ))}
          </InlineGrid>

          {enterprisePlan && (
            <FullWidthPriceCard
              plan={enterprisePlan}
              subscription={subscription}
              shop={shop}
            />
          )}

          {isShopUnderProPlan && (
            <>
              <InlineStack>
                <Text as="p">
                  <Link
                    monochrome={true}
                    // variant="monochromePlain"
                    onClick={() => setShowCancelModal(true)}
                  >
                    Click Here
                  </Link>{" "}
                  to <Text as="p">cancel your subscription plan</Text>
                </Text>
              </InlineStack>

              {showCancelModal && (
                <CancelSubscriptionModal
                  showCancelModal={showCancelModal}
                  setShowCancelModal={setShowCancelModal}
                />
              )}
            </>
          )}
        </BlockStack>
      </Box>
    </Page>
  );
}
