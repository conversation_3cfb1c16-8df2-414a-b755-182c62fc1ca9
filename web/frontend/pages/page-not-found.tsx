import { Card, EmptyState, Page, Text } from "@shopify/polaris";
import React from "react";
import { useNavigate } from "react-router-dom";

export default function PageNotFound(): React.ReactElement {
  const navigate = useNavigate();

  return (
    <Page>
      <Card>
        <EmptyState
          heading="There’s no page at this address"
          action={{
            content: "Go to dashboard",
            onAction: () => {
              navigate("/");
            },
          }}
          image={process.env.VITE_STORAGE_CDN_BASE_URL + "/assets/404.png"}
        >
          <Text
            as="p"
            variant="bodySm"
          >
            Check the URL and try again, or use the search bar to find what you need.
          </Text>
        </EmptyState>
      </Card>
    </Page>
  );
}
