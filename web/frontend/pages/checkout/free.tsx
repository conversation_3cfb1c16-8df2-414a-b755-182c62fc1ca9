import {
  <PERSON>leed,
  BlockStack,
  Box,
  <PERSON>ton,
  Card,
  Divider,
  InlineGrid,
  InlineStack,
  Link,
  Page,
  Text,
} from "@shopify/polaris";
import React from "react";

import { useState } from "react";
import { useNavigate } from "react-router-dom";
import queryKeys from "../../../app/enums/queryKeys";
import { getPlan } from "../../../app/utils/plans";
import { fetchSubscription, freePlanSubscription } from "../../apis/subscription.api";

import { useAppQuery, useQueryClient } from "storeware-tanstack-query";
import PageLoader from "../../components/loader/PageLoader";

const plan = getPlan("free");

export default function Checkout(): React.ReactElement {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // Load subscription
  const { data: { subscription } = {}, isLoading } = useAppQuery({
    queryKey: [queryKeys.FETCH_SUBSCRIPTION],
    queryFn: fetchSubscription,
    reactQueryOptions: {
      enabled: true,
      staleTime: 0,
    },
  });

  // Handle subscription
  const [isLoadingProgress, setLoadingrogress] = useState<boolean>(false);

  const handleToFreePlanSubscribtion = async (): Promise<void> => {
    try {
      setLoadingrogress(true);

      await freePlanSubscription();
    } catch (error) {
      console.log(error);
    } finally {
      setTimeout(() => {
        setLoadingrogress(false);

        queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_SHOP_INFO] });
        queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_SUBSCRIPTION] });

        navigate("/");
      }, 4000);
    }
  };

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <Page
      title="Checkout"
      backAction={{
        id: "subscription",
        content: "Subscription",
        onAction: () => {
          navigate("/subscription");
        },
      }}
    >
      <InlineGrid
        columns={["twoThirds", "oneThird"]}
        gap="400"
      >
        <Card>
          <InlineStack
            align="space-between"
            blockAlign="center"
          >
            <Text
              as="h3"
              variant="headingSm"
            >
              Plan Details
            </Text>
            <Link onClick={() => navigate("/subscription")}>Change Plan</Link>
          </InlineStack>
          <Bleed marginInline="400">
            <Box paddingBlock="300">
              <Divider />
            </Box>
          </Bleed>
          <Box>
            <BlockStack gap="400">
              <BlockStack gap="200">
                <Text
                  as="h3"
                  variant="headingMd"
                >
                  Plan Name
                </Text>
                <Text
                  as="p"
                  variant="bodySm"
                  tone="subdued"
                >
                  Free
                </Text>
              </BlockStack>

              <BlockStack gap="200">
                <Text
                  as="h3"
                  variant="headingMd"
                >
                  Plan Type
                </Text>
                <Text
                  as="p"
                  variant="bodySm"
                  tone="subdued"
                >
                  Free
                </Text>
              </BlockStack>

              <BlockStack gap="200">
                <Text
                  as="h3"
                  variant="headingMd"
                >
                  Plan Interval
                </Text>
                <Text
                  as="p"
                  variant="bodySm"
                  tone="subdued"
                >
                  Monthly
                </Text>
              </BlockStack>

              <BlockStack gap="200">
                <Text
                  as="h3"
                  variant="headingMd"
                >
                  Duration
                </Text>
                <Text
                  as="p"
                  variant="bodySm"
                  tone="subdued"
                >
                  1 Month
                </Text>
              </BlockStack>

              <BlockStack gap="200">
                <Text
                  as="h3"
                  variant="headingMd"
                >
                  Plan Description
                </Text>
                <Text
                  as="p"
                  variant="bodySm"
                  fontWeight="semibold"
                  tone="subdued"
                >
                  {subscription
                    ? `Plan upgrade from ${subscription.plan.toUpperCase()} to ${plan?.name.toUpperCase()}`
                    : `Plan subscribe to ${plan?.name.toUpperCase()}`}
                  .
                </Text>
              </BlockStack>
            </BlockStack>
          </Box>
        </Card>
        <Box
          background="bg-surface"
          padding="400"
          borderRadius="300"
          shadow="100"
          borderColor="border-brand"
          borderWidth="0165"
          borderStyle="solid"
        >
          <div style={{ height: "100%", display: "flex", flexDirection: "column" }}>
            <div style={{ flex: 1 }}>
              <InlineStack
                align="space-between"
                blockAlign="center"
              >
                <Text
                  as="h3"
                  variant="headingSm"
                >
                  Pricing
                </Text>
              </InlineStack>
              <Bleed marginInline="400">
                <Box paddingBlock="300">
                  <Divider />
                </Box>
              </Bleed>
              <Box>
                <BlockStack gap="400">
                  <InlineStack
                    gap="200"
                    align="space-between"
                  >
                    <Text
                      as="h3"
                      variant="headingMd"
                    >
                      Plan Name
                    </Text>
                    <Text
                      as="h3"
                      variant="headingMd"
                    >
                      {plan?.name.toUpperCase()}
                    </Text>
                  </InlineStack>

                  <InlineStack
                    gap="200"
                    align="space-between"
                  >
                    <Text
                      as="h3"
                      variant="headingMd"
                    >
                      Duration
                    </Text>
                    <Text
                      as="h3"
                      variant="headingMd"
                    >
                      1 month
                    </Text>
                  </InlineStack>

                  <InlineStack
                    gap="200"
                    align="space-between"
                  >
                    <Text
                      as="h3"
                      variant="headingMd"
                    >
                      Sub Total
                    </Text>
                    <Text
                      as="h3"
                      variant="headingMd"
                    >
                      ${plan?.discountPrice}
                    </Text>
                  </InlineStack>
                </BlockStack>
              </Box>
            </div>
            <Bleed marginInline="400">
              <Box paddingBlock="300">
                <Divider />
              </Box>
            </Bleed>
            <BlockStack gap="300">
              <InlineStack
                gap="200"
                align="space-between"
              >
                <Text
                  as="h3"
                  variant="headingSm"
                >
                  Total
                </Text>
                <Text
                  as="h3"
                  variant="headingSm"
                >
                  ${plan?.discountPrice}
                </Text>
              </InlineStack>
              <Button
                variant="primary"
                onClick={handleToFreePlanSubscribtion}
                fullWidth
                loading={isLoadingProgress}
              >
                Checkout
              </Button>
            </BlockStack>
          </div>
        </Box>
      </InlineGrid>
    </Page>
  );
}
