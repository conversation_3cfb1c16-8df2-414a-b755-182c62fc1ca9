import {
  <PERSON>leed,
  BlockStack,
  Box,
  <PERSON>ton,
  Card,
  Divider,
  InlineGrid,
  InlineStack,
  Link,
  Page,
  Text,
} from "@shopify/polaris";
import React, { useState } from "react";

import { useNavigate, useParams } from "react-router-dom";

import { roundToTwoDigits } from "../../utils/helper";

import { useAppQuery } from "storeware-tanstack-query";
import queryKeys from "../../../app/enums/queryKeys";
import { getPlan } from "../../../app/utils/plans";
import { fetchSubscription, proPlanSubscription } from "../../apis/subscription.api";

import PageLoader from "../../components/loader/PageLoader";
import PageNotFound from "../page-not-found";

export default function Checkout(): React.ReactElement {
  const navigate = useNavigate();
  const { slug } = useParams<{ slug: string }>();

  //   Load subscription
  const { data: { subscription } = {}, isLoading } = useAppQuery({
    queryKey: [queryKeys.FETCH_SUBSCRIPTION],
    queryFn: fetchSubscription,
    reactQueryOptions: {
      enabled: true,
      staleTime: 0,
    },
  });

  // Handle subscription
  const [isLoadingProgress, setLoadingProgress] = useState<boolean>(false);

  const gotoCheckoutPage = async (): Promise<void> => {
    try {
      setLoadingProgress(true);

      const { data } = await proPlanSubscription({ slug: slug as string });

      if (data?.url) {
        window.open(data?.url, "_parent");
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoadingProgress(false);
    }
  };

  const plan = getPlan(slug as string);
  if (!plan) {
    return <PageNotFound />;
  }

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <Page
      title="Checkout"
      backAction={{
        id: "dashboard",
        content: "Dashboard",
        onAction: () => {
          navigate("/subscription");
        },
      }}
    >
      <InlineGrid
        columns={["twoThirds", "oneThird"]}
        gap="400"
      >
        <Card>
          <InlineStack
            align="space-between"
            blockAlign="center"
          >
            <Text
              as="h3"
              variant="headingSm"
            >
              Plan Details
            </Text>
            <Link onClick={() => navigate("/subscription")}>Change Plan</Link>
          </InlineStack>
          <Bleed marginInline="400">
            <Box paddingBlock="300">
              <Divider />
            </Box>
          </Bleed>
          <Box>
            <BlockStack gap="400">
              <BlockStack gap="200">
                <Text
                  as="h3"
                  variant="headingMd"
                >
                  Plan Name
                </Text>
                <Text
                  as="p"
                  variant="bodySm"
                  tone="subdued"
                >
                  {plan.name.toUpperCase()}
                </Text>
              </BlockStack>

              <BlockStack gap="200">
                <Text
                  as="h3"
                  variant="headingMd"
                >
                  Plan Type
                </Text>
                <Text
                  as="p"
                  variant="bodySm"
                  tone="subdued"
                >
                  {plan.slug === "free" ? "Free" : "Pro"}
                </Text>
              </BlockStack>

              <BlockStack gap="200">
                <Text
                  as="h3"
                  variant="headingMd"
                >
                  Plan Interval
                </Text>
                <Text
                  as="p"
                  variant="bodySm"
                  tone="subdued"
                >
                  Monthly
                </Text>
              </BlockStack>

              <BlockStack gap="200">
                <Text
                  as="h3"
                  variant="headingMd"
                >
                  Duration
                </Text>
                <Text
                  as="p"
                  variant="bodySm"
                  tone="subdued"
                >
                  1 Month
                </Text>
              </BlockStack>

              <BlockStack gap="200">
                <Text
                  as="h3"
                  variant="headingMd"
                >
                  Plan Description
                </Text>
                <Text
                  as="p"
                  variant="bodySm"
                  fontWeight="semibold"
                  tone="subdued"
                >
                  Plan upgrade from "{subscription.plan.toUpperCase()}" to "{plan.name.toUpperCase()}".
                </Text>
              </BlockStack>
            </BlockStack>
          </Box>
        </Card>
        <Box
          background="bg-surface"
          padding="400"
          borderRadius="300"
          shadow="100"
          borderColor="border-brand"
          borderWidth="0165"
          borderStyle="solid"
        >
          <div style={{ height: "100%", display: "flex", flexDirection: "column" }}>
            <div style={{ flex: 1 }}>
              <InlineStack
                align="space-between"
                blockAlign="center"
              >
                <Text
                  as="h3"
                  variant="headingSm"
                >
                  Pricing
                </Text>
              </InlineStack>
              <Bleed marginInline="400">
                <Box paddingBlock="300">
                  <Divider />
                </Box>
              </Bleed>
              <Box>
                <BlockStack gap="400">
                  <InlineStack
                    gap="200"
                    align="space-between"
                  >
                    <Text
                      as="h3"
                      variant="headingMd"
                    >
                      Plan Name
                    </Text>
                    <Text
                      as="h3"
                      variant="headingMd"
                    >
                      {plan.name.toUpperCase()}
                    </Text>
                  </InlineStack>

                  <InlineStack
                    gap="200"
                    align="space-between"
                  >
                    <Text
                      as="h3"
                      variant="headingMd"
                    >
                      Duration
                    </Text>
                    <Text
                      as="h3"
                      variant="headingMd"
                    >
                      1 month
                    </Text>
                  </InlineStack>

                  <InlineStack
                    gap="200"
                    align="space-between"
                  >
                    <Text
                      as="h3"
                      variant="headingMd"
                    >
                      Price
                    </Text>
                    <Text
                      as="h3"
                      variant="headingMd"
                    >
                      ${plan.price}
                    </Text>
                  </InlineStack>

                  <InlineStack
                    gap="200"
                    align="space-between"
                  >
                    <Text
                      as="h3"
                      variant="headingMd"
                    >
                      Discount
                    </Text>
                    <Text
                      as="h3"
                      variant="headingMd"
                    >
                      ${roundToTwoDigits(plan.price - plan.discountPrice)}
                    </Text>
                  </InlineStack>
                </BlockStack>
              </Box>
            </div>
            <Bleed marginInline="400">
              <Box paddingBlock="300">
                <Divider />
              </Box>
            </Bleed>
            <BlockStack gap="300">
              <InlineStack
                gap="200"
                align="space-between"
              >
                <Text
                  as="h3"
                  variant="headingSm"
                >
                  Total
                </Text>
                <Text
                  as="h3"
                  variant="headingSm"
                >
                  ${plan.discountPrice}
                </Text>
              </InlineStack>
              <Button
                variant="primary"
                onClick={gotoCheckoutPage}
                fullWidth
                loading={isLoadingProgress}
              >
                Checkout
              </Button>
            </BlockStack>
          </div>
        </Box>
      </InlineGrid>
    </Page>
  );
}
