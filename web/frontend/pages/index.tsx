import { Badge, BlockStack, Box, InlineGrid, InlineStack, Page, Text, Tooltip } from "@shopify/polaris";
import React, { lazy, Suspense, useEffect, useState } from "react";

import GetStarted from "../components/common/GetStarted";
import EmailConfig from "../components/dashboard/EmailConfig";
import ReviewConfig from "../components/dashboard/ReviewConfig";
import SendPastOrderReviews from "../components/dashboard/SendPastOrderReviews";
import SubscribePlanOverview from "../components/dashboard/SubscribePlanOverview";
import OnboardingProcess from "../components/onboard/OnboardingProcess";

const EmailAnalyticsReport = lazy(() => import("../components/analytics/EmailAnalyticsReport"));
const DashboardLinks = lazy(() => import("../components/dashboard/DashboardLinks"));
const OtherAppList = lazy(() => import("../components/dashboard/OtherAppList"));

import ContactSupport from "../components/dashboard/ContactSupport";
import { useAppContext } from "../providers/AppProvider";
import { checkOnboardingCompletion } from "../utils/helper";

export default function Dashboard(): React.ReactElement {
  const { shop, isShopUnderProPlan, configurationSettings } = useAppContext();

  const [emailConfigureSetting, setEmailConfigureSetting] = useState<boolean>(false);
  const [reviewConfigureSetting, setReviewConfigureSetting] = useState<boolean>(false);
  const [partnerConfigureSetting, setPartnerConfigureSetting] = useState<boolean>(false);
  const [contactSupportConfigureSetting, setContactSupportConfigureSetting] = useState<boolean>(false);

  const isShopVerified: boolean = shop?.verified || isShopUnderProPlan;
  const isOnboardingDone: boolean = checkOnboardingCompletion(shop?.onboardingSteps);

  const [loadOtherContent, setLoadOtherContent] = useState<boolean>(false);
  useEffect(() => {
    setTimeout(() => {
      setLoadOtherContent(true);
    }, 3000);
  }, []);

  console.log(configurationSettings);

  useEffect(() => {
    if (configurationSettings) {
      setEmailConfigureSetting(configurationSettings?.email_configure_setting || false);
      setReviewConfigureSetting(configurationSettings?.review_configure_setting || false);
      setPartnerConfigureSetting(configurationSettings?.partner_configure_setting || false);
      setContactSupportConfigureSetting(configurationSettings?.contact_support_configure_setting || false);
    }
  }, [configurationSettings]);

  return (
    <Page
      title={
        // <Text
        //   as="span"
        //   variant="headingXl"
        // >
        "Welcome To TrustSync"
        // </Text>
      }
      titleMetadata={
        <>
          {isShopVerified ? (
            <Badge tone="success">Verified</Badge>
          ) : (
            <Tooltip content="Schedule a call with us to verify your store and double your email limit">
              <Badge tone="attention">Unverified</Badge>
            </Tooltip>
          )}
        </>
      }
    >
      <Box paddingBlockEnd="800">
        <BlockStack gap="400">
          <InlineStack>
            <GetStarted />
          </InlineStack>

          {!isOnboardingDone && (
            <BlockStack>
              <OnboardingProcess isDashboard={true} />
            </BlockStack>
          )}

          <SubscribePlanOverview />

          {!shop?.sentPastOrders && <SendPastOrderReviews />}

          <InlineGrid
            columns={emailConfigureSetting && reviewConfigureSetting ? 2 : 1}
            gap="400"
          >
            {emailConfigureSetting && <EmailConfig setEmailConfigureSetting={setEmailConfigureSetting} />}
            {reviewConfigureSetting && <ReviewConfig setReviewConfigureSetting={setReviewConfigureSetting} />}
          </InlineGrid>

          {contactSupportConfigureSetting && <ContactSupport setConfigureSetting={setContactSupportConfigureSetting} />}

          {loadOtherContent && (
            <Suspense>
              <EmailAnalyticsReport />

              <DashboardLinks />

              {partnerConfigureSetting && <OtherAppList setPartnerConfigureSetting={setPartnerConfigureSetting} />}
            </Suspense>
          )}
        </BlockStack>
      </Box>
    </Page>
  );
}
