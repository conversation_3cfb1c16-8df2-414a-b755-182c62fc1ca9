import React, { useCallback, useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";

import {
  Box,
  Card,
  ChoiceList,
  IndexFilters,
  IndexFiltersMode,
  IndexTable,
  InlineStack,
  Page,
  Pagination,
  Text,
  useIndexResourceState,
  useSetIndexFiltersMode,
} from "@shopify/polaris";

import { Modal as AppBridgeModal, TitleBar } from "@shopify/app-bridge-react";

import { isEmpty } from "../../app/utils/helper";

import { useAppQuery, useQueryClient } from "storeware-tanstack-query";
import { INegativeFeedback } from "types";
import queryKeys from "../../app/enums/queryKeys";
import { fetchNegativeFeedbacks } from "../apis/analytics.api";
import FeedbackForm from "../components/negative-feedback/FeedbackForm";
import ReviewItem from "../components/negative-feedback/ReviewItem";
import { sleep } from "../utils/helper";

interface NegativeFeedbackPagination {
  page: number;
  pageSize: number;
  totalItems: number;
}

export default function NegativeReviewList(): React.ReactElement {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [searchParams] = useSearchParams();

  const feedbackId: string | null = searchParams.get("id");

  const [queryValue, setQueryValue] = useState<string>("");
  const handleFiltersQueryChange = useCallback((value: string) => {
    setQueryValue(value);
  }, []);

  const [negativeReviews, setNegativeReviews] = useState<INegativeFeedback[]>([]);
  const [pagination, setPagination] = useState<NegativeFeedbackPagination>({
    page: 1,
    pageSize: 10,
    totalItems: 0,
  });

  const { data, isLoading } = useAppQuery({
    queryKey: [queryKeys.FETCH_NEGATIVE_FEEDBACK, pagination.page, pagination.pageSize, queryValue],
    queryFn: () => fetchNegativeFeedbacks(pagination.page, pagination.pageSize, queryValue),
    reactQueryOptions: {
      enabled: true,
      staleTime: 0,
      refetchInterval: 60000,
    },
  });

  const feedback: INegativeFeedback | undefined = data?.negativeReviews?.find(
    (review: INegativeFeedback) => (review?._id?.toString() || "") === feedbackId
  );

  // Manage Filter
  const { mode, setMode } = useSetIndexFiltersMode(IndexFiltersMode.Default);
  const [selected, setSelected] = useState<number>(0);

  // For pagination filter purpose
  const [perPageSize, setPerPageSize] = useState<string[]>([pagination?.pageSize.toString() ?? "10"]);

  const handleFilterPerPageSize = useCallback((pageSize: string[]) => {
    setPerPageSize(pageSize);
    setPagination((prev) => ({ ...prev, pageSize: parseInt(pageSize[0]) ?? 10 }));
  }, []);

  const removeFilterPerPageSize = useCallback(() => {
    setPerPageSize(["10"]);
    setPagination((prev) => ({ ...prev, pageSize: 10 }));
  }, []);

  const handleFilterClearAll = useCallback(() => {
    setPerPageSize(["10"]);
    setPagination((prev) => ({ ...prev, pageSize: 10 }));
  }, []);

  const onHandleCancel = (): void => {
    setQueryValue("");
    setPerPageSize(["10"]);
    setPagination((prev) => ({ ...prev, pageSize: 10 }));
  };

  const handlePagination = (newPage: number): void => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  const filters = [
    {
      key: "pageSize",
      label: "Show reviews",
      filter: (
        <ChoiceList
          title=""
          titleHidden
          choices={[
            { label: "10", value: "10" },
            { label: "25", value: "25" },
            { label: "50", value: "50" },
            { label: "100", value: "100" },
            { label: "200", value: "200" },
          ]}
          selected={perPageSize}
          onChange={handleFilterPerPageSize}
        />
      ),
      pinned: true,
      hideClearButton: true,
    },
  ];

  const appliedFilters =
    perPageSize && !isEmpty(perPageSize)
      ? [
          {
            key: "pageSize",
            label: `Show reviews: ${perPageSize.join(", ")}`,
            onRemove: removeFilterPerPageSize,
          },
        ]
      : [];

  const { selectedResources, allResourcesSelected, handleSelectionChange, clearSelection } = useIndexResourceState(
    negativeReviews as any[],
    {
      resourceIDResolver: (email) => {
        return email._id;
      },
    }
  );

  // Remove bulk negative feedback
  const [isRemovingBulkReview, setIsRemovingBulkReview] = useState<boolean>(false);
  const bulkActions = [
    {
      content: isRemovingBulkReview ? "Procesing..." : "Remove",
      onAction: async () => {
        setIsRemovingBulkReview(true);
        try {
          await fetch("/api/v1/negative-feedbacks/remove", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ ids: selectedResources }),
          });

          await sleep(1500);

          queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_NEGATIVE_FEEDBACK] });

          shopify.toast.show("Remove feedback", {
            duration: 5000,
          });
        } catch (error) {
          console.error("Error removing reviews:", error);
        } finally {
          setIsRemovingBulkReview(false);
          clearSelection();
        }
      },
    },
  ];

  // Delete review feedback
  const [deletingReviewId, setDeletingReviewId] = useState<string | null>(null);
  const handleDeleteReview = (ids: string): void => {
    setDeletingReviewId(ids);
    (document.getElementById("confirmation-modal") as any)?.show();
  };

  const [isRemovingReview, setIsRemovingReview] = useState<boolean>(false);
  const deleteReviewItem = async (): Promise<void> => {
    try {
      setIsRemovingReview(true);

      await fetch("/api/v1/negative-feedbacks/remove", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ids: [deletingReviewId] }),
      });

      await sleep(1500);

      (document.getElementById("confirmation-modal") as any)?.hide();

      queryClient.invalidateQueries({ queryKey: [queryKeys.FETCH_NEGATIVE_FEEDBACK] });

      shopify.toast.show("Remove feedback", {
        duration: 5000,
      });
    } catch (error) {
      console.log(error);
    } finally {
      setIsRemovingReview(false);
    }
  };

  // Reply feedback form
  const [replyReview, setReplyReview] = useState<INegativeFeedback | null>(null);
  const [activeModal, setActiveModal] = useState<boolean>(false);

  useEffect(() => {
    if (data) {
      setNegativeReviews(data.negativeReviews);
      // Map API pagination structure to frontend pagination structure
      setPagination({
        page: data.pagination.page || 1,
        pageSize: data.pagination.pageSize || 10,
        totalItems: data.pagination.totalItems || 0,
      });
    }
  }, [data]);

  // Handle URL query parameter
  useEffect(() => {
    if (feedback) {
      setReplyReview(feedback);
      setActiveModal(true);
    }
  }, [feedback]);

  return (
    <Page
      fullWidth
      title="Negative Feedbacks"
      backAction={{
        id: "dashboard",
        content: "Dashboard",
        onAction: () => {
          navigate("/");
        },
      }}
    >
      <Card padding={"0"}>
        <IndexFilters
          queryValue={queryValue}
          queryPlaceholder="Searching in all..."
          onQueryChange={handleFiltersQueryChange}
          onQueryClear={() => setQueryValue("")}
          cancelAction={{
            onAction: onHandleCancel,
            disabled: false,
            loading: false,
          }}
          tabs={[]}
          selected={selected}
          onSelect={setSelected}
          filters={filters}
          appliedFilters={appliedFilters}
          onClearAll={handleFilterClearAll}
          mode={mode}
          setMode={setMode}
          loading={isLoading}
        />

        <IndexTable
          resourceName={{ singular: "negative feedback", plural: "negative feedbacks" }}
          itemCount={negativeReviews?.length || 0}
          selectedItemsCount={allResourcesSelected ? "All" : selectedResources.length}
          onSelectionChange={handleSelectionChange}
          headings={[
            { title: "Name" },
            { title: "Email" },
            { title: "Rating" },
            { title: "Attachment", alignment: "center" },
            { title: "Status", alignment: "center" },
            { title: "Message" },
            { title: "Date" },
            { title: "Action", alignment: "center" },
          ]}
          selectable={true}
          hasMoreItems={false}
          promotedBulkActions={bulkActions}
        >
          <ReviewItem
            negativeReviews={negativeReviews}
            selectedResources={selectedResources}
            deletingReviewId={deletingReviewId}
            handleDeleteReview={handleDeleteReview}
            handleReplyReview={(data: INegativeFeedback) => {
              setReplyReview(data);
              setActiveModal(true);
            }}
          />
        </IndexTable>

        <Box padding="400">
          <InlineStack align="center">
            <Pagination
              label={`${Math.min((pagination.page - 1) * pagination.pageSize + 1, pagination.totalItems)} - ${Math.min(
                pagination.page * pagination.pageSize,
                pagination.totalItems
              )} of ${pagination.totalItems} negative feedbacks`}
              hasPrevious={pagination.page > 1}
              onPrevious={() => handlePagination(pagination.page - 1)}
              hasNext={pagination.page * pagination.pageSize < pagination.totalItems}
              onNext={() => handlePagination(pagination.page + 1)}
            />
          </InlineStack>
        </Box>
      </Card>

      <AppBridgeModal
        id="confirmation-modal"
        variant="small"
      >
        <Box padding="400">
          <Text as="p">Do you really want to delete this feedback? This process cannot be undone.</Text>
        </Box>
        <TitleBar title="Are you sure?">
          <button
            variant="primary"
            tone="critical"
            onClick={deleteReviewItem}
            {...(isRemovingReview && { loading: "true" })}
          >
            Delete
          </button>
        </TitleBar>
      </AppBridgeModal>

      {activeModal && (
        <FeedbackForm
          replyReview={replyReview}
          activeModal={activeModal}
          setActiveModal={setActiveModal}
        />
      )}
    </Page>
  );
}
