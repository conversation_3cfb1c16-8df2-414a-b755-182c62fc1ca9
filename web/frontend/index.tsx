// import { createRoot } from "react-dom/client";
import { onCLS, onLCP } from "web-vitals";
// import App from "./App";
// import { initI18n } from "./utils/i18nUtils";

// import "./assets/css/custom.css";

// Ensure that locales are loaded before rendering the app
// const root = createRoot(document.getElementById("app"));
// root.render(<App />);
// initI18n();

onLCP((metric) => console.log("LCP: ", { ...metric }), { reportAllChanges: true });
onCLS((metric) => console.log("CLS: ", { ...metric }), { reportAllChanges: true });

const loadApp = async (): Promise<void> => {
  const { createRoot } = await import("react-dom/client");
  const { default: App } = await import("./App");
  const { initI18n } = await import("./utils/i18nUtils");

  const appElement = document.getElementById("app");
  if (!appElement) {
    throw new Error("App element not found");
  }

  const root = createRoot(appElement);
  root.render(<App />);
  initI18n();
};

loadApp();
