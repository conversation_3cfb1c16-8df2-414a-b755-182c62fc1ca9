import { Session } from "@shopify/shopify-api";
import SessionsModel from "./app/models/Sessions";

class CustomSessionStorage {
  /*
    The storeCallback takes in the Session, and sets a stringified version of it on the redis store
    This callback is used for BOTH saving new Sessions and updating existing Sessions.
    If the session can be stored, return true
    Otherwise, return false
  */
  async storeSession(session: Session): Promise<boolean> {
    try {
      // Inside our try, we use the `setAsync` method to save our session.
      // This method returns a boolean (true if successful, false if not)
      // return await this.client.set(session.id, JSON.stringify(session));
      const query = { id: session.id };
      const toSave = {
        id: session.id,
        payload: JSON.stringify(session),
      };

      await SessionsModel.findOneAndUpdate(query, toSave, { upsert: true });

      return true;
    } catch (err: unknown) {
      // throw errors, and handle them gracefully in your application
      const errorMessage = err instanceof Error ? err.message : String(err);
      throw new Error(errorMessage);
    }
  }

  /*
    The loadCallback takes in the id, and uses the getAsync method to access the session data
     If a stored session exists, it's parsed and returned
     Otherwise, return undefined
  */
  async loadSession(id: string): Promise<Session | undefined> {
    try {
      const sessionDB = await SessionsModel.findOne({ id }).lean();
      if (sessionDB) {
        return new Session(JSON.parse(sessionDB.payload));
      }
      return undefined;
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      throw new Error(errorMessage);
    }
  }

  /*
    The deleteCallback takes in the id, and uses the redis `del` method to delete it from the store
    If the session can be deleted, return true
    Otherwise, return false
  */
  async deleteSession(id: string): Promise<boolean> {
    try {
      // Inside our try, we use the `delAsync` method to delete our session.
      // This method returns a boolean (true if successful, false if not)
      // return await this.client.del(id);
      await SessionsModel.deleteOne({ id });
      return true;
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      throw new Error(errorMessage);
    }
  }

  async deleteSessions(ids: string[]): Promise<boolean> {
    try {
      // Use `Promise.all` to perform all deletions in parallel
      const deletePromises = ids.map((id) => SessionsModel.deleteOne({ id }));
      await Promise.all(deletePromises);
      return true; // Return true if all deletions succeed
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      throw new Error(`Failed to delete sessions: ${errorMessage}`);
    }
  }

  async findSessionsByShop(id: string): Promise<Session | undefined> {
    try {
      const sessionDB = await SessionsModel.findOne({ id }).lean();

      if (sessionDB) {
        return new Session(JSON.parse(sessionDB.payload));
      }
      return undefined;
    } catch (e: unknown) {
      console.log(e);
      return undefined;
    }
  }
}

// Export the class
export default CustomSessionStorage;
