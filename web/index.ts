import compression from "compression";
import cors from "cors";
import "dotenv/config";
import express, { NextFunction, Request, Response } from "express";
import { readFileSync } from "fs";
import { join } from "path";
import serveStatic from "serve-static";
import { upload } from "./app/clients/multer.js";
import webhookController from "./app/controllers/webhook.controller.js";
import morganMiddleware from "./app/middleware/morgan.middleware.js";
import shopInstallMiddleware from "./app/middleware/shop-install.middleware.js";
import apiRoutes from "./app/routes/index.js";
import ratingRoutes from "./app/routes/rating.js";
import webhookHandlers from "./app/routes/webhooks.js";
import connectDB from "./database.js";
import shopify from "./shopify.js";

connectDB();

const PORT: number = parseInt(process.env.BACKEND_PORT || process.env.PORT || "3000", 10);
const STATIC_PATH: string =
  process.env.NODE_ENV === "production" ? `${process.cwd()}/frontend/dist` : `${process.cwd()}/frontend/`;

console.log("static path: ", STATIC_PATH);
const INDEX_HTML: string = readFileSync(join(STATIC_PATH, "index.html"), { encoding: "utf8" })
  .toString()
  .replace("%VITE_SHOPIFY_API_KEY%", process.env.SHOPIFY_API_KEY || "");

// Create Express Server
const app = express();

app.post("/webhooks", express.json(), webhookController.defaultWebhook);
app.post("/webhooks/open-email", express.json(), webhookController.openedEmail);
app.post("/webhooks/click-email", express.json(), webhookController.clickedEmail);
// get email reply via mailgun webhook
app.post(
  "/webhooks/reply-email",
  express.urlencoded({ extended: true }),
  upload.any(),
  webhookController.replyEmailWebhook as any
);

// Set up Shopify authentication and webhook handling
app.get(shopify.config.auth.path, shopify.auth.begin());

app.get(
  shopify.config.auth.callbackPath,
  shopify.auth.callback(),
  shopInstallMiddleware,
  shopify.redirectToShopifyOrAppRoot()
);

app.use(shopify.config.webhooks.path, shopify.processWebhooks({ webhookHandlers }));

// If you are adding routes outside of the /api path, remember to
// also add a proxy rule for them in web/frontend/vite.config.js
app.use("/api/v1", shopify.validateAuthenticatedSession());

app.use(express.json());
app.use(morganMiddleware);
app.use(compression());

app.use("/api/v1", apiRoutes);
app.use("/api/rating", cors(), ratingRoutes);
app.get("/api/ingest", webhookController.ingestWebhook);

app.use(shopify.cspHeaders());
app.use(
  serveStatic(STATIC_PATH, {
    index: false,
    cacheControl: true,
    etag: true,
    immutable: true,
    lastModified: true,
    maxAge: "20 days",
  })
);

app.use(
  "/*",
  shopify.ensureInstalledOnShop(),
  async (_req: Request, res: Response, _next: NextFunction): Promise<void> => {
    const html: string =
      process.env.NODE_ENV === "production"
        ? INDEX_HTML
        : readFileSync(join(STATIC_PATH, "index.html"), { encoding: "utf8" })
            .toString()
            .replace("%VITE_SHOPIFY_API_KEY%", process.env.SHOPIFY_API_KEY || "");
    res.status(200).set("Content-Type", "text/html").send(html);
  }
);

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
