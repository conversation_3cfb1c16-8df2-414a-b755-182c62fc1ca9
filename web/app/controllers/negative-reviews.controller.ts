import { Upload } from "@aws-sdk/lib-storage";
import { Request, Response } from "express";
import { ParsedQs } from "qs";
import { s3Client } from "../clients/s3.js";

import Email from "../models/Email.js";
import EmailSettings from "../models/EmailSettings.js";
import NegativeReview from "../models/NegativeReviews.js";
import Shop from "../models/Shop.js";
import { handleNegativeReviewEmail } from "../services/mailgun.service.js";
import { transformShop } from "../services/negative-review.service.js";

// Interface for file upload
interface UploadedFile {
  originalname: string;
  buffer: Buffer;
  mimetype: string;
}

// Interface for negative review list query
interface NegativeReviewListQuery extends ParsedQs {
  shop?: string;
}

// Interface for negative review request body
interface NegativeReviewBody {
  name: string;
  email: string;
  rating: number;
  message: string;
  orderId: string;
}

export async function handleNegativeReviewList(
  req: Request & { query: NegativeReviewListQuery },
  res: Response
): Promise<void> {
  try {
    const { shop } = req.query;
    const shopId = shop || "";

    const shopDB = await Shop.findById(shopId);
    if (!shopDB) {
      res.status(400).json({ data: {}, message: "No shop found." });
      return;
    }

    const settings = await EmailSettings.findOne({ shop: shopDB.shop });
    if (!settings) {
      res.status(400).json({ data: {}, message: "No email settings found." });
      return;
    }

    res.status(200).json({ data: transformShop(shopDB, settings), message: "Success" });
  } catch (error) {
    console.log(error);
    res.status(400).json({ data: {}, message: "Error" });
  }
}

export async function handleNegativeReview(
  req: Request & { query: NegativeReviewListQuery; body: NegativeReviewBody; file?: UploadedFile },
  res: Response
): Promise<void> {
  try {
    const { shop } = req.query;
    const { name, email, rating, message, orderId } = req.body;

    const shopId = shop || "";

    const shopDB = await Shop.findById(shopId);
    if (!shopDB) {
      res.status(400).json({ data: {}, message: "No shop found." });
      return;
    }

    const settings = await EmailSettings.findOne({ shop: shopDB.shop });

    if (!settings) {
      res.status(400).json({ data: {}, message: "No email settings found." });
      return;
    }

    const emailData = await Email.findOne({ shop: shopDB.shop, orderId: orderId });

    let attachment: string | null = null;
    const firstName = emailData?.firstName || "";

    const file = req.file;
    if (file) {
      const upload = new Upload({
        client: s3Client,
        params: {
          Bucket: process.env.STORAGE_BUCKET || "",
          Key: process.env.STORAGE_FOLDER ? `${process.env.STORAGE_FOLDER}/${file.originalname}` : file.originalname,
          Body: file.buffer,
          ContentType: file.mimetype,
          ACL: "public-read",
        },
      });

      attachment = file?.originalname || null;

      await upload.done();
    }

    const negativeReview = await NegativeReview.create({
      name,
      email,
      rating,
      message,
      orderId,
      shop: shopDB.shop,
      attachment,
    });

    await handleNegativeReviewEmail(shopDB, negativeReview, firstName, orderId);

    res.status(200).json({ data: {} });
  } catch (error) {
    console.log("error while creating negative review ", error);

    res.status(400).json({ data: {}, message: "Error" });
  }
}
