import { Request, Response } from "express";
import { ParsedQs } from "qs";
import mailgunService from "../clients/mailgun.js";
import EmailSettings from "../models/EmailSettings.js";
import Shop from "../models/Shop.js";

// Interface for add domain request body
interface AddDomainBody {
  domain: string;
}

// Interface for domain info query
interface DomainInfoQuery extends ParsedQs {
  domain?: string;
}

interface AddDomainRequest extends Request {
  body: AddDomainBody;
}

interface DomainInfoRequest extends Request {
  query: DomainInfoQuery;
}

export async function handleAddDomain(req: AddDomainRequest, res: Response): Promise<void> {
  try {
    const { domain } = req.body;
    if (!domain) {
      res.status(400).json({ data: {}, message: "No domain found", status: 400 });
      return;
    }

    const { shop } = res.locals.shopify.session;

    const shopDB = await Shop.findOne({ shop });
    if (!shopDB) {
      res.status(400).json({ data: {}, message: "No shop found", status: 400 });
      return;
    }

    const emailSettings = await EmailSettings.findOne({ shop });
    if (!emailSettings) {
      res.status(400).json({ data: {}, message: "No email settings found", status: 400 });
      return;
    }

    if (domain === "trustsync.io") {
      emailSettings.domain = domain;
      emailSettings.domainVerified = true;

      await emailSettings.save();
    } else {
      try {
        const response = await mailgunService.domains.get(domain);

        emailSettings.domain = domain;
        emailSettings.domainVerified = response?.state === "active";
        await emailSettings.save();
      } catch (error) {
        await mailgunService.domains.create({ name: domain, smtp_password: "temp_password" });
        await mailgunService.webhooks.create(domain, "opened", `${process.env.APP_URL}/webhooks/open-email`, false);
        await mailgunService.webhooks.create(domain, "clicked", `${process.env.APP_URL}/webhooks/click-email`, false);

        emailSettings.domain = domain;
        emailSettings.domainVerified = false;

        await emailSettings.save();
      }
    }

    res.status(200).json({ data: {}, message: "Domain added", status: 200 });
  } catch (error) {
    console.log(error);

    res.status(400).json({ data: {}, message: "DOMAIN_NOT_FOUND", status: 400 });
  }
}

export async function handleDomainInfo(req: DomainInfoRequest, res: Response): Promise<void> {
  try {
    const { domain } = req.query;
    if (!domain) {
      res.status(400).json({ data: {}, message: "No domain found", status: 400 });
      return;
    }

    const { shop } = res.locals.shopify.session;

    const shopDB = await Shop.findOne({ shop });
    if (!shopDB) {
      res.status(400).json({ data: {}, message: "No shop found.", status: 400 });
      return;
    }

    const emailSettings = await EmailSettings.findOne({ shop });
    if (!emailSettings) {
      res.status(400).json({ data: {}, message: "No email settings found", status: 400 });
      return;
    }

    const response = await mailgunService.domains.get(domain);

    emailSettings.domainVerified = response?.state === "active";

    await emailSettings.save();

    res.status(200).json({ data: response, message: "Success", status: 200 });
  } catch (error) {
    console.log(error);
    res.status(400).json({ data: {}, message: "DOMAIN_NOT_FOUND", status: 400 });
  }
}
