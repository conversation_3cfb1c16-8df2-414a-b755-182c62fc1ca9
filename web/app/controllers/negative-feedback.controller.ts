import { NegativeReviewStaus } from "@trustsync/types";
import { Request, Response } from "express";
import { ParsedQs } from "qs";
import NegativeReview from "../models/NegativeReviews.js";
import Shop from "../models/Shop.js";
import { handleNegativeReviewReplyEmail } from "../services/mailgun.service.js";
import { saveNegativeReviewReply } from "../services/negative-review.service.js";
import { getDOImageUrl, stripTags } from "../utils/helper.js";

// Interface for  negative feedback query parameters
interface NegativeFeedbackQuery extends ParsedQs {
  page?: string;
  pageSize?: string;
  q?: string;
}

// Interface for remove feedback request body
interface RemoveFeedbackBody {
  ids: string[];
}

// Interface for reply feedback request body
interface ReplyFeedbackBody {
  id: string;
  reply: string;
  status?: string;
}

interface NegativeFeedbackListRequest extends Request {
  query: NegativeFeedbackQuery;
}

interface RemoveFeedbackRequest extends Request {
  body: RemoveFeedbackBody;
}

interface ReplyFeedbackRequest extends Request {
  body: ReplyFeedbackBody;
}

export async function getNegativeFeedbackList(req: NegativeFeedbackListRequest, res: Response): Promise<void> {
  try {
    const { page = "1", pageSize = "10", q = "" } = req.query;

    // Parse query parameters
    const parsedPage = parseInt(page, 10);
    const parsedPageSize = parseInt(pageSize, 10);
    const skip = (parsedPage - 1) * parsedPageSize;

    const { shop } = res.locals.shopify.session;

    // Build search query
    const searchQuery = q
      ? {
          $or: [
            { name: { $regex: q, $options: "i" } }, // Case-insensitive search in 'name'
            { email: { $regex: q, $options: "i" } }, // Case-insensitive search in 'email'
            { message: { $regex: q, $options: "i" } }, // Case-insensitive search in 'message'
          ],
        }
      : {};

    let negativeReviews = await NegativeReview.where({ shop })
      .find(searchQuery)
      .skip(skip)
      .limit(parsedPageSize)
      .sort({ created_at: -1 })
      .lean();

    negativeReviews = negativeReviews?.map((feedback) => {
      // Process main attachment
      const { attachment = "" } = feedback;
      const attachmentUrl = attachment?.length > 0 ? getDOImageUrl(attachment) : "";

      // Process attachments in conversation array
      let updatedFeedback = { ...feedback, hasAttachment: !!attachment?.length, attachment: attachmentUrl };

      // Check if conversation array exists (it might be undefined for new reviews)
      if (updatedFeedback.conversation && Array.isArray(updatedFeedback.conversation)) {
        // Map through conversation array and process attachments
        updatedFeedback.conversation = updatedFeedback.conversation.map((message) => {
          const { attachments: messageAttachments = [] } = message;
          let updatedMessage = { ...message };

          if (messageAttachments && messageAttachments.length > 0) {
            updatedMessage.attachments = messageAttachments.map((att: string) => getDOImageUrl(att));
            // updatedMessage.hasMultipleAttachments = true;
          }

          return updatedMessage;
        });
      }

      return updatedFeedback;
    });

    const totalItems = await NegativeReview.where({ shop }).countDocuments(searchQuery);

    res.json({
      data: {
        negativeReviews,
        pagination: {
          page: parsedPage,
          pageSize: parsedPageSize,
          totalItems,
        },
      },
      message: "Success",
      status: 200,
    });
  } catch (error) {
    console.log(error);

    res.json({ data: {}, message: "Error", status: 400 });
  }
}

export async function getNegativeFeedbackRemove(req: RemoveFeedbackRequest, res: Response): Promise<void> {
  try {
    const { ids } = req.body;

    await NegativeReview.deleteMany({ _id: { $in: ids } });

    res.json({ data: {}, message: "Success", status: 200 });
  } catch (error) {
    console.log(error);

    res.json({ data: {}, message: "Error", status: 400 });
  }
}

export async function getNegativeFeedbackReply(req: ReplyFeedbackRequest, res: Response): Promise<void> {
  try {
    const { id, reply, status } = req.body;
    const { session } = res.locals.shopify;

    const shop = await Shop.findOne({ shop: session.shop });
    const negativeReview = await NegativeReview.findById(id);

    if (!shop) {
      res.json({ data: {}, message: "Shop not found", status: 404 });
      return;
    }

    if (!negativeReview) {
      res.json({ data: {}, message: "Negative review not found", status: 404 });
      return;
    }

    // Send email reply
    const emailResponse = await handleNegativeReviewReplyEmail(shop, negativeReview, reply);

    if (!emailResponse || emailResponse.status !== 200) {
      res.json({ data: {}, message: "Mail sent failed", status: 400 });
      return;
    }

    // Create message object to add to conversation
    const message = {
      from: "shop" as const,
      messageId: emailResponse.id as string,
      message: stripTags(reply),
      status: status || "unresolved",
      readAt: new Date(),
    };

    // Save the message to the conversation array and update the main review status
    const updatedReview = await saveNegativeReviewReply(id, message, (status || "unresolved") as NegativeReviewStaus);

    res.json({
      data: { updatedReview },
      message: "Reply sent successfully",
      status: 200,
    });
  } catch (error) {
    console.error("Error in getNegativeFeedbackReply:", error);
    res.json({ data: {}, message: "Failed to send reply", status: 400 });
  }
}
