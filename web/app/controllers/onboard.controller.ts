import { Session } from "@shopify/shopify-api";
import { Request, Response } from "express";
import {
  enabledEmailSendingSetting,
  OnboardEmailSettingData,
  OnboardEmailTriggerData,
  OnboardReviewLinkData,
  saveAddReviewLink,
  saveOnboardEmailTriggerSetting,
} from "../services/onboard.service.js";

import onboardEmailTrigerValidation from "../validations/onboardEmailTrigerValidation.js";
import onboardReviewLinkValidation from "../validations/onboardReviewLinkValidation.js";

// Interface for Shopify authenticated response
interface ShopifyAuthenticatedResponse extends Response {
  locals: {
    shopify: {
      session: Session;
    };
  };
}

// Interface for validation result
interface ValidationResult {
  isValid: boolean;
  [key: string]: any;
}

// Request interfaces
interface EnableEmailSettingRequest extends Request {
  body: OnboardEmailSettingData;
}

interface AddReviewLinkSettingRequest extends Request {
  body: OnboardReviewLinkData;
}

interface EmailTriggerSettingRequest extends Request {
  body: OnboardEmailTriggerData;
}

export async function saveEnableEmailSetting(req: EnableEmailSettingRequest, res: Response): Promise<void> {
  try {
    const {
      session: { shop },
    } = res.locals.shopify;

    await enabledEmailSendingSetting(shop, req.body);

    res.status(200).json({ data: {}, message: "Success", status: 200 });
  } catch (error) {
    console.log(error);

    res.status(400).json({ data: {}, message: "Error", status: 400 });
  }
}

export async function saveAddReviewLinkSetting(req: AddReviewLinkSettingRequest, res: Response): Promise<void> {
  try {
    const {
      session: { shop },
    } = res.locals.shopify;

    const validation: ValidationResult = onboardReviewLinkValidation.validateForm(req.body);
    if (!validation.isValid) {
      // delete validation.isValid;
      res.status(422).json({ data: {}, message: "Success", status: 422, errors: validation });
      return;
    }

    await saveAddReviewLink(shop, req.body);

    res.status(200).json({ data: {}, message: "Success", status: 200 });
  } catch (error) {
    console.log(error);

    res.status(400).json({ data: {}, message: "Error", status: 400 });
  }
}

export async function saveEmailTriggerSetting(req: EmailTriggerSettingRequest, res: Response): Promise<void> {
  try {
    const {
      session: { shop },
    } = res.locals.shopify;

    const validation: ValidationResult = onboardEmailTrigerValidation.validateForm(req.body);
    if (!validation.isValid) {
      // delete validation.isValid;
      res.status(422).json({ data: {}, message: "Success", status: 422, errors: validation });
      return;
    }

    await saveOnboardEmailTriggerSetting(shop, req.body);

    res.status(200).json({ data: {}, message: "Success", status: 200 });
  } catch (error) {
    console.log(error);

    res.status(400).json({ data: {}, message: "Error", status: 400 });
  }
}
