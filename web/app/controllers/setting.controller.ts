import { Request, Response } from "express";
import EmailSettings from "../models/EmailSettings.js";
import Shop from "../models/Shop.js";
import {
  EmailDesignSettingFormData,
  GeneralSettingFormData,
  ReviewSettingFormData,
  saveEmailDesignSetting,
  saveGeneralSetting,
  saveReviewSetting,
} from "../services/email.setting.service.js";
import { emailDesignSettingValidation } from "../validations/emailDesignSettingValidation.js";
import { generalSettingValidation } from "../validations/generalSettingValidation.js";
import { reviewSettingValidation } from "../validations/reviewSettingValidation.js";

interface ValidationResult {
  isValid: boolean;
  [key: string]: any;
}
interface ReviewSettingsRequest extends Request {
  body: ReviewSettingFormData;
}

interface EmailDesignSettingsRequest extends Request {
  body: EmailDesignSettingFormData;
}

interface GeneralEmailSettingsRequest extends Request {
  body: GeneralSettingFormData;
}

export async function hangleEmailSettings(req: Request, res: Response): Promise<void> {
  try {
    const { shop } = res.locals.shopify.session;

    const shopDB = await Shop.findOne({ shop });
    const settings = await EmailSettings.findOne({ shop });

    res.json({ data: { settings, shop: shopDB }, message: "Success", status: 200 });
  } catch (error) {
    console.log(error);

    res.json({ data: {}, message: "Error", status: 400 });
  }
}

export async function saveReviewSettings(req: ReviewSettingsRequest, res: Response): Promise<void> {
  try {
    const {
      session: { shop },
    } = res.locals.shopify;

    const validation: ValidationResult = reviewSettingValidation.validateForm(req.body);
    if (!validation.isValid) {
      // delete validation.isValid;
      res.json({ data: {}, message: "Success", status: 422, errors: validation });
      return;
    }

    await saveReviewSetting(shop, req.body);

    res.json({ data: {}, message: "Success", status: 200 });
  } catch (error) {
    console.log(error);

    res.json({ data: {}, message: "Error", status: 400 });
  }
}

export async function saveEmailDesignSettings(req: EmailDesignSettingsRequest, res: Response): Promise<void> {
  try {
    const { shop } = res.locals.shopify.session;

    const validation: ValidationResult = emailDesignSettingValidation.validateForm(req.body);
    if (!validation.isValid) {
      // delete validation.isValid;
      res.json({ data: {}, message: "Success", status: 422, errors: validation });
      return;
    }

    await saveEmailDesignSetting(shop, req.body);

    res.json({ data: {}, message: "Success", status: 200 });
  } catch (error) {
    console.log(error);

    res.json({ data: {}, message: "Error", status: 400 });
  }
}

export async function saveGeneralEmailSettings(req: GeneralEmailSettingsRequest, res: Response): Promise<void> {
  try {
    const { shop } = res.locals.shopify.session;

    const validation: ValidationResult = generalSettingValidation.validateForm(req.body);
    if (!validation.isValid) {
      // delete validation.isValid;
      res.json({ data: {}, message: "Success", status: 422, errors: validation });
      return;
    }

    await saveGeneralSetting(shop, req.body);

    res.json({ data: {}, message: "Success", status: 200 });
  } catch (error) {
    console.log(error);

    res.json({ data: {}, message: "Error", status: 400 });
  }
}
