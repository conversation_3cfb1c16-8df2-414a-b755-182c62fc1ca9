import { Request, Response } from "express";
import Shop from "../models/Shop.js";
import Subscription from "../models/Subscription.js";
import { processConfigureSettings } from "../services/configuration-settings.service.js";

export async function getShop(req: Request, res: Response): Promise<void> {
  try {
    const { session } = res.locals.shopify;

    const shop = await Shop.findOne({ shop: session.shop });

    res.status(200).json({ data: { shop }, message: "Success" });
  } catch (error) {
    console.log(error);

    res.status(400).json({ data: {}, message: "Error" });
  }
}

export async function getShopInfo(req: Request, res: Response): Promise<void> {
  try {
    const { session } = res.locals.shopify;

    const shop = await Shop.findOne({ shop: session.shop });
    const subscription = await Subscription.findOne({ shop: session.shop });
    const configurationSettings = await processConfigureSettings(session.shop);

    res.status(200).json({ data: { shop, subscription, configurationSettings }, message: "Success" });
  } catch (error) {
    console.log(error);

    res.status(400).json({ data: {}, message: "Error" });
  }
}
