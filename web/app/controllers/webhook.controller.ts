import { Upload } from "@aws-sdk/lib-storage";
import { ShopifyWebhookHeaders } from "@trustsync/types";
import { Request, Response } from "express";
import { s3Client } from "../clients/s3.js";
import Email from "../models/Email.js";
import NegativeReview from "../models/NegativeReviews.js";
import { webhookIngest } from "../services/ingest.service.js";
import { uninstalledShop } from "../services/shop.service.js";
import { extractOrderIdFromEmail } from "../utils/helper.js";

// Interface for Mailgun webhook data
interface MailgunWebhookData {
  "message-id"?: string;
  "event-data"?: {
    message?: {
      headers?: {
        "message-id"?: string;
      };
    };
  };
  [key: string]: any;
}

// Interface for file upload
interface UploadedFile {
  originalname: string;
  buffer: Buffer;
  mimetype: string;
}

// Interface for reply email webhook body
interface ReplyEmailWebhookBody {
  "Message-Id": string;
  "stripped-text": string;
  References?: string;
  subject?: string;
  [key: string]: any;
}

const getMailgunMessageId = (data: MailgunWebhookData): string | null => {
  return data["message-id"] || data["event-data"]?.["message"]?.headers?.["message-id"] || null;
};

const defaultWebhook = async (req: Request, res: Response): Promise<void> => {
  console.log("call default webhook");

  const { "x-shopify-topic": shopifyTopic, "x-shopify-shop-domain": shopifyShopDomain } =
    req.headers as Partial<ShopifyWebhookHeaders>;

  if (shopifyTopic === "app/uninstalled" && shopifyShopDomain) {
    await uninstalledShop(shopifyShopDomain);
  }

  res.status(200).json({ message: "Success" });
};

const openedEmail = async (req: Request, res: Response): Promise<void> => {
  try {
    const messageId = getMailgunMessageId(req.body as MailgunWebhookData);

    console.log({ messageId });

    const updatedEmail = await Email.findOneAndUpdate(
      { messageId: `<${messageId}>` },
      { $set: { opened: true } },
      { new: true }
    );

    console.log("open mailgun webhook ", messageId, updatedEmail?.email);
  } catch (error) {
    console.log("error from open email webhook " + error);
  }

  res.status(200).json({ message: "received" });
};

const clickedEmail = async (req: Request, res: Response): Promise<void> => {
  try {
    const messageId = getMailgunMessageId(req.body as MailgunWebhookData);

    console.log({ messageId });

    const updatedEmail = await Email.findOneAndUpdate(
      { messageId: `<${messageId}>` },
      { $set: { clicked: true } },
      { new: true }
    );

    console.log("click mailgun webhook ", messageId, updatedEmail?.email);
  } catch (error) {
    console.log("error from click email webhook " + error);
  }

  res.status(200).json({ message: "received" });
};

const ingestWebhook = async (req: Request, res: Response): Promise<void> => {
  await webhookIngest();
  res.status(200).json({ message: "Success" });
};

const handleFileUpload = async (files: UploadedFile[] | undefined): Promise<string[]> => {
  const allFiles = files || [];
  const imageFiles = allFiles.filter((file) => file.mimetype.startsWith("image/"));

  if (imageFiles.length === 0) {
    return [];
  }

  const attachments: string[] = [];

  for (const file of imageFiles) {
    try {
      const upload = new Upload({
        client: s3Client,
        params: {
          Bucket: process.env.STORAGE_BUCKET || "",
          Key: process.env.STORAGE_FOLDER ? `${process.env.STORAGE_FOLDER}/${file.originalname}` : file.originalname,
          Body: file.buffer,
          ContentType: file.mimetype,
          ACL: "public-read",
        },
      });

      await upload.done();

      // Add the filename to attachments array
      if (file?.originalname) {
        attachments.push(file.originalname);
      }
    } catch (error: any) {
      console.log(`Error uploading attachment ${file.originalname}:`, error?.message);
    }
  }

  return attachments;
};

// Interface for conversation message
interface ConversationMessage {
  from: string;
  messageId: string;
  message: string;
  readAt: Date | null;
  status: string;
  created_at: Date;
  attachments?: string[];
}

const replyEmailWebhook = async (
  req: Request & { files?: UploadedFile[]; body: ReplyEmailWebhookBody },
  res: Response
): Promise<void> => {
  try {
    let message: ConversationMessage = {
      from: "customer",
      messageId: req.body["Message-Id"],
      message: req.body["stripped-text"],
      readAt: null,
      status: "replied",
      created_at: new Date(),
    };
    const attachments = await handleFileUpload(req.files);

    // if attachments available
    if (attachments?.length > 0) {
      message = { ...message, attachments };
    }

    const referenceId = req.body?.References;
    const orderId = extractOrderIdFromEmail(req.body?.subject);

    // Find the review by the References field (messageId)
    let review = await NegativeReview.findOne({
      "conversation.messageId": referenceId,
    });

    // If actual order happens
    if (!review && orderId && orderId !== "0000000000") {
      review = await NegativeReview.findOne({ orderId });
    }

    if (!review) {
      res.json({
        message: "Review not found",
        status: 404,
      });
      return;
    }

    // initialize conversation array if not found
    if (!review.conversation) {
      review.conversation = [];
    }

    review.conversation.push(message as any);
    await review.save();

    console.log("Customer reply saved successfully to review ID:", review._id);
    res.json({
      message: "Customer reply saved successfully",
      status: 200,
    });
  } catch (err: any) {
    console.error("Error processing reply email webhook:", err);
    res.status(500).json({
      message: err.message || "Error processing webhook",
      status: 500,
    });
  }
};

export default {
  defaultWebhook,
  openedEmail,
  clickedEmail,
  ingestWebhook,
  replyEmailWebhook,
};
