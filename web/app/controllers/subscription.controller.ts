import { Session } from "@shopify/shopify-api";
import { Request, Response } from "express";
import Shop from "../models/Shop.js";
import Subscription from "../models/Subscription.js";
import { fluentSubscriptionSwitch } from "../services/fluent-crm.service.js";
import {
  cancelShopifySubscription,
  createShopifySubscription,
  subscribeToFreePlan,
} from "../services/subscription.service.js";

interface ProPlanSubscriptionBody {
  plan: string;
}

interface ProPlanSubscriptionRequest extends Request {
  body: ProPlanSubscriptionBody;
}

export async function getShopSubscription(req: Request, res: Response): Promise<void> {
  try {
    const { shop } = res.locals.shopify.session;

    const shopDB = await Shop.findOne({ shop });
    const subscription = await Subscription.findOne({ shop });

    res.status(200).json({ data: { shop: shopDB, subscription }, message: "Success" });
  } catch (error) {
    console.log(error);

    res.status(400).json({ data: {}, message: "Error" });
  }
}

export async function freePlanSubscription(req: Request, res: Response): Promise<void> {
  try {
    const { session } = res.locals.shopify;
    const shop = session.shop;

    const subscription = await Subscription.findOne({ shop });
    if (subscription && subscription?.plan !== "free") {
      await cancelShopifySubscription(session, subscription);
    }

    await subscribeToFreePlan(shop);

    const shopData = await Shop.findOne({ shop: session.shop });
    if (shopData?.info) {
      fluentSubscriptionSwitch(shopData.info, "free");
    }

    res.status(200).json({ data: [], message: "Success" });
  } catch (error) {
    console.log(error);

    res.status(400).json({ data: {}, message: "Error" });
  }
}

export async function proPlanSubscription(req: ProPlanSubscriptionRequest, res: Response): Promise<void> {
  try {
    const { plan } = req.body;
    const { session } = res.locals.shopify;

    const url = await createShopifySubscription(session, plan);

    res.status(200).json({ data: { url }, message: "Success" });
  } catch (error) {
    console.log(error);

    res.status(400).json({ data: {}, message: "Error" });
  }
}
