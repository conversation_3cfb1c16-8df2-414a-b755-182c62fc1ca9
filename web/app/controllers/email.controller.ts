import { Session } from "@shopify/shopify-api";
import { Request, Response } from "express";
import Email from "../models/Email.js";
import { sendBulkEmail, sendTestEmail } from "../services/emailsend.service.js";
import { sendTestEmailValidation } from "../validations/sendTestEmailVallidation.js";

// Interface for Shopify authenticated response
interface ShopifyAuthenticatedResponse extends Response {
  locals: {
    shopify: {
      session: Session;
    };
  };
}

// Interface for remove mail request body
interface RemoveMailBody {
  ids?: string[];
}

// Interface for resend mail request body
interface ResendMailBody {
  ids?: string[];
}

// Interface for test mail request body
interface TestMailBody {
  email: string;
}

// Interface for validation result
interface ValidationResult {
  isValid: boolean;
  [key: string]: any;
}

interface RemoveMailRequest extends Request {
  body: RemoveMailBody;
}

interface ResendMailRequest extends Request {
  body: ResendMailBody;
}

interface TestMailRequest extends Request {
  body: TestMailBody;
}

export async function handleRemoveMail(req: RemoveMailRequest, res: Response): Promise<void> {
  try {
    const { ids } = req.body;
    if (ids?.length && ids.length > 0) {
      await Email.deleteMany({ _id: { $in: ids } });
    }

    res.status(200).json({ data: {}, message: "Success", status: 200 });
  } catch (error) {
    console.log("error while remove schedule email", error);

    res.status(400).json({ data: {}, message: "error while remove schedule email.", status: 400 });
  }
}

export async function handleResendMail(req: ResendMailRequest, res: Response): Promise<void> {
  try {
    const { shop } = res.locals.shopify.session;

    const { ids } = req.body;
    if (ids?.length && ids.length > 0) {
      await sendBulkEmail(shop, ids);
    }

    res.status(200).json({ data: {}, message: "Success", status: 200 });
  } catch (error) {
    console.log(error);

    res.status(400).json({ data: {}, message: "Error", status: 400 });
  }
}

export async function handleTestMail(req: TestMailRequest, res: Response): Promise<void> {
  try {
    const { shop } = res.locals.shopify.session;

    const validation: ValidationResult = sendTestEmailValidation.validateForm(req.body);
    if (!validation.isValid) {
      // delete validation.isValid;
      res.status(422).json({ data: {}, message: "Error", status: 422, errors: validation });
      return;
    }

    await sendTestEmail(shop, req.body.email);

    res.status(200).json({ data: {}, message: "Success", status: 200 });
  } catch (error) {
    console.log(error);

    res.status(400).json({ data: {}, message: "Error", status: 400 });
  }
}
