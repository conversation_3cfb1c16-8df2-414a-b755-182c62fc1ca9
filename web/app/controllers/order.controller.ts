import { Request, Response } from "express";
import Shop from "../models/Shop.js";
import { fetchAllOrders, sendReviewEmails, splitOrdersByDays } from "../services/order.service.js";

interface SchedulePastOrderBody {
  selectPastOrder?: number;
}

export async function getPastOrderList(req: Request, res: Response): Promise<void> {
  try {
    const { session } = res.locals.shopify;
    const { shop: shopDomain } = session;

    const shop = await Shop.findOne({ shop: shopDomain });
    if (!session || !shop || shop?.sentPastOrders) {
      res.status(400).json({ data: {}, message: "Error", status: 400 });
      return;
    }

    // Calculate the minimum date days ago
    const oldDay = 150;
    const shopInstalledOn = shop?.installedOn || new Date();

    const minDate = new Date(shopInstalledOn);
    minDate.setDate(minDate.getDate() - oldDay);

    const fromDate = minDate.toISOString();
    const toDate = shopInstalledOn.toISOString();

    const orders = await fetchAllOrders(session, fromDate, toDate);

    if (orders?.length === 0) {
      console.log("order not found.");
      await Shop.findOneAndUpdate({ shop: shopDomain }, { sentPastOrders: true }, { upsert: true });

      res.status(400).json({ data: {}, message: "Error", status: 400 });
      return;
    }

    res.status(200).json({ data: splitOrdersByDays(orders), message: "Success", status: 200 });
  } catch (error) {
    console.log(error);

    res.status(400).json({ data: {}, message: "Error", status: 400 });
  }
}

interface SchedulePastOrderRequest extends Request {
  body: SchedulePastOrderBody;
}

export const schedulePastOrder = async (req: SchedulePastOrderRequest, res: Response): Promise<void> => {
  try {
    const { session } = res.locals.shopify;
    const { shop: shopDomain } = session;

    const shop = await Shop.findOne({ shop: shopDomain });
    if (!session || !shop || shop?.sentPastOrders) {
      res.status(400).json({ data: {}, message: "Error", status: 400 });
      return;
    }

    const { selectPastOrder } = req.body;

    const oldDay = selectPastOrder || 150;
    const shopInstalledOn = shop?.installedOn || new Date();

    const minDate = new Date(shopInstalledOn);
    minDate.setDate(minDate.getDate() - oldDay);

    const fromDate = minDate.toISOString();
    const toDate = shopInstalledOn.toISOString();

    const orders = await fetchAllOrders(session, fromDate, toDate);

    const response = await sendReviewEmails(orders, shop);

    console.log({ response });

    res.status(200).json({ data: {}, message: "Success", status: 200 });
  } catch (error) {
    console.log(error);

    res.status(400).json({ data: {}, message: "Error", status: 400 });
  }
};
