import { Request, Response } from "express";
import { saveConfigurationSettings } from "../services/configuration-settings.service.js";

interface ConfigureSettingBody {
  value: any;
  key: string;
}

interface ConfigureSettingRequest extends Request {
  body: ConfigureSettingBody;
}

export async function handleConfigureSetting(req: ConfigureSettingRequest, res: Response): Promise<void> {
  try {
    const { value, key } = req.body;

    const { shop } = res.locals.shopify.session;

    await saveConfigurationSettings(shop, key, value);

    res.json({ data: {}, message: "Success", status: 200 });
  } catch (error) {
    console.log(error);

    res.json({ data: {}, message: "Error", status: 400 });
  }
}
