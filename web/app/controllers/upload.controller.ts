import { Upload } from "@aws-sdk/lib-storage";
import { Session } from "@shopify/shopify-api";
import { Request, Response } from "express";
import { s3Client } from "../clients/s3.js";
import EmailSettings from "../models/EmailSettings.js";

interface UploadedFile {
  originalname: string;
  buffer: Buffer;
  mimetype: string;
}

// Interface for file upload
interface UploadedFile {
  originalname: string;
  buffer: Buffer;
  mimetype: string;
}

export async function uploadShopLogo(req: Request & { file?: UploadedFile }, res: Response): Promise<void> {
  try {
    const file = req.file;
    if (!file) {
      res.status(400).json({ data: {}, message: "No file uploaded.", status: 400 });
      return;
    }

    const upload = new Upload({
      client: s3Client,
      params: {
        Bucket: process.env.STORAGE_BUCKET || "",
        Key: process.env.STORAGE_FOLDER ? `${process.env.STORAGE_FOLDER}/${file.originalname}` : file.originalname,
        Body: file.buffer,
        ContentType: file.mimetype,
        ACL: "public-read",
      },
    });

    await upload.done();

    const { shop } = res.locals.shopify.session;

    const originalname = file?.originalname || null;
    const logo = originalname ? `${process.env.STORAGE_CDN_BASE_URL}/${originalname}` : null;

    await EmailSettings.findOneAndUpdate({ shop }, { logo }, { upsert: true });

    res.status(200).json({ data: { logo }, message: "Success", status: 200 });
  } catch (error) {
    console.log({ error });
    res.status(400).json({ data: {}, message: "Error", status: 400 });
  }
}

export async function uploadShopImage(req: Request & { file?: UploadedFile }, res: Response): Promise<void> {
  try {
    const file = req.file;
    if (!file) {
      res.status(400).json({ data: {}, message: "No file uploaded.", status: 400 });
      return;
    }

    const upload = new Upload({
      client: s3Client,
      params: {
        Bucket: process.env.STORAGE_BUCKET || "",
        Key: process.env.STORAGE_FOLDER ? `${process.env.STORAGE_FOLDER}/${file.originalname}` : file.originalname,
        Body: file.buffer,
        ContentType: file.mimetype,
        ACL: "public-read",
      },
    });

    await upload.done();

    const originalname = file?.originalname || null;
    const url = originalname ? `${process.env.STORAGE_CDN_BASE_URL}/${originalname}` : null;

    res.status(200).json({ data: { url }, message: "Success", status: 200 });
  } catch (error) {
    console.log({ error });
    res.status(400).json({ data: {}, message: "Error", status: 400 });
  }
}
