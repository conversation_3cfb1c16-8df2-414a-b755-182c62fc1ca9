import { Request, Response } from "express";
import { ParsedQs } from "qs";
import Email from "../models/Email.js";

interface AnalyticsQuery extends ParsedQs {
  page?: string;
  pageSize?: string;
  selected?: string;
  platform?: string;
  q?: string;
  since?: string;
  until?: string;
}

interface AnalyticsRequest extends Request {
  query: AnalyticsQuery;
}

export async function analyticsInfo(req: AnalyticsRequest, res: Response): Promise<void> {
  try {
    const { page = "1", pageSize = "10", selected = "0", platform, q = "", since = "", until = "" } = req.query;

    // Parse query parameters
    const parsedPage = parseInt(page, 10);
    const parsedPageSize = parseInt(pageSize, 10);
    const parsedSelected = parseInt(selected, 10);
    const skip = (parsedPage - 1) * parsedPageSize;

    const { shop } = res.locals.shopify.session;

    const queryConditions: any = {
      shop,
      ...(q && {
        $or: [{ firstName: { $regex: q, $options: "i" } }, { email: { $regex: q, $options: "i" } }],
      }),
    };

    if (platform) {
      queryConditions.reviewPlatform = platform;
    }

    queryConditions.sent = parsedSelected ? true : false;

    const dateFilter: any = {};
    if (since) {
      const sinceDate = new Date(since);
      sinceDate.setHours(0, 0, 0, 0); // Start of the 'since' date
      dateFilter.$gte = sinceDate;
    }

    if (until) {
      const untilDate = new Date(until);
      untilDate.setHours(23, 59, 59, 999); // End of the 'until' date
      dateFilter.$lte = untilDate;
    }

    // Set default to the last 30 days if dateFilter is empty
    if (Object.keys(dateFilter).length === 0) {
      const now = new Date();
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(now.getDate() - 30);
      thirtyDaysAgo.setHours(0, 0, 0, 0); // Start of the range
      now.setHours(23, 59, 59, 999); // End of the range

      dateFilter.$gte = thirtyDaysAgo;
      dateFilter.$lte = now;
    }

    const emails = await Email.find({ ...queryConditions, [parsedSelected ? "sendDate" : "created_at"]: dateFilter })
      .skip(skip)
      .limit(parsedPageSize)
      .sort({ [parsedSelected ? "sendDate" : "created_at"]: -1 });

    const totalPendingItems = await Email.find({
      ...queryConditions,
      sent: false,
      created_at: dateFilter,
    }).countDocuments({});

    const totalSentItems = await Email.find({
      ...queryConditions,
      sent: true,
      sendDate: dateFilter,
    }).countDocuments({});

    const totalOpenItems = await Email.find({
      ...queryConditions,
      sent: true,
      opened: true,
      sendDate: dateFilter,
    }).countDocuments({});

    const totalClickItems = await Email.find({
      ...queryConditions,
      sent: true,
      clicked: true,
      sendDate: dateFilter,
    }).countDocuments({});

    res.status(200).json({
      data: {
        emails,
        pagination: {
          page: parsedPage,
          pageSize: parsedPageSize,
          totalItems: parsedSelected ? totalSentItems : totalPendingItems,
        },
        states: {
          totalPending: totalPendingItems,
          totalSent: totalSentItems,
          totalOpen: totalOpenItems,
          totalClick: totalClickItems,
        },
      },
      message: "Success",
      status: 200,
    });
  } catch (error) {
    console.log(error);

    res.status(400).json({ data: {}, message: "Error", status: 400 });
  }
}
