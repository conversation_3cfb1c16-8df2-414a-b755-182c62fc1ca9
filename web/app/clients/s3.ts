import { GetObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { Readable } from "stream";

const STORAGE_ACCESS_KEY = process.env.STORAGE_ACCESS_KEY;
const STORAGE_SECRET = process.env.STORAGE_SECRET;
const STORAGE_REGION = process.env.STORAGE_REGION;
const STORAGE_BUCKET = process.env.STORAGE_BUCKET;
const STORAGE_ENDPOINT = process.env.STORAGE_ENDPOINT;
const STORAGE_FOLDER = process.env.STORAGE_FOLDER;

if (!STORAGE_ACCESS_KEY || !STORAGE_SECRET || !STORAGE_REGION || !STORAGE_BUCKET || !STORAGE_ENDPOINT) {
  throw new Error(`Storage is missing required configuration.`);
}

const streamToBuffer = async (stream: Readable): Promise<Buffer> => {
  const result: Buffer[] = [];
  for await (const chunk of stream) {
    result.push(chunk);
  }
  return Buffer.concat(result);
};

export const s3Client = new S3Client({
  endpoint: STORAGE_ENDPOINT,
  credentials: {
    accessKeyId: STORAGE_ACCESS_KEY,
    secretAccessKey: STORAGE_SECRET,
  },
  region: STORAGE_REGION,
});

export const getAttachmentStreamFromS3 = async (filename: string): Promise<Buffer> => {
  try {
    // Create the GetObject command
    const getObjectCommand = new GetObjectCommand({
      Bucket: STORAGE_BUCKET,
      Key: STORAGE_FOLDER ? `${STORAGE_FOLDER}/${filename}` : filename,
    });

    // Fetch the object from S3
    const data = await s3Client.send(getObjectCommand);

    // Convert the S3 data.Body stream into a Buffer
    const fileBuffer = await streamToBuffer(data.Body as Readable);

    return fileBuffer;
  } catch (err) {
    throw new Error(err as string);
  }
};
