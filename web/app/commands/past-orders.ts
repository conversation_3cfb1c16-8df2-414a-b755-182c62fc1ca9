import { ordersGraphQuery } from "../queries/orders.query";
import { graphqlClient } from "./shopify.api";

// Interface for order edge
interface OrderEdge {
  node: any;
  cursor: string;
}

// Interface for page info
interface PageInfo {
  hasNextPage: boolean;
  endCursor: string;
}

// Interface for orders response
interface OrdersResponse {
  orders: {
    edges: OrderEdge[];
    pageInfo: PageInfo;
  };
}

const fetchAllOrders = async (fromDate: string, toDate: string, after: string | null = null): Promise<OrderEdge[]> => {
  const response = await graphqlClient.request(ordersGraphQuery(fromDate, toDate), {
    variables: { after },
  });

  const { edges, pageInfo } = response.data.orders;

  // Recursively fetch more pages if there are more results
  if (pageInfo.hasNextPage) {
    const nextPage = await fetchAllOrders(fromDate, toDate, pageInfo.endCursor);
    return edges.concat(nextPage); // Combine current page with the next
  }

  return edges; // No more pages, return the current results
};

const shopInstalledOn = new Date();
const minDate = new Date(shopInstalledOn);
minDate.setDate(minDate.getDate() - 20);

const fromDate = minDate.toISOString();
const toDate = shopInstalledOn.toISOString();

console.log(await fetchAllOrders(fromDate, toDate));

process.exit();
