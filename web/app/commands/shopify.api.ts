import { ApiVersion, shopifyApi } from "@shopify/shopify-api";
import "@shopify/shopify-api/adapters/node";
import dotenv from "dotenv";
import { session } from "./redis-session";

dotenv.config();

export const shopify: any = shopifyApi({
  apiKey: process.env.SHOPIFY_API_KEY as string,
  apiSecretKey: process.env.SHOPIFY_API_SECRET as string,
  scopes: (process.env.SCOPES as string).split(","),
  hostName: process.env.SHOPIFY_APP_URL as string,
  apiVersion: "2024-10" as ApiVersion,
  isEmbeddedApp: true,
});

export const graphqlClient: any = new shopify.clients.Graphql({ session });
export const restClient: any = new shopify.clients.Rest({ session });
