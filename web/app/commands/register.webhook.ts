import webhookSubscriptionCreateMutation from "../queries/webhookSubscriptionCreate.mutation";
import webhooks from "../utils/webhooks";
import { graphqlClient } from "./shopify.api";

async function registerWebhooks(): Promise<void> {
  for (const webhook of webhooks) {
    try {
      const {
        data: { webhookSubscriptionCreate },
      } = await graphqlClient.request(webhookSubscriptionCreateMutation, {
        variables: {
          topic: webhook.topic,
          webhookSubscription: {
            callbackUrl: webhook.callbackUrl,
            format: webhook.format,
          },
        },
      });

      console.log(webhookSubscriptionCreate);
    } catch (error: any) {
      console.error(`Failed to register webhook for topic ${webhook.topic}:`, error);
    }
  }
}

registerWebhooks().then(() => {
  process.exit();
});
