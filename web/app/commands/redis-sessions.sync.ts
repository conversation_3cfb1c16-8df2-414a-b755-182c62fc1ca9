import { Session } from "@shopify/shopify-api";
import "@shopify/shopify-api/adapters/node";
import { RedisSessionStorage } from "@shopify/shopify-app-session-storage-redis";
import dotenv from "dotenv";
import connectDB from "../../database.js";
import Sessions, { ISessionsDocument } from "../models/Sessions";

dotenv.config({ path: "./web/.env" });

connectDB();

const redis = RedisSessionStorage.withCredentials(
  process.env.REDIS_HOST as string,
  parseInt(process.env.REDIS_DATABASE as string, 10),
  process.env.REDIS_USERNAME as string,
  process.env.REDIS_PASSWORD as string,
  {}
);

const filteredSessions: ISessionsDocument[] = await Sessions.find({ id: /offline_/ }); // Fetch sessions where `id` starts with `offline_`.

console.log(`Webhook will process: ${filteredSessions.length}`);

async function syncWithRedis(): Promise<void> {
  for (const filteredSession of filteredSessions) {
    if (!filteredSession.payload) {
      continue;
    }

    const inputSession = JSON.parse(filteredSession.payload);
    if (!inputSession) {
      continue;
    }

    const session = new Session(inputSession);

    await redis.storeSession(session);
  }
}

syncWithRedis().then(() => {
  process.exit();
});
