import dotenv from "dotenv";
import FormData from "form-data";
import Mailgun from "mailgun.js";

dotenv.config();

const mailgun = new Mailgun(FormData);

const mailgunServer = mailgun.client({
  username: process.env.MAILGUN_API_KEY as string,
  key: process.env.MAILGUN_API_KEY as string,
});

// const response = await mailgunServer.domains.get("pensxpress.com");

// console.log(response);

const data = await mailgunServer.domains.create({
  name: "pensxpress.com",
  smtp_password: "temp_password_123",
});

console.log(data);
