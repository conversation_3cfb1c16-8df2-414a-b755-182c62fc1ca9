import dotenv from "dotenv";
import FormData from "form-data";
import Mailgun from "mailgun.js";
import { weeklyReportEmailTemplate } from "../services/email-template.service";

dotenv.config();

// Interface for test shop data
interface TestShop {
  email: string;
  shopName: string;
  info: {
    shop_owner: string;
  };
}

// Initialize Mailgun client
const mailgun = new Mailgun(FormData);
const mailgunClient = mailgun.client({
  username: process.env.MAILGUN_API_KEY as string,
  key: process.env.MAILGUN_API_KEY as string,
});

(async (): Promise<void> => {
  try {
    // Test recipient setup
    const shop: TestShop = {
      email: "<EMAIL>",
      shopName: "abir-new-test-store",
      info: {
        shop_owner: "Abir Hossain",
      },
    };

    const appName = "trustsync-test-app";

    const domain = "trustsync.dev";
    const sendFromEmail = `TrustSync <hello@${domain}>`;
    const sendToEmail = shop?.email;

    const analyticsPageUrl = `https://admin.shopify.com/store/${shop?.shopName}/apps/${appName}/analytics`;
    const emailHtml = weeklyReportEmailTemplate(shop as any, 2, 2, analyticsPageUrl);

    const mailOptions = {
      from: sendFromEmail,
      to: sendToEmail,
      subject: `Your weekly review request summary is ready 🚀`,
      html: emailHtml,
    };

    // Send the email
    console.log(`Sending email to ${sendToEmail} via ${domain}...`);
    const response = await mailgunClient.messages.create(domain, mailOptions);
    console.log("Email sent successfully:", response);
  } catch (error: any) {
    console.error("Error sending email:", error);
  }
})();
