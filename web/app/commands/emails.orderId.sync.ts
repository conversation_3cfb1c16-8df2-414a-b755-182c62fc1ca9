// import "@shopify/shopify-api/adapters/node";
import dotenv from "dotenv";
import connectDB from "../../database";
import Email, { IEmailDocument } from "../models/Email";
import { extractId } from "../utils/helper";

dotenv.config();

connectDB();

const orderEmails: IEmailDocument[] = await Email.find({ orderId: /gid/ }); // Fetch Email where `id` starts with `offline_`.

console.log(`Email will process: ${orderEmails.length}`);

async function processOrderEmails(): Promise<void> {
  for (const orderEmail of orderEmails) {
    const extractedId = extractId(String(orderEmail.orderId || ""));
    orderEmail.orderId = typeof extractedId === "number" ? extractedId.toString() : extractedId;
    await orderEmail.save();
  }
}

processOrderEmails().then(() => {
  console.log("Success");
  process.exit();
});
