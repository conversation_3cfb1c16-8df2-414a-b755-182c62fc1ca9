import { NextFunction, Request, Response } from "express";
import ReRegisterWebhook from "../models/ReRegisterWebhook.js";
import { installShop } from "../services/shop.service.js";

const shopInstallMiddleware = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { session } = res.locals.shopify;
    const { shop: shopDomain } = session;
    // Handle shop installation after successful authentication
    await installShop(session);
    // Proceed to the next middleware

    await ReRegisterWebhook.findOneAndUpdate({ shop: shopDomain }, { status: true }, { upsert: true });

    next();
  } catch (error) {
    console.error("Error during shop installation:", error);
    // Return an error response or pass the error to the next middleware
    return next(error);
  }
};

export default shopInstallMiddleware;
