import { Request, Response } from "express";
import morgan from "morgan";

const morganMiddleware = morgan(
  (tokens, req: Request, res: Response) => {
    const status = tokens.status(req, res);

    return [
      status,
      "-",
      tokens.method(req, res),
      "-",
      tokens.url(req, res),
      "-",
      tokens["response-time"](req, res) + "ms",
    ].join(" ");
  },
  {
    skip: (req: Request, res: Response) => {
      return req.url.includes("_next") || req.url.includes("img") || req.url.includes("locales");
    },
  }
);

export default morganMiddleware;
