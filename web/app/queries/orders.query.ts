export const ordersGraphQuery = (fromDate: string, toDate: string): string => {
  return `query($after: String) {
        orders(
            first: 250,
            after:$after,
            sortKey:CREATED_AT,
            reverse: true,
            query:"financial_status:paid created_at:>=${fromDate} created_at:<=${toDate}"
        ) {
            edges {
                node {
                    id
                    name
                    email
                    customer {
                    id
                    displayName
                    firstName
                    lastName
                    }
                    createdAt
                }
            },
            pageInfo {
                hasPreviousPage
                hasNextPage
                startCursor
                endCursor
            }
        }
    }`;
};
