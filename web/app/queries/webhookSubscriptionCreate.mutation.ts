const webhookSubscriptionCreateMutation: string = `mutation WebhookSubscriptionCreate($topic: WebhookSubscriptionTopic!, $webhookSubscription: WebhookSubscriptionInput!) {
    webhookSubscriptionCreate(topic: $topic, webhookSubscription: $webhookSubscription) {
        userErrors {
            field
            message
        }
        webhookSubscription {
            id
            topic
            apiVersion {
                handle
            }
            format
            createdAt
        }
    }
}`;

export default webhookSubscriptionCreateMutation;
