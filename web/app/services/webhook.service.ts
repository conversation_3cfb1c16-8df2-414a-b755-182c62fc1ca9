import WebhookModel from "../models/Webhook.js";

export async function processOrderWebhook(topic: string, shop: string, payload: any): Promise<void> {
  console.log(`Webhook ${topic} call from webhooks.handle-order.${topic} route page`);

  const newWebhook = new WebhookModel({
    topic,
    payload,
    shop,
    status: "TO_TREAT",
    treated_date: null,
    last_processing_date: null,
  });

  await newWebhook.save();
}

export async function processUninstallWebhook(topic: string, shop: string, payload: any): Promise<void> {
  console.log(`Webhook ${topic} call for app uninstall`);

  const newWebhook = new WebhookModel({
    topic,
    payload,
    shop,
    status: "TO_TREAT",
    treated_date: null,
    last_processing_date: null,
  });

  await newWebhook.save();
}
