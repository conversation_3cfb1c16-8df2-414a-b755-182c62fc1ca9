import { Session } from "@shopify/shopify-api";
import { addDays } from "date-fns";
import "dotenv/config";
import cron from "node-cron";

import EmailSettings from "../models/EmailSettings.js";
import Shop from "../models/Shop.js";
import Subscription, { ISubscriptionDocument } from "../models/Subscription.js";

import { extractId } from "../utils/helper.js";
import { getCurrentPlan, getPlan } from "../utils/plans.js";
import { fluentSubscriptionSwitch } from "./fluent-crm.service.js";
import { graphCancelSubscription, graphCreateSubscription } from "./graph.api.js";

const freePlanShopEmailPattern = "@wpdeveloper.com";

// Interface for subscription payload
interface SubscriptionPayload {
  name: string;
  created_at: string;
  status: string;
}

// Interface for subscription response
interface SubscriptionResponse {
  message: string;
  subscription?: ISubscriptionDocument;
  payload?: SubscriptionPayload;
}

const subscriptionReturnUrl = (shop: string): string => {
  const myShopifyUsername = shop.replace(".myshopify.com", "");
  return `https://admin.shopify.com/store/${myShopifyUsername}/apps/` + process.env.APP_NAME;
};

// Schedule to reset subscription quota every 30 minutes
cron.schedule("*/30 * * * *", () => {
  resetSubscriptionQuota();
});

// Free plan subscription
export const subscribeToFreePlan = async (shop: string): Promise<void> => {
  const selectedPlan = getPlan("free");

  if (!selectedPlan) {
    throw new Error("Free plan not found");
  }

  const subscription = await Subscription.findOneAndUpdate(
    { shop },
    {
      plan: "free",
      limitEmails: selectedPlan.emails,
      chargeId: null,
      tmpChargeId: null,
      dateBilled: null,
    },
    { upsert: true }
  );

  if (!subscription) {
    throw new Error("Failed to create or update subscription");
  }

  if ((subscription.emails || 0) > 50) {
    subscription.valid = false;
    subscription.error = "Impressions exceeded for this plan, please upgrade!";
  }

  await subscription.save();
};

// Pro plan subscription
export const subscribeToProPlan = async (shop: string, payload: SubscriptionPayload): Promise<void> => {
  const { name, created_at } = payload;
  const selectedPlan = getCurrentPlan(name.toLowerCase());

  if (!selectedPlan) {
    throw new Error("Pro plan not found");
  }

  const subscription = await Subscription.findOne({ shop });
  if (!subscription) {
    throw new Error("Subscription not found");
  }

  subscription.plan = name.toLowerCase();
  subscription.chargeId = subscription.tmpChargeId;
  subscription.tmpChargeId = null;
  subscription.status = "active";
  subscription.dateBilled = new Date(created_at);
  subscription.limitEmails = selectedPlan?.emails || 50;
  subscription.valid = true;
  subscription.error = null;

  // reset email quota and update resetDate
  subscription.emails = 0;
  subscription.resetDate = new Date(addDays(new Date(), 30));

  await subscription.save();
};

// when user switch to another subscription plan
export const createShopifySubscription = async (session: Session, plan: string): Promise<string> => {
  const shop = await Shop.findOne({ shop: session.shop });
  const selectedPlan = getPlan(plan);
  const shopEmail = shop?.info?.email || "";

  if (!selectedPlan) {
    throw new Error("Pro plan not found");
  }

  const { confirmationUrl, appSubscription } = await graphCreateSubscription(session, {
    name: selectedPlan.name.toUpperCase(),
    returnUrl: subscriptionReturnUrl(session.shop),
    lineItems: [
      {
        plan: {
          appRecurringPricingDetails: {
            price: {
              amount: selectedPlan.discountPrice,
              currencyCode: "USD",
            },
            interval: "EVERY_30_DAYS",
          },
        },
      },
    ],
    test: shopEmail.includes(freePlanShopEmailPattern),
  });

  await Subscription.findOneAndUpdate(
    { shop: session.shop },
    {
      tmpChargeId: extractId(appSubscription.id),
    },
    { upsert: true }
  );

  return confirmationUrl;
};

// when user cancel subscription plan
export const cancelShopifySubscription = async (
  session: Session,
  subscription: ISubscriptionDocument
): Promise<{ message: string }> => {
  const chargeId = subscription?.chargeId;

  try {
    await graphCancelSubscription(session, `gid://shopify/AppSubscription/${chargeId}`);

    await EmailSettings.findOneAndUpdate(
      { shop: session.shop },
      {
        customersPick: false,
        secondEmail: false,
        onlyOnlineStoreOrders: false,
        customNegativeForm: false,
        customNegativeLink: null,
        blacklistedEmails: [],
      },
      { upsert: true }
    );

    console.log(`Deleted recurring chargeID: ${chargeId}`);
  } catch (error: any) {
    console.error("Error when deleting recurring chargeId", { chargeId, message: error.message });
  } finally {
    return { message: "Charge Cancelled" };
  }
};

// Shopify subscription webhook verify.
export const verifyWebhookShopifySubscription = async (
  shop: string,
  payload: SubscriptionPayload
): Promise<SubscriptionResponse> => {
  const subscription = await Subscription.findOne({ shop });

  if (payload.status.toLowerCase() === "active") {
    if (!subscription?.tmpChargeId) {
      return { message: "Shopify temp chargeId not found.", subscription: subscription || undefined };
    }

    await subscribeToProPlan(shop, payload);

    const shopDB = await Shop.findOne({ shop });
    if (shopDB?.info) {
      fluentSubscriptionSwitch(shopDB.info, payload.name.toLowerCase());
    }
  }
  return { message: "Subscription updated", payload };
};

// Function to reset subscription quota
export const resetSubscriptionQuota = async (): Promise<void> => {
  console.log("===resetSubscriptionQuota function");

  // Find subscriptions whose resetDate is less than or equal to the current date
  const endingCycle = await Subscription.find({
    resetDate: { $lte: new Date() },
  });

  // If no subscriptions need resetting, return early
  if (endingCycle.length === 0) {
    return;
  }

  // Iterate over subscriptions that need resetting
  for (const subscription of endingCycle) {
    let newDate: Date;

    // Calculate new resetDate based on existing resetDate or subscription creation date
    const resetDate = new Date(subscription.resetDate ?? subscription.createdAt);
    newDate = new Date(addDays(resetDate, 30));

    // Reset email quota and update resetDate
    subscription.emails = 0;
    subscription.valid = true;
    subscription.resetDate = newDate;

    // Save the updated subscription
    await subscription.save();
  }
};
