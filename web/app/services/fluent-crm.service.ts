import { ShopInfo } from "@trustsync/types";
import "dotenv/config";

// Basic auth credentials
const FLUENT_BASE_URL = "https://trustsync.io/wp-json/fluent-crm/v2";
const FLUENT_USERNAME = "rostomali";
const FLUENT_PASSWORD = "MUrR WAhk zICL 5Vnl 9FB2 RUZi";
const FLUENT_AUTH_HEADER = {
  Authorization: "Basic " + Buffer.from(`${FLUENT_USERNAME}:${FLUENT_PASSWORD}`).toString("base64"),
};

export const FLUENT_DEFAULT_LIST_SLUG =
  process.env.APP_ENVIRONMENT === "development" ? "trustsyncdevcontact" : "trustsyncprodcontact";

console.log({ FLUENT_DEFAULT_LIST_SLUG });

export const FLUENT_APP_INSTALLED_TAG_SLUG = "app_installed";
export const FLUENT_APP_UNINSTALLED_TAG_SLUG = "app_uninstalled";

export const FLUENT_FREE_PLAN_TAG_SLUG = "free";
export const FLUENT_BASIC_PLAN_TAG_SLUG = "basic";
export const FLUENT_PRO_PLAN_TAG_SLUG = "pro";
export const FLUENT_ELITE_PLAN_TAG_SLUG = "elite";
export const FLUENT_ENTERPRISE_PLAN_TAG_SLUG = "enterprise";

// Interface for FluentCRM subscriber response
interface FluentCRMSubscriber {
  id: number;
  email: string;
  first_name?: string;
  last_name?: string;
  status: string;
  [key: string]: any;
}

// Interface for FluentCRM API response
interface FluentCRMResponse {
  data?: FluentCRMSubscriber[];
  subscriber?: FluentCRMSubscriber;
  [key: string]: any;
}

// Interface for processed contact data
interface ProcessedContact {
  first_name?: string;
  email?: string;
  phone?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  country?: string;
  custom_values: {
    shop_name?: string;
    shop_email?: string;
    shop_domain?: string;
    shop_myshopify_domain?: string;
  };
}

export const getCrmSubscriberList = async (): Promise<FluentCRMResponse> => {
  try {
    const response = await fetch(`${FLUENT_BASE_URL}/subscribers`, {
      method: "GET",
      headers: FLUENT_AUTH_HEADER,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status} - ${response.statusText}`);
    }

    return await response.json();
  } catch (error: unknown) {
    console.error("Error fetching subscribers:", error as Error);
    throw error; // Rethrow the error for higher-level handling
  }
};

export const getCrmSubscriberListById = async (id: string | number): Promise<FluentCRMResponse> => {
  try {
    const response = await fetch(`${FLUENT_BASE_URL}/subscribers/${id}`, {
      method: "GET",
      headers: FLUENT_AUTH_HEADER,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status} - ${response.statusText}`);
    }

    return await response.json();
  } catch (error: unknown) {
    console.error("Error fetching subscribers:", error as Error);
    throw error; // Rethrow the error for higher-level handling
  }
};

export const getCrmSubscriberListByEmail = async (email: string): Promise<FluentCRMResponse> => {
  try {
    const response = await fetch(`${FLUENT_BASE_URL}/subscribers/0?get_by_email=${email}`, {
      method: "GET",
      headers: FLUENT_AUTH_HEADER,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status} - ${response.statusText}`);
    }

    return await response.json();
  } catch (error: unknown) {
    console.error("Error fetching subscribers:", error as Error);
    throw error; // Rethrow the error for higher-level handling
  }
};

export const processContact = (data: ShopInfo): ProcessedContact => {
  return {
    first_name: data?.shop_owner,
    email: data?.email,
    phone: data?.billingAddress?.phone,
    address1: data?.billingAddress?.address1,
    address2: data?.billingAddress?.address2,
    city: data?.billingAddress?.city,
    state: data?.billingAddress?.province,
    country: data?.billingAddress?.country,
    custom_values: {
      shop_name: data?.name,
      shop_email: data?.email,
      shop_domain: data?.myshopifyDomain,
      shop_myshopify_domain: data?.myshopifyDomain,
    },
  };
};

export const fluentAppInstalledUninstall = async (data: ShopInfo, slug: string): Promise<void> => {
  try {
    const detachTags = [FLUENT_APP_INSTALLED_TAG_SLUG, FLUENT_APP_UNINSTALLED_TAG_SLUG].filter((tag) => {
      return tag != slug;
    });

    const userData = processContact(data);

    const postData = {
      ...userData,
      status: "subscribed",
      __force_update: "yes",
      lists: [FLUENT_DEFAULT_LIST_SLUG],
      tags: [slug],
      detach_tags: detachTags,
    };

    const response = await fetch(`${FLUENT_BASE_URL}/subscribers`, {
      method: "POST",
      headers: {
        ...FLUENT_AUTH_HEADER,
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      body: JSON.stringify(postData),
    });

    if (!response.ok) {
      const errData = await response.json();
      console.error(`Error: ${response.status} - ${response.statusText}`, errData);
      throw new Error(`Failed to add contact person. Server responded with: ${response.statusText}`);
    }

    await response.json();

    console.log("Succcess fluentAppInstalledUninstall");
  } catch (error) {
    console.log(error);
  }
};

export const fluentSubscriptionSwitch = async (data: ShopInfo, slug: string): Promise<void> => {
  try {
    const detachTags = [
      FLUENT_FREE_PLAN_TAG_SLUG,
      FLUENT_BASIC_PLAN_TAG_SLUG,
      FLUENT_PRO_PLAN_TAG_SLUG,
      FLUENT_ELITE_PLAN_TAG_SLUG,
      FLUENT_ENTERPRISE_PLAN_TAG_SLUG,
    ].filter((tag) => {
      return tag != slug;
    });

    const userData = processContact(data);

    const postData = {
      ...userData,
      status: "subscribed",
      __force_update: "yes",
      lists: [FLUENT_DEFAULT_LIST_SLUG],
      tags: [slug],
      detach_tags: detachTags,
    };

    const response = await fetch(`${FLUENT_BASE_URL}/subscribers`, {
      method: "POST",
      headers: {
        ...FLUENT_AUTH_HEADER,
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      body: JSON.stringify(postData),
    });

    if (!response.ok) {
      const errData = await response.json();
      console.error(`Error: ${response.status} - ${response.statusText}`, errData);
      throw new Error(`Failed to add contact person. Server responded with: ${response.statusText}`);
    }

    await response.json();

    console.log("Succcess fluentSubscriptionSwitch");
  } catch (error) {
    console.log(error);
  }
};
