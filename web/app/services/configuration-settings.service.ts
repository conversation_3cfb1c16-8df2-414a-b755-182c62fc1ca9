import configureSettings from "../enums/configureSettings.js";
import ConfigurationSettings from "../models/ConfigurationSettings.js";

const CONFIGURE_SETTINGS = [
  configureSettings.EMAIL_CONFIGURE_SETTING,
  configureSettings.REVIEW_CONFIGURE_SETTING,
  configureSettings.PARTNER_CONFIGURE_SETTING,
  configureSettings.CONTACT_SUPPORT_CONFIGURE_SETTING,
] as const;

// Interface for configuration settings result
interface ConfigurationSettingsResult {
  [key: string]: boolean;
}

export const saveConfigurationSettings = async (shop: string, key: string, value: string | Date): Promise<void> => {
  await ConfigurationSettings.findOneAndUpdate({ shop, key }, { shop, key, value }, { upsert: true });
};

export const processConfigureSettings = async (shop: string): Promise<ConfigurationSettingsResult> => {
  const settings = await ConfigurationSettings.find({ shop });

  return CONFIGURE_SETTINGS.reduce((acc: ConfigurationSettingsResult, key) => {
    const setting = settings.find((s) => s.key === key);
    acc[key] = Boolean(setting ? setting.value && new Date(setting.value) < new Date() : true);
    return acc;
  }, {});
};
