import { WhenToSend } from "@trustsync/types";
import EmailSettings from "../models/EmailSettings.js";
import Shop from "../models/Shop.js";

// Interface for review setting form data
export interface ReviewSettingFormData {
  reviewLinks: any[];
  autoPublish: string;
  customNegativeForm: boolean;
  customNegativeLink: string;
  [key: string]: any;
}

// Interface for general setting form data
export interface GeneralSettingFormData {
  active: boolean;
  whenToSend: WhenToSend;
  sendOnRepeat: string;
  secondEmail: boolean;
  secondWhenToSend: WhenToSend;
  specificTimeActive: boolean;
  specificTime: string;
  onlyOnlineStoreOrders: boolean;
  sender: string;
  selectedReplyToEmails: string[];
  selectedBlacklistEmails: string[];
  sentPastOrders: boolean;
}

// Interface for email design setting form data
export interface EmailDesignSettingFormData {
  from?: string;
  subject?: string;
  emailHTML?: string;
  bgColor?: string;
  borderColor?: string;
  logo?: string;
  logoHeight?: number;
  designTemplate?: string;
  [key: string]: any;
}

export const saveReviewSetting = async (shop: string, formData: ReviewSettingFormData): Promise<void> => {
  await EmailSettings.findOneAndUpdate({ shop }, formData, { upsert: true });
};

export const saveGeneralSetting = async (shop: string, formData: GeneralSettingFormData): Promise<void> => {
  await EmailSettings.findOneAndUpdate(
    { shop },
    {
      active: formData.active,
      whenToSend: formData.whenToSend,
      sendOnRepeat: formData.sendOnRepeat,
      secondEmail: formData.secondEmail,
      secondWhenToSend: formData.secondWhenToSend,
      specificTimeActive: formData.specificTimeActive,
      specificTime: formData.specificTime,
      onlyOnlineStoreOrders: formData.onlyOnlineStoreOrders,
      sender: formData.sender,
      replyToEmails: formData.selectedReplyToEmails,
      blacklistedEmails: formData.selectedBlacklistEmails,
    },
    { upsert: true }
  );

  await Shop.findOneAndUpdate({ shop }, { sentPastOrders: formData.sentPastOrders }, { upsert: true });
};

export const saveEmailDesignSetting = async (shop: string, formData: EmailDesignSettingFormData): Promise<void> => {
  await EmailSettings.findOneAndUpdate({ shop }, formData, { upsert: true });
};
