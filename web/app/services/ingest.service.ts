import { addDays } from "date-fns";
import dayjs from "dayjs";

import { Error } from "mongoose";
import Email from "../models/Email.js";
import EmailSettings, { IEmailSettingsDocument } from "../models/EmailSettings.js";
import Subscription, { ISubscriptionDocument } from "../models/Subscription.js";
import WebhookModel, { IWebhookDocument } from "../models/Webhook.js";
import { extractId } from "../utils/helper.js";

// import { FLUENT_APP_UNINSTALLED_TAG_SLUG, fluentAppInstalledUninstall } from "../services/fluent-crm.service.js";

// Interface for webhook processing result
interface WebhookResult {
  status: boolean;
  message: string;
}

// Interface for order payload from webhook
interface OrderPayload {
  id: string;
  order_number: string;
  email?: string;
  source_name?: string;
  tags?: string;
  customer?: {
    first_name?: string;
    last_name?: string;
  };
  [key: string]: any;
}

export const webhookIngest = async (): Promise<WebhookResult> => {
  // Delete useless treated webhooks
  await WebhookModel.deleteMany({
    status: "TREATED",
    treated_date: { $lt: dayjs().subtract(2, "weeks") },
    // topic: { $in: ["ORDERS_CREATED"] },
  });

  // Get the older first
  const where = {
    $and: [
      {
        $or: [{ status: "TO_TREAT" }, { status: "ERROR", next_retry_date: { $lt: dayjs() } }],
      },
      {
        retry_count: { $lt: 10 },
      },
    ],
  };

  // We sort by status because we want status: ERROR to be prioritized over TO_TREAT
  const webhooksDB = await WebhookModel.find(where).sort("status created_at").limit(40);
  if (!webhooksDB || webhooksDB.length === 0) {
    console.log("No webhook to treat");
    return errorMessage("No webhook to treat");
  }

  console.log("=== webhook to treat", webhooksDB.length);
  for (let webhookDB of webhooksDB) {
    await processWebhook(webhookDB);
  }

  return successMessage("Process complete.");
};

const processWebhook = async (webhookDB: IWebhookDocument): Promise<void> => {
  console.log(
    `Start processaing webhook id=${webhookDB._id}, shop=${webhookDB.shop}, topic=${webhookDB.topic}, created_at=${webhookDB.created_at}`
  );

  try {
    let result;
    if (["ORDERS_CREATE", "ORDERS_PAID", "ORDERS_FULFILLED", "ORDERS_UPDATED"].includes(webhookDB.topic)) {
      result = await orderProcess(
        { shop: webhookDB.shop, payload: webhookDB.payload as OrderPayload },
        webhookDB.topic
      );
    } else {
      result = errorMessage("Not managed webhook");
    }

    console.log("Webhook status: " + result?.message);

    if (result.status) {
      const datenow = new Date();

      webhookDB.status = "TREATED";
      webhookDB.last_processing_date = datenow;
      webhookDB.treated_date = datenow;

      // If there is a note, we save it
      if (result.message) {
        webhookDB.message = result.message;
      }

      // Save the webhook
      await webhookDB.save();
    } else {
      await manageFail(webhookDB, result.message);
    }
  } catch (err: unknown) {
    console.log("===err", err);
    const errorMessage = err instanceof Error 
      ? err.stack ? err.stack.toString() : err.message
      : String(err);
    await manageFail(webhookDB, errorMessage);
  }
};

const orderProcess = async (
  { shop, payload }: { shop: string; payload: OrderPayload },
  topic: string
): Promise<WebhookResult> => {
  console.log({ payload });
  // for topic ORDERS_CREATE, ORDERS_PAID, ORDERS_FULFILLED, ORDERS_UPDATED
  const settings = await EmailSettings.findOne({ shop });
  if (!settings) {
    return successMessage("No email setting found.");
  }

  if (!settings.active) {
    return successMessage("Settings disabled.");
  }

  const emailWhenToSend = settings?.whenToSend?.after;
  // ORDERS_CREATE
  if (topic === "ORDERS_CREATE" && emailWhenToSend !== "created") {
    return successMessage("Not for created");
  }

  // ORDERS_PAID
  if (topic === "ORDERS_PAID" && emailWhenToSend !== "paid") {
    return successMessage("Not for paid");
  }

  // ORDERS_PAID
  if (topic === "ORDERS_FULFILLED" && emailWhenToSend !== "fulfilled") {
    return successMessage("Not for fulfilled");
  }

  // ORDERS_UPDATED
  if (topic === "ORDERS_UPDATED" && ["delivered", "tagadded"].includes(emailWhenToSend || "") === false) {
    return successMessage("Not for updated");
  }

  if (settings.onlyOnlineStoreOrders && payload.source_name !== "web") {
    return successMessage("Not for online store orders");
  }

  const { email } = payload;
  if (!email) {
    return successMessage("No customer email");
  }

  const subscription = await Subscription.findOne({ shop });

  const isFreeSubscriptionPlan = subscription?.plan !== "free";
  if (settings?.blacklistedEmails?.includes(email) && isFreeSubscriptionPlan) {
    return successMessage("Blacklisted email");
  }

  // HANDLE CREATE MAIL
  const order = payload;

  // Orders update with tags trigger
  if (topic === "ORDERS_UPDATED" && settings?.whenToSend?.after === "tagadded") {
    if (!order?.tags?.includes(settings?.whenToSend?.tag || "")) {
      return successMessage("Tag not added");
    }
  }

  const emailDB = await Email.findOne({ orderId: extractId(order.id) });
  if (emailDB && emailDB.sendDate) {
    return successMessage("Already handled. Sending date: " + emailDB.sendDate);
  }

  const reviewLinks = settings?.reviewLinks;
  if (!reviewLinks || reviewLinks.length === 0) {
    return successMessage("No review links");
  }

  let chosenReivewLink = {};

  if (reviewLinks.length === 1) {
    chosenReivewLink = reviewLinks[0];
  } else {
    let number = 100;
    const randomNumber = Math.floor(Math.random() * 100) + 1;
    const randomReviewLink = reviewLinks.map((link) => {
      number -= parseInt(link.percentage || "0", 10);
      if (randomNumber > number) {
        return {
          ...link,
          selected: true,
        };
      } else {
        return {
          ...link,
          selected: false,
        };
      }
    });
    const foundLink = randomReviewLink.find((link) => link.selected);
    if (!foundLink) {
      return errorMessage("No review link selected");
    }
    chosenReivewLink = foundLink;
  }

  const sendDate = addDays(new Date(), parseInt(settings?.whenToSend?.days || "1", 10));

  if (settings?.specificTimeActive) {
    sendDate.setHours(parseInt(settings.specificTime?.split(":")[0] || "0", 10));
    sendDate.setMinutes(parseInt(settings.specificTime?.split(":")[1] || "0", 10));
  }

  const newEmail = new Email({
    shop,
    email,
    orderId: extractId(order.id),
    orderNumber: order.order_number,
    firstName: order.customer?.first_name || "",
    sendDate,
    sent: false,
    opened: false,
    clicked: false,
    reviewURL: (chosenReivewLink as any)?.url,
    reviewPlatform: (chosenReivewLink as any)?.platform,
  });

  return await newEmail
    .save()
    .then(() => {
      return successMessage("Success");
    })
    .catch((err) => {
      console.log("===err when saving order", err);
      return errorMessage(err?.message);
    });
};

const manageFail = async (webhookDB: IWebhookDocument, errorMessage: string): Promise<void> => {
  const datenow = new Date();
  // Initialize retry_count if it doesn't exist
  webhookDB.retry_count =
    webhookDB.retry_count === null || webhookDB.retry_count === undefined ? 0 : webhookDB.retry_count;
  const coef = webhookDB.retry_count + 1;

  webhookDB.status = "ERROR";
  webhookDB.last_processing_date = datenow;
  webhookDB.next_retry_date = new Date(Date.now() + coef * coef * 10 * 60 * 1000);
  webhookDB.retry_count = webhookDB.retry_count + 1;

  // If there is an error context, save it
  if (errorMessage) {
    webhookDB.error_message = errorMessage;
  }
  // If there is an error context, save it
  if (errorMessage) {
    webhookDB.error_message = errorMessage;
  }

  console.log("===webhookDB", webhookDB.shop, "retry", webhookDB.retry_count, "next retry", webhookDB.next_retry_date);

  // Save the webhook
  await webhookDB.save();
};

const successMessage = (message: string): WebhookResult => {
  return { status: true, message };
};

const errorMessage = (message: string): WebhookResult => {
  return { status: false, message };
};
