import { Session } from "@shopify/shopify-api";
import { PastOrderOption, ShopDocument, ShopifyOrderEdge, ShopifyOrdersResponse } from "@trustsync/types";
import { differenceInDays } from "date-fns";
import shopify from "../../shopify.js";

import { addTimeToDate, extractId } from "../utils/helper.js";

import Email from "../models/Email.js";
import EmailSettings from "../models/EmailSettings.js";
import { ordersGraphQuery } from "../queries/orders.query.js";

export const fetchAllOrders = async (
  session: Session,
  fromDate: string,
  toDate: string,
  after: string | null = null
): Promise<ShopifyOrderEdge[]> => {
  const client = new shopify.api.clients.Graphql({ session });

  const { data } = await client.request(ordersGraphQuery(fromDate, toDate), {
    variables: { after },
  });

  console.log({ data });

  const { edges, pageInfo }: ShopifyOrdersResponse = data.orders;

  // Recursively fetch more pages if there are more results
  if (pageInfo.hasNextPage) {
    const nextPage = await fetchAllOrders(session, fromDate, toDate, pageInfo.endCursor);
    return edges.concat(nextPage); // Combine current page with the next
  }

  return edges;
};

export function splitOrdersByDays(orders: ShopifyOrderEdge[]): PastOrderOption[] {
  if (!orders?.length || !Array.isArray(orders)) {
    return [];
  }

  const pastOrderSplit: Record<number, ShopifyOrderEdge[]> = {};
  const pastOrderOptions: PastOrderOption[] = [];

  for (let i = 10; i <= 150; i += 10) {
    pastOrderSplit[i] = orders.filter((order) => {
      return differenceInDays(new Date(), new Date(order.node.createdAt)) <= i;
    });
  }

  Object.keys(pastOrderSplit).forEach((key) => {
    const count = pastOrderSplit[parseInt(key)].length;
    pastOrderOptions.push({
      label: `Send to orders past ${key} days (${count} ${count > 1 ? "orders" : "order"})`,
      value: key,
    });
  });

  return pastOrderOptions;
}

export const sendReviewEmails = async (orders: ShopifyOrderEdge[], shop: any): Promise<string> => {
  try {
    // Check if orders exist
    if (!orders || orders?.length === 0) {
      return "No orders found";
    }

    // Find email settings for the current shop
    const emailSettings = await EmailSettings.findOne({ shop: shop.shop });

    if (!emailSettings) {
      return "Email settings not found";
    }

    // Check if review links are available
    const reviewLinks = emailSettings?.reviewLinks || [];
    if (reviewLinks?.length === 0) {
      return "No review links available";
    }

    const pendingEmails: any[] = [];

    // Loop through orders
    for (const orderItem of orders) {
      const order = orderItem.node;

      // Check if email has already been sent for this order
      const emailExists = await Email.exists({ orderId: extractId(order.id) });
      if (emailExists || !order.email) {
        continue; // Skip if email already sent or email is missing
      }

      // Choose a review link randomly based on percentages
      let chosenReviewLink: any;
      if (reviewLinks.length === 1) {
        chosenReviewLink = reviewLinks[0];
      } else {
        let totalPercentage = 0;
        for (const link of reviewLinks) {
          totalPercentage += parseInt((link as any).percentage || "0");
        }
        const randomNumber = Math.floor(Math.random() * totalPercentage) + 1;
        let accumulatedPercentage = 0;
        for (const link of reviewLinks) {
          accumulatedPercentage += parseInt((link as any).percentage || "0");
          if (randomNumber <= accumulatedPercentage) {
            chosenReviewLink = link;
            break;
          }
        }
      }

      // Create pending email object
      pendingEmails.push({
        shop: shop.shop,
        email: order.email,
        orderId: extractId(order.id),
        orderNumber: order.name,
        firstName: order?.customer?.firstName || "",
        sendDate: addTimeToDate(
          {
            specificTimeActive: emailSettings?.specificTimeActive || false,
            specificTime: emailSettings?.specificTime || "",
          },
          shop?.info?.timezoneAbbreviation
        ),
        sent: false,
        opened: false,
        clicked: false,
        reviewURL: chosenReviewLink?.url,
        reviewPlatform: chosenReviewLink?.platform,
      });
    }

    // Insert pending emails into the database
    await Email.insertMany(pendingEmails);

    // Update shop status
    shop.sentPastOrders = true;
    await shop.save();

    return "Email scheduled.";
  } catch (error: any) {
    console.log("Occurs error when send email" + error);

    return "Error " + error?.message;
  }
};
