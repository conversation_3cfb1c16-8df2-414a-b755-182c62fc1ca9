import { ObjectId } from "mongoose";
import mailgunServer from "../clients/mailgun.js";
import { getAttachmentStreamFromS3 } from "../clients/s3.js";
import { IEmailDocument } from "../models/Email.js";
import { IEmailSettingsDocument } from "../models/EmailSettings.js";
import { INegativeReviewDocument } from "../models/NegativeReviews.js";
import { IShopDocument } from "../models/Shop.js";
import { stripShopDomain } from "../utils/helper.js";
import {
  getStars,
  negativeReviewEmailTemplate,
  negativeReviewReplyEmailTemplate,
  trustSyncEmailTemplate,
  weeklyReportEmailTemplate,
} from "./email-template.service.js";

// Interface for mailgun response
export interface MailgunResponse {
  message: string;
  status: number;
  id: string;
}

// Interface for mailgun domain response
interface MailgunDomainResponse {
  state?: string;
}

// Interface for email data used in handleSendEmail
interface EmailData {
  email?: string | null;
  firstName?: string | null;
  orderNumber?: string | null;
  orderId?: string | null;
  reviewURL?: string | null;
}

/**
 * Handles sending an email to customer.
 */
export const handleSendEmail = async (
  email: EmailData | IEmailDocument,
  emailSettings: IEmailSettingsDocument,
  shop: IShopDocument
): Promise<MailgunResponse | null> => {
  console.log("===handleSendEmail");

  if (!emailSettings.emailHTML) {
    console.log("No email HTML template found");
    return null;
  }

  let html = emailSettings.emailHTML
    .replaceAll("[first.name]", email.firstName || "")
    .replaceAll("[order.number]", `#${email.orderNumber || ""}`)
    .replaceAll("[review.stars]", `${getStars(emailSettings, email, shop._id as ObjectId)}`);

  html = html.replaceAll("<p>Powered by Trustsync</p>", "");
  if (!shop.verified) {
    html += `<br /><div style="display: flex;">
        Powered by <img style="width:20px;height:20px;margin-top:6px; padding: 0px 2px" src="${process.env.APP_URL}/powered-by-logo.png" /> <a href=https://apps.shopify.com/customer-review-app target=_blank style="color: #5166ff;">TrustSync</a>
    </div>`;
  }

  let replyToEmail = shop?.info?.email || "<EMAIL>";
  const replyToEmails = emailSettings?.replyToEmails || [];
  if (replyToEmails?.length) {
    replyToEmail = replyToEmails.join(",");
  }

  let sendFromDomain: string;
  let sendToEmail: string;

  if (process.env.APP_ENVIRONMENT === "development") {
    sendFromDomain = process.env.MAILGUN_SANDBOX_DOMAIN as string;
    sendToEmail = process.env.MAILGUN_DEV_TO_EMAIL as string;
  } else {
    sendFromDomain = emailSettings?.domain || "trustsync.io";
    sendToEmail = email.email as string;
  }

  console.log({ sendFromDomain });

  // If the email domain is not "trustsync.io", check if it's verified on Mailgun
  if (sendFromDomain !== "trustsync.io") {
    try {
      // Fetch the domain details from Mailgun
      const domain: MailgunDomainResponse = await mailgunServer.domains.get(sendFromDomain);

      console.log({ domain });

      // If the domain is not active or does not exist, set sendFromDomain to "trustsync.io"
      if (!domain || domain.state !== "active") {
        sendFromDomain = "trustsync.io";
      }
    } catch (error) {
      // Log any errors that occur during the Mailgun domain check
      console.error("Error while checking Mailgun domain:", error);
      sendFromDomain = "trustsync.io";
    }
  }

  console.log("===emailSettings.domain", sendFromDomain);

  let sendFromEmail = "";
  if (sendFromDomain === "trustsync.io") {
    sendFromEmail = emailSettings.from
      ? `${emailSettings.from} <<EMAIL>>`
      : `TrustSync <<EMAIL>>`;
  } else {
    sendFromEmail =
      `${emailSettings.from} <${emailSettings.sender || "hello"}@${sendFromDomain}>` ||
      `TrustSync - ${emailSettings.shop}`;
  }

  try {
    const response = await mailgunServer.messages.create(sendFromDomain, {
      from: sendFromEmail,
      to: sendToEmail,
      subject: emailSettings.subject || "We need 10 seconds of your time",
      html: trustSyncEmailTemplate(html, emailSettings),
      "o:tracking-opens": "yes",
      "o:tracking-clicks": "yes",
      "o:tag": email.orderId || "",
      "h:Reply-To": replyToEmail,
    });

    return {
      message: response.message || "Queued. Thank you.",
      status: 200,
      id: response.id || "",
    };
  } catch (err: Error | unknown) {
    console.log(err);
    return null;
  }
};

/**
 * Handles sending a negative review email to the shop owner.
 */
export const handleNegativeReviewEmail = async (
  shop: IShopDocument,
  negativeReview: INegativeReviewDocument,
  firstName: string,
  orderId: string
): Promise<MailgunResponse | null> => {
  let sendFromDomain = "trustsync.io";
  let sendFromEmail = "TrustSync <<EMAIL>>";
  let sendToEmail = shop?.info?.email || "";
  let replyToEmail = "<EMAIL>";

  const id = negativeReview._id?.toString() || "";

  if (process.env.APP_ENVIRONMENT === "development") {
    sendFromDomain = process.env.MAILGUN_SANDBOX_DOMAIN as string;
    sendToEmail = process.env.MAILGUN_DEV_TO_EMAIL as string;
  }

  const myShopifyUsername = stripShopDomain(shop.shop);
  const negativeFeedbackUrl = `https://admin.shopify.com/store/${myShopifyUsername}/apps/${process.env.APP_NAME}/negative-feedbacks?id=${id}`;

  const mailOptions: any = {
    from: sendFromEmail,
    to: sendToEmail,
    subject: `New feedback Received from - ${firstName ? firstName : "Test mail"} [#${orderId || ""}]`,
    html: negativeReviewEmailTemplate(negativeReview, negativeFeedbackUrl),
    "h:Reply-To": replyToEmail,
  };

  try {
    if (negativeReview?.attachment) {
      const attachment = await getAttachmentStreamFromS3(negativeReview.attachment);
      if (attachment) {
        mailOptions.attachment = {
          data: attachment,
          filename: negativeReview.attachment,
        };
      }
    }
  } catch (error) {
    console.log(error);
  }

  try {
    const response = await mailgunServer.messages.create(sendFromDomain, mailOptions);
    return {
      message: response.message || "Queued. Thank you.",
      status: 200,
      id: response.id || "",
    };
  } catch (err) {
    console.log(err);
    return null;
  }
};

/**
 * Handles sending a negative review reply email to the shop owner.
 */
export const handleNegativeReviewReplyEmail = async (
  shop: IShopDocument,
  negativeReview: INegativeReviewDocument,
  reply: string
): Promise<MailgunResponse | null> => {
  let sendFromDomain = "trustsync.io";
  let sendFromEmail = `${shop?.info?.name} <${shop?.info?.email}>`;
  let sendToEmail = negativeReview.email;
  let replyToEmail =
    process.env.APP_NAME === "trustsync-dev" ? "<EMAIL>" : "<EMAIL>";

  const orderId = negativeReview?.orderId;

  if (process.env.APP_ENVIRONMENT === "development") {
    sendFromDomain = process.env.MAILGUN_SANDBOX_DOMAIN as string;
    sendToEmail = process.env.MAILGUN_DEV_TO_EMAIL as string;
    replyToEmail = process.env.MAILGUN_WEBHOOK_REPLY_EMAIL as string;
  }

  const mailOptions = {
    from: sendFromEmail,
    to: sendToEmail,
    subject: `${shop?.info?.name} - Just replied your feedback [#${orderId || ""}]`,
    html: negativeReviewReplyEmailTemplate(shop, reply),
    "h:Reply-To": replyToEmail,
  };

  try {
    const response = await mailgunServer.messages.create(sendFromDomain, mailOptions);
    return {
      message: response.message || "Queued. Thank you.",
      status: 200,
      id: response.id || "",
    };
  } catch (error) {
    console.log(error);
    return null;
  }
};

/**
 * Handles sending weekly report email to shop owner
 */
export const handleWeeklyReportEmail = async (
  shop: IShopDocument,
  sentEmails: number,
  pendingEmails: number
): Promise<MailgunResponse | null> => {
  let sendFromDomain = "trustsync.io";
  let sendFromEmail = "TrustSync <<EMAIL>>";
  let sendToEmail = shop?.info?.email || "";
  let replyToEmail = "<EMAIL>";

  if (process.env.APP_ENVIRONMENT === "development") {
    sendFromDomain = process.env.MAILGUN_SANDBOX_DOMAIN as string;
    sendToEmail = process.env.MAILGUN_DEV_TO_EMAIL as string;
  }

  const shopifyUserName = stripShopDomain(shop.shop);
  const analyticsPageUrl = `https://admin.shopify.com/store/${shopifyUserName}/apps/${process.env.APP_NAME}/analytics`;

  // Generate the HTML for the weekly report email
  const emailHtml = weeklyReportEmailTemplate(shop, sentEmails, pendingEmails, analyticsPageUrl);

  const mailOptions = {
    from: sendFromEmail,
    to: sendToEmail,
    subject: `Your weekly review request summary is ready 🚀`,
    html: emailHtml,
    "h:Reply-To": replyToEmail,
    // "o:tracking-opens": "yes",
    // "o:tracking-clicks": "yes",
  };

  try {
    const response = await mailgunServer.messages.create(sendFromDomain, mailOptions);
    return {
      message: response.message || "Queued. Thank you.",
      status: 200,
      id: response.id || "",
    };
  } catch (err) {
    console.log("Error sending weekly report email:", err);
    return null;
  }
};
