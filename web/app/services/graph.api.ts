import { Session } from "@shopify/shopify-api";
import { ShopInfo } from "@trustsync/types";
import shopify from "../../shopify.js";

import appSubscriptionCancelMutation from "../queries/appSubscriptionCancel.mutation.js";
import appSubscriptionCreateMutation from "../queries/appSubscriptionCreate.mutation.js";
import shopShowQuery from "../queries/shopShow.query.js";

import { flattenShopData } from "../utils/helper.js";

// Interface for subscription variables
interface SubscriptionVariables {
  [key: string]: any;
}

// Interface for GraphQL error
interface GraphQLError {
  body?: {
    errors?: any[];
  };
}

export async function graphCreateSubscription(session: Session, variables: SubscriptionVariables): Promise<any> {
  try {
    const client = new shopify.api.clients.Graphql({ session });

    const { data: { appSubscriptionCreate } } = await client.request(appSubscriptionCreateMutation,
      { variables }
    );

    return appSubscriptionCreate;
  } catch (error: any) {
    console.log((error as GraphQLError).body?.errors);
    return "";
  }
}

export async function graphCancelSubscription(session: Session, id: string): Promise<any> {
  try {
    const client = new shopify.api.clients.Graphql({ session });

    const { data: { appSubscriptionCancel } } = await client.request(appSubscriptionCancelMutation,
      { variables: { id } }
    );

    return appSubscriptionCancel;
  } catch (error: any) {
    console.log((error as GraphQLError).body?.errors);
    return "";
  }
}

export async function graphShopInfo(session: Session): Promise<ShopInfo> {
  try {
    const client = new shopify.api.clients.Graphql({ session });

    const { data: { shop } } = await client.request(shopShowQuery);

    return flattenShopData(shop);
  } catch (error) {
    console.log(error);
    return {};
  }
}