import { MessageDocument, NegativeReviewStaus, ReviewLink } from "@trustsync/types";
import { IEmailSettingsDocument } from "../models/EmailSettings.js";
import NegativeReview, { INegativeReviewDocument } from "../models/NegativeReviews.js";
import { IShopDocument } from "../models/Shop.js";
import { capitalizeFirstLetter } from "../utils/helper.js";

// Interface for review link
// interface ReviewLink {
//   id?: string;
//   platform?: string;
//   url?: string;
// }

// Interface for transformed review link
interface TransformedReviewLink {
  id: string;
  name: string;
  logo: string;
  url: string;
}

// Interface for transformed shop
interface TransformedShop {
  _id: any;
  id: string | null;
  name: string | null;
  logo: string;
  email: string | null;
  domain: string | null;
  reviewLinks: TransformedReviewLink[];
}

// Interface for conversation message
interface ConversationMessage {
  from: "shop" | "customer";
  messageId: string;
  message?: string;
  attachments?: string[];
  status: string;
  readAt?: Date | null;
}

export const transformShop = (shop: IShopDocument, settings: IEmailSettingsDocument): TransformedShop => {
  const { _id, info = {} } = shop;
  const { logo, reviewLinks } = settings;

  return {
    _id,
    id: info.id?.toString() ?? null,
    name: info.name ?? null,
    logo: logo ?? `${process.env.APP_URL}/trustsync.png`,
    email: info.email ?? null,
    domain: info.myshopifyDomain ?? null,
    reviewLinks: transformReviewLinks(reviewLinks || []),
  };
};

export const transformReviewLinks = (reviewLinks: ReviewLink[]): TransformedReviewLink[] => {
  if (!Array.isArray(reviewLinks) || reviewLinks.length === 0) {
    return [];
  }

  return reviewLinks.map(({ id, platform, url }) => ({
    id: id || "",
    name: capitalizeFirstLetter(platform || ""),
    logo: `${process.env.APP_URL}/logos/${platform}.png`,
    url: url || "",
  }));
};

export const saveNegativeReviewReply = async (
  id: string,
  message: ConversationMessage,
  status: NegativeReviewStaus
): Promise<INegativeReviewDocument> => {
  try {
    const review = await NegativeReview.findById(id);

    if (!review) {
      throw new Error("Review not found");
    }

    const currentDate = new Date();

    // If conversation doesn't exist, initialize it with the message
    if (!review.conversation) {
      review.conversation = [message as MessageDocument];
      review.status = status;

      const updatedReview = await review.save();
      return updatedReview;
    }
    // Otherwise use two separate operations to update the conversation
    else {
      // First, update all messages with null readAt to current date
      await NegativeReview.updateOne(
        { _id: id },
        {
          $set: {
            status,
            "conversation.$[elem].readAt": currentDate,
          },
        },
        {
          arrayFilters: [{ "elem.readAt": null }],
        }
      );

      // Then, push the new message to the conversation array
      const updatedReview = await NegativeReview.findByIdAndUpdate(
        id,
        { $push: { conversation: message } },
        { new: true }
      );

      if (!updatedReview) {
        throw new Error("Failed to update review");
      }

      return updatedReview;
    }
  } catch (error) {
    console.error("Error saving negative review reply:", error);
    throw error;
  }
};
