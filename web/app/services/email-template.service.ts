import { ObjectId } from "mongoose";
import { IEmailDocument } from "../models/Email.js";
import { IEmailSettingsDocument } from "../models/EmailSettings.js";
import { INegativeReviewDocument } from "../models/NegativeReviews.js";
import { IShopDocument } from "../models/Shop.js";

// Interface for email data used in templates
interface EmailData {
  reviewURL?: string | null;
  orderId?: string | null;
}

export const getStars = (
  emailSettings: IEmailSettingsDocument,
  email: EmailData | IEmailDocument,
  shopId: ObjectId
): string | undefined => {
  const { designTemplate, autoPublish: autoPublishStr, highestText, lowestText } = emailSettings;
  const autoPublish = parseInt(autoPublishStr || "4", 10);

  let reviewURL: string;
  if (emailSettings.customersPick) {
    reviewURL = `${process.env.APP_REVIEW_URL}/platform/${shopId}`;
  } else {
    reviewURL = email.reviewURL || "";
  }

  const ratingLink = `${process.env.APP_REVIEW_URL}/review/${shopId}?order=${email.orderId}`;

  // Transparency
  // grey-stars-squares.png           => GOOD
  // number-1.png ++                  => GOOD
  // regular.png                      => GOOD
  // regular-gray.png                 => GOOD
  // colored-1.png ++                 => GOOD
  // regular-vertical-boxed-1.png ++  => GOOD
  // regular-vertical-1.png ++        => GOOD
  // heart-1.png ++                   => GOOD
  // thunder-1.png                    => GOOD
  // taco-1.png                       => GOOD
  // fire-1.png                       => GOOD

  const emailFooter =
    lowestText || highestText
      ? `
    <div>
      ${lowestText ? `<span style= "display: inline-block; min-width:11.9rem;">${lowestText}</span>` : ""}
      ${highestText ? `<span style="margin-left: 1rem;">${highestText}</span>` : ""}
    </div>
  `
      : "";

  if (emailSettings.designTemplate == "boxedGray") {
    return `<br />
      <div style="display: flex;">
        <div style="margin-right: 3px">
          <a href="${ratingLink}&rating=1" target=_blank>
            <img src="${process.env.APP_URL}/reviews/grey-stars-squares.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
        <div style="margin-right: 3px">
          <a href="${ratingLink}&rating=2" target=_blank>
            <img src="${process.env.APP_URL}/reviews/grey-stars-squares.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
        <div style="margin-right: 3px">
          <a href=${autoPublish <= 3 ? reviewURL : `${ratingLink}&rating=3`} target=_blank>
            <img src="${process.env.APP_URL}/reviews/grey-stars-squares.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
        <div style="margin-right: 3px">
          <a href=${autoPublish <= 4 ? reviewURL : `${ratingLink}&rating=4`} target=_blank>
            <img src="${process.env.APP_URL}/reviews/grey-stars-squares.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
        <div>
          <a href=${autoPublish <= 5 ? reviewURL : `${ratingLink}&rating=5`} target=_blank>
            <img src="${process.env.APP_URL}/reviews/grey-stars-squares.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
      </div>
      ${emailFooter}
    `;
  } else if (emailSettings.designTemplate == "numbers") {
    return `<br />
      <div style="display: flex;">
        <div style="margin-right: 3px">
          <a href="${ratingLink}&rating=1" target=_blank>
            <img src="${process.env.APP_URL}/reviews/number-1.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
        <div style="margin-right: 3px">
          <a href="${ratingLink}&rating=2" target=_blank>
            <img src="${process.env.APP_URL}/reviews/number-2.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
        <div style="margin-right: 3px">
          <a href=${3 >= autoPublish ? reviewURL : `${ratingLink}&rating=3`} target=_blank>
            <img src="${process.env.APP_URL}/reviews/number-3.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
        <div style="margin-right: 3px">
          <a href=${4 >= autoPublish ? reviewURL : `${ratingLink}&rating=4`} target=_blank>
            <img src="${process.env.APP_URL}/reviews/number-4.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
        <div>
          <a href=${5 >= autoPublish ? reviewURL : `${ratingLink}&rating=5`} target=_blank>
            <img src="${process.env.APP_URL}/reviews/number-5.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
      </div>
      ${emailFooter}
    `;
  } else if (emailSettings.designTemplate == "regular") {
    return `<br />
      <div style="display: flex;">
        <div style="margin-right: 2px">
          <a href="${ratingLink}&rating=1" target=_blank>
            <img src="${process.env.APP_URL}/reviews/regular.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
        <div style="margin-right: 2px">
          <a href="${ratingLink}&rating=2" target=_blank>
            <img src="${process.env.APP_URL}/reviews/regular.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
        <div style="margin-right: 2px">
          <a href="${autoPublish <= 3 ? reviewURL : `${ratingLink}&rating=3`}" target=_blank>
            <img src="${process.env.APP_URL}/reviews/regular.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
        <div style="margin-right: 2px">
          <a href="${autoPublish <= 4 ? reviewURL : `${ratingLink}&rating=4`}" target=_blank>
            <img src="${process.env.APP_URL}/reviews/regular.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
        <div>
          <a href="${autoPublish <= 5 ? reviewURL : `${ratingLink}&rating=5`}" target=_blank>
            <img src="${process.env.APP_URL}/reviews/regular.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
      </div>
      ${emailFooter}
    `;
  } else if (emailSettings.designTemplate == "regularGray") {
    return `<br />
      <div style="display: flex;">
        <div style="margin-right: 2px">
          <a href="${ratingLink}&rating=1" target=_blank>
            <img src="${process.env.APP_URL}/reviews/regular-gray.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
        <div style="margin-right: 2px">
          <a href="${ratingLink}&rating=2" target=_blank>
            <img src="${process.env.APP_URL}/reviews/regular-gray.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
        <div style="margin-right: 2px">
          <a href=${autoPublish <= 3 ? reviewURL : `${ratingLink}&rating=3`} target=_blank>
            <img src="${process.env.APP_URL}/reviews/regular-gray.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
        <div style="margin-right: 2px">
          <a href=${autoPublish <= 4 ? reviewURL : `${ratingLink}&rating=4`} target=_blank>
            <img src="${process.env.APP_URL}/reviews/regular-gray.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
        <div>
          <a href=${autoPublish <= 5 ? reviewURL : `${ratingLink}&rating=5`} target=_blank>
            <img src="${process.env.APP_URL}/reviews/regular-gray.png" style="width: 3rem; cursor: pointer;" />
          </a>
        </div>
      </div>
      ${emailFooter}
    `;
  } else if (designTemplate == "colored") {
    return `<br /><div>
  <div><a href="${ratingLink}&rating=1" target=_blank style="text-decoration: none; color: black;"><table style="width: 100%; background: #f7f7f6;padding: 0px 7px 0px 6px;border-radius: 3px;"><tbody style="width: 100%"><tr><td style="padding-top: 8px"><img src="${
      process.env.APP_URL
    }/reviews/colored-1.png" style="width: 16rem; cursor: pointer;" /></td><td style="text-align: end"><span style="font-weight: bold; font-size: 1.4rem;">${
      lowestText || ""
    }</span></td></tr></tbody></table></a></div>
  <div><a href="${ratingLink}&rating=2" target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px;margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/colored-2.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
  <div><a href=${
    autoPublish <= 3 ? reviewURL : `${ratingLink}&rating=3`
  } target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px;margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/colored-3.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
  <div><a href=${
    autoPublish <= 4 ? reviewURL : `${ratingLink}&rating=4`
  } target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px;margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/colored-4.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
  <div><a href=${
    autoPublish <= 5 ? reviewURL : `${ratingLink}&rating=5`
  } target=_blank style="text-decoration: none; color: black;"><table style="width: 100%; background: #f7f7f6;padding: 0px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><tbody style="width: 100%"><tr><td style="padding-top: 8px"><img src="${
      process.env.APP_URL
    }/reviews/colored-5.png" style="width: 16rem; cursor: pointer;" /></td><td style="text-align: end"><span style="margin-top: 1%; margin-left: auto; font-weight: bold; font-size: 1.4rem;">${
      highestText || ""
    }</span></td></tr></tbody></table></a></div>
  </div>`;
  } else if (designTemplate == "regularVerticalBoxed") {
    return `<br /><div>
    <div><a href="${ratingLink}&rating=1" target=_blank style="text-decoration: none; color: black;"><table style="width: 100%; background: #f7f7f6;padding: 0px 7px 0px 6px;border-radius: 3px;"><tbody style="width: 100%"><tr><td style="padding-top: 8px"><img src="${
      process.env.APP_URL
    }/reviews/regular-vertical-boxed-1.png" style="width: 16rem; cursor: pointer;" /></td><td style="text-align: end"><span style="font-weight: bold; font-size: 1.4rem;">${
      lowestText || ""
    }</span></td></tr></tbody></table></a></div>
  <div><a href="${ratingLink}&rating=2" target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px;margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/regular-vertical-boxed-2.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
  <div><a href=${
    autoPublish <= 3 ? reviewURL : `${ratingLink}&rating=3`
  } target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px;margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/regular-vertical-boxed-3.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
  <div><a href=${
    autoPublish <= 4 ? reviewURL : `${ratingLink}&rating=4`
  } target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px;margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/regular-vertical-boxed-4.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
    <div><a href=${
      autoPublish <= 5 ? reviewURL : `${ratingLink}&rating=5`
    } target=_blank style="text-decoration: none; color: black;"><table style="width: 100%; background: #f7f7f6;padding: 0px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><tbody style="width: 100%"><tr><td style="padding-top: 8px"><img src="${
      process.env.APP_URL
    }/reviews/regular-vertical-boxed-5.png" style="width: 16rem; cursor: pointer;" /></td><td style="text-align: end"><span style="margin-top: 1%; margin-left: auto; font-weight: bold; font-size: 1.4rem;">${
      highestText || ""
    }</span></td></tr></tbody></table></a></div>
  </div>`;
  } else if (designTemplate == "regularVertical") {
    return `<br /><div>
    <div><a href="${ratingLink}&rating=1" target=_blank style="text-decoration: none; color: black;"><table style="width: 100%; background: #f7f7f6;padding: 0px 7px 0px 6px;border-radius: 3px;"><tbody style="width: 100%"><tr><td style="padding-top: 8px"><img src="${
      process.env.APP_URL
    }/reviews/regular-vertical-1.png" style="width: 16rem; cursor: pointer;" /></td><td style="text-align: end"><span style="font-weight: bold; font-size: 1.4rem;">${
      lowestText || ""
    }</span></td></tr></tbody></table></a></div>
  <div><a href="${ratingLink}&rating=2" target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/regular-vertical-2.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
  <div><a href=${
    autoPublish <= 3 ? reviewURL : `${ratingLink}&rating=3`
  } target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/regular-vertical-3.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
  <div><a href=${
    autoPublish <= 4 ? reviewURL : `${ratingLink}&rating=4`
  } target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/regular-vertical-4.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
    <div><a href=${
      autoPublish <= 5 ? reviewURL : `${ratingLink}&rating=5`
    } target=_blank style="text-decoration: none; color: black;"><table style="width: 100%; background: #f7f7f6;padding: 0px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><tbody style="width: 100%"><tr><td style="padding-top: 8px"><img src="${
      process.env.APP_URL
    }/reviews/regular-vertical-5.png" style="width: 16rem; cursor: pointer;" /></td><td style="text-align: end"><span style="margin-top: 1%; margin-left: auto; font-weight: bold; font-size: 1.4rem;">${
      highestText || ""
    }</span></td></tr></tbody></table></a></div>
  </div>`;
  } else if (designTemplate == "heart") {
    return `<br /><div>
    <div><a href="${ratingLink}&rating=1" target=_blank style="text-decoration: none; color: black;"><table style="width: 100%; background: #f7f7f6;padding: 0px 7px 0px 6px;border-radius: 3px;"><tbody style="width: 100%"><tr><td style="padding-top: 8px"><img src="${
      process.env.APP_URL
    }/reviews/heart-1.png" style="width: 16rem; cursor: pointer;" /></td><td style="text-align: end"><span style="font-weight: bold; font-size: 1.4rem;">${
      lowestText || ""
    }</span></td></tr></tbody></table></a></div>
  <div><a href="${ratingLink}&rating=2" target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/heart-2.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
  <div><a href=${
    autoPublish <= 3 ? reviewURL : `${ratingLink}&rating=3`
  } target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/heart-3.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
  <div><a href=${
    autoPublish <= 4 ? reviewURL : `${ratingLink}&rating=4`
  } target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/heart-4.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
    <div><a href=${
      autoPublish <= 5 ? reviewURL : `${ratingLink}&rating=5`
    } target=_blank style="text-decoration: none; color: black;"><table style="width: 100%; background: #f7f7f6;padding: 0px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><tbody style="width: 100%"><tr><td style="padding-top: 8px"><img src="${
      process.env.APP_URL
    }/reviews/heart-5.png" style="width: 16rem; cursor: pointer;" /></td><td style="text-align: end"><span style="margin-top: 1%; margin-left: auto; font-weight: bold; font-size: 1.4rem;">${
      highestText || ""
    }</span></td></tr></tbody></table></a></div>
  </div>`;
  } else if (designTemplate == "thunder") {
    return `<br /><div>
    <div><a href="${ratingLink}&rating=1" target=_blank style="text-decoration: none; color: black;"><table style="width: 100%; background: #f7f7f6;padding: 0px 7px 0px 6px;border-radius: 3px;"><tbody style="width: 100%"><tr><td style="padding-top: 8px"><img src="${
      process.env.APP_URL
    }/reviews/thunder-1.png" style="width: 16rem; cursor: pointer;" /></td><td style="text-align: end"><span style="font-weight: bold; font-size: 1.4rem;">${
      lowestText || ""
    }</span></td></tr></tbody></table></a></div>
  <div><a href="${ratingLink}&rating=2" target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/thunder-2.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
  <div><a href=${
    autoPublish <= 3 ? reviewURL : `${ratingLink}&rating=3`
  } target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/thunder-3.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
  <div><a href=${
    autoPublish <= 4 ? reviewURL : `${ratingLink}&rating=4`
  } target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/thunder-4.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
    <div><a href=${
      autoPublish <= 5 ? reviewURL : `${ratingLink}&rating=5`
    } target=_blank style="text-decoration: none; color: black;"><table style="width: 100%; background: #f7f7f6;padding: 0px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><tbody style="width: 100%"><tr><td style="padding-top: 8px"><img src="${
      process.env.APP_URL
    }/reviews/thunder-5.png" style="width: 16rem; cursor: pointer;" /></td><td style="text-align: end"><span style="margin-top: 1%; margin-left: auto; font-weight: bold; font-size: 1.4rem;">${
      highestText || ""
    }</span></td></tr></tbody></table></a></div>
  </div>`;
  } else if (designTemplate == "taco") {
    return `<br /><div>
    <div><a href="${ratingLink}&rating=1" target=_blank style="text-decoration: none; color: black;"><table style="width: 100%; background: #f7f7f6;padding: 0px 7px 0px 6px;border-radius: 3px;"><tbody style="width: 100%"><tr><td style="padding-top: 8px"><img src="${
      process.env.APP_URL
    }/reviews/taco-1.png" style="width: 16rem; cursor: pointer;" /></td><td style="text-align: end"><span style="font-weight: bold; font-size: 1.4rem;">${
      lowestText || ""
    }</span></td></tr></tbody></table></a></div>
  <div><a href="${ratingLink}&rating=2" target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/taco-2.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
  <div><a href=${
    autoPublish <= 3 ? reviewURL : `${ratingLink}&rating=3`
  } target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/taco-3.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
  <div><a href=${
    autoPublish <= 4 ? reviewURL : `${ratingLink}&rating=4`
  } target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/taco-4.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
    <div><a href=${
      autoPublish <= 5 ? reviewURL : `${ratingLink}&rating=5`
    } target=_blank style="text-decoration: none; color: black;"><table style="width: 100%; background: #f7f7f6;padding: 0px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><tbody style="width: 100%"><tr><td style="padding-top: 8px"><img src="${
      process.env.APP_URL
    }/reviews/taco-5.png" style="width: 16rem; cursor: pointer;" /></td><td style="text-align: end"><span style="margin-top: 1%; margin-left: auto; font-weight: bold; font-size: 1.4rem;">${
      highestText || ""
    }</span></td></tr></tbody></table></a></div>
  </div>`;
  } else if (designTemplate == "fire") {
    return `<br /><div>
    <div><a href="${ratingLink}&rating=1" target=_blank style="text-decoration: none; color: black;"><table style="width: 100%; background: #f7f7f6;padding: 0px 7px 0px 6px;border-radius: 3px;"><tbody style="width: 100%"><tr><td style="padding-top: 8px"><img src="${
      process.env.APP_URL
    }/reviews/fire-1.png" style="width: 16rem; cursor: pointer;" /></td><td style="text-align: end"><span style="font-weight: bold; font-size: 1.4rem;">${
      lowestText || ""
    }</span></td></tr></tbody></table></a></div>
  <div><a href="${ratingLink}&rating=2" target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/fire-2.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
  <div><a href=${
    autoPublish <= 3 ? reviewURL : `${ratingLink}&rating=3`
  } target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/fire-3.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
  <div><a href=${
    autoPublish <= 4 ? reviewURL : `${ratingLink}&rating=4`
  } target=_blank style="text-decoration: none; color: black;"><div style="background: #f7f7f6;padding: 8px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><img src="${
      process.env.APP_URL
    }/reviews/fire-4.png" style="width: 16rem; cursor: pointer;" /></div></a></div>
    <div><a href=${
      autoPublish <= 5 ? reviewURL : `${ratingLink}&rating=5`
    } target=_blank style="text-decoration: none; color: black;"><table style="width: 100%; background: #f7f7f6;padding: 0px 7px 0px 6px;border-radius: 3px; margin-top: 8px;"><tbody style="width: 100%"><tr><td style="padding-top: 8px"><img src="${
      process.env.APP_URL
    }/reviews/fire-5.png" style="width: 16rem; cursor: pointer;" /></td><td style="text-align: end"><span style="margin-top: 1%; margin-left: auto; font-weight: bold; font-size: 1.4rem;">${
      highestText || ""
    }</span></td></tr></tbody></table></a></div>
  </div>`;
  }
};

export const trustSyncEmailTemplate = (html: string, emailSettings: IEmailSettingsDocument): string => {
  let logoHtml = "";
  if (emailSettings?.logo) {
    logoHtml = `
      <tr>
        <td align="center" valign="top" width="507">
          <img
            src=${emailSettings.logo}
            style="display:block;border:none;outline:none;max-width:368px;opacity:1;max-width:100%;object-fit:contain;${
              emailSettings.logoHeight ? `height:${emailSettings.logoHeight}px;` : ""
            }"
            class="CToWUd"
            data-bit="iit"
          />
        </td>
      </tr>
    `;
  }

  const bgColor = emailSettings?.bgColor || "#edf4ff";
  const borderColor = emailSettings?.borderColor || "#7f88ff";

  return `
    <div style="font-family:Arial;line-height:1.1;margin:0px;background:${bgColor};width:100%;text-align:center">
      <div style="margin:0px;outline:none;padding:0px;color:#000000;font-family:arial;line-height:1.1;width:100%;text-align:center">
      <div style="clear:both;min-width:100%;padding:20px;color:#000000;font-size:11px;font-family:arial,helvetica,sans-serif;line-height:140%;line-height:1.4;text-align:center;box-sizing:border-box"></div>
      <table border="0" cellpadding="0" cellspacing="0" width="100%" align="left" style="border-collapse:collapse;font-size:14px;min-width:auto;">
          <tbody>
            <tr>
              <td align="center" valign="top" width="100%">
                <table border="0" cellpadding="0" cellspacing="0" width="597" style="border-collapse:collapse;font-size:14px;min-width:auto;max-width:597px">
                  <tbody>
                    <tr>
                      <td valign="top" align="center" width="597" style="background-color:#ffffff;">
                        <table cellpadding="0" cellspacing="0" border="0" width="597" style="border-collapse:collapse;font-size:14px;min-width:100%;">
                          <tbody>
                            <tr style="">
                              <td valign="top" style="padding:0px;">
                                <table width="100%" border="0" cellpadding="0" cellspacing="0" style="border-collapse:initial!important;font-size:14px;min-width:100%">
                                  <tbody>
                                    <tr>
                                      <td valign="top" style="padding:0px">
                                        <table width="100%" border="0" cellpadding="0" cellspacing="0" style="border-collapse:collapse;font-size:14px;min-width:100%">
                                          <tbody>
                                            <tr>
                                              <td valign="top" height="10">
                                                <div style="margin:0;outline:none;padding:0;height:10px">
                                                  <table cellpadding="0" cellspacing="0" border="0" width="100%" style="border-collapse:collapse;font-size:14px;min-width:100%">
                                                    <tbody>
                                                      <tr>
                                                        <td valign="top" height="10" width="597" style="background-color:${borderColor};">&nbsp;</td>
                                                      </tr>
                                                    </tbody>
                                                  </table>
                                                </div>
                                              </td>
                                            </tr>
                                          </tbody>
                                        </table>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </td>
                            </tr>
                            <tr>
                              <td valign="top" style="padding:0;">
                                <table width="100%" border="0" cellpadding="0" cellspacing="0" style="border-collapse:initial!important;font-size:14px;min-width:100%">
                                  <tbody>
                                    <tr style="margin:0;padding:0;">
                                      <td valign="top" style="background-color:white;padding:29px 50px 20px 50px;">
                                        <table width="100%" border="0" cellpadding="0" cellspacing="0" style="border-collapse:collapse;font-size:14px;min-width:100%">
                                          <tbody>
                                            ${logoHtml}
                                            <tr>
                                              <td valign="top" align="left" width="497" style="line-height:200%;margin:0;outline:none;padding:0;font-weight:inherit;line-height:2;text-decoration:inherit;font-family:arial;">
                                                ${logoHtml ? "<br /><br />" : ""}
                                                <div style="color:#171717;font-size:17px;">${html}</div>
                                              </td>
                                            </tr>
                                          </tbody>
                                        </table>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div style="clear:both;background-color:inherit;background:inherit;min-width:100%;padding:20px;color:#000000;font-size:11px;font-family:arial,helvetica,sans-serif;line-height:140%;line-height:1.4;text-align:center;box-sizing:border-box;"></div>
    </div>
  `;
};

/**
 * Generate negative review email template
 */
export const negativeReviewEmailTemplate = (
  negativeReview: INegativeReviewDocument,
  negativeFeedbackUrl: string
): string => {
  const bgColor = "#edf4ff";
  const borderColor = "#7f88ff";

  let html = `
    <div style="line-height: 20px;"><b>${negativeReview.name} (${negativeReview.email})</b> just left feedback for your store.</div>
    <div style="background:#edf4ff;border-left:5px solid ${borderColor};padding:5px 10px;margin:15px 0px;line-height:25px">${negativeReview.message}</div>
    <div style="display:flex;margin-top:20px;">
      <img style="height:60px;padding:0px 2px;margin:7px 3px" src="${process.env.APP_URL}/star/${negativeReview.rating}.png" />
    </div>
    <div style="text-align:center;margin-top:20px">
      <a href="${negativeFeedbackUrl}"
        style="
          background-color: #7f88ff;
          color: #ffffff;
          padding: 3px 15px;
          text-decoration: none;
          border-radius: 5px;
          font-size: 14px;
          display: inline-block;
        "
      >
        Reply to the customer
      </a>
    </div>
    <div style="display:flex;margin-top:20px;">
      Powered by <img style="width:20px;height:20px;padding:0px 2px;margin:7px 3px" src="${process.env.APP_URL}/powered-by-logo.png" /> <a href="https://apps.shopify.com/customer-review-app" target="_blank" style="color: #5166ff;">TrustSync</a>
    </div>
    `;

  return `
    <div style="font-family:Arial;line-height:1.1;margin:0px;background:${bgColor};width:100%;text-align:center">
      <div style="margin:0px;outline:none;padding:0px;color:#000000;font-family:arial;line-height:1.1;width:100%;text-align:center">
      <div style="clear:both;min-width:100%;padding:20px;color:#000000;font-size:11px;font-family:arial,helvetica,sans-serif;line-height:140%;line-height:1.4;text-align:center;box-sizing:border-box"></div>
      <table border="0" cellpadding="0" cellspacing="0" width="100%" align="left" style="border-collapse:collapse;font-size:14px;min-width:auto;">
          <tbody>
            <tr>
              <td align="center" valign="top" width="100%">
                <table border="0" cellpadding="0" cellspacing="0" width="597" style="border-collapse:collapse;font-size:14px;min-width:auto;max-width:597px">
                  <tbody>
                    <tr>
                      <td valign="top" align="center" width="597" style="background-color:#ffffff;">
                        <table cellpadding="0" cellspacing="0" border="0" width="597" style="border-collapse:collapse;font-size:14px;min-width:100%;">
                          <tbody>
                            <tr style="">
                              <td valign="top" style="padding:0px;">
                                <table width="100%" border="0" cellpadding="0" cellspacing="0" style="border-collapse:initial!important;font-size:14px;min-width:100%">
                                  <tbody>
                                    <tr>
                                      <td valign="top" style="padding:0px">
                                        <table width="100%" border="0" cellpadding="0" cellspacing="0" style="border-collapse:collapse;font-size:14px;min-width:100%">
                                          <tbody>
                                            <tr>
                                              <td valign="top" height="10">
                                                <div style="margin:0;outline:none;padding:0;height:10px">
                                                  <table cellpadding="0" cellspacing="0" border="0" width="100%" style="border-collapse:collapse;font-size:14px;min-width:100%">
                                                    <tbody>
                                                      <tr>
                                                        <td valign="top" height="10" width="597" style="background-color:${borderColor};">&nbsp;</td>
                                                      </tr>
                                                    </tbody>
                                                  </table>
                                                </div>
                                              </td>
                                            </tr>
                                          </tbody>
                                        </table>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </td>
                            </tr>
                            <tr>
                              <td valign="top" style="padding:0;">
                                <table width="100%" border="0" cellpadding="0" cellspacing="0" style="border-collapse:initial!important;font-size:14px;min-width:100%">
                                  <tbody>
                                    <tr style="margin:0;padding:0;">
                                      <td valign="top" style="background-color:white;padding:29px 50px 20px 50px;">
                                        <table width="100%" border="0" cellpadding="0" cellspacing="0" style="border-collapse:collapse;font-size:14px;min-width:100%">
                                          <tbody>
                                            <tr>
                                              <td align="center" valign="top" width="507">
                                                <img
                                                  src="${process.env.APP_URL}/trustsync.png"
                                                  style="display:block;border:none;outline:none;width:200px;margin-bottom:10px"
                                                  class="CToWUd"
                                                  data-bit="iit"
                                                />
                                              </td>
                                            </tr>
                                            <tr>
                                              <td valign="top" align="left" width="497" style="line-height:200%;margin:0;outline:none;padding:0;font-weight:inherit;line-height:2;text-decoration:inherit;font-family:arial;">
                                                <div style="color:#171717;font-size:17px;margin-top:15px">${html}</div>
                                              </td>
                                            </tr>
                                          </tbody>
                                        </table>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div style="clear:both;background-color:inherit;background:inherit;min-width:100%;padding:20px;color:#000000;font-size:11px;font-family:arial,helvetica,sans-serif;line-height:140%;line-height:1.4;text-align:center;box-sizing:border-box;"></div>
    </div>
  `;
};

/**
 * Generate negative review reply email template
 */
export const negativeReviewReplyEmailTemplate = (shop: IShopDocument, reply: string): string => {
  const bgColor = "#edf4ff";
  const borderColor = "#7f88ff";

  let html = `
      <div style="line-height: 20px;"><b>${shop?.info?.name} (${shop?.info?.email})</b> just give reply on your feedback.</div>
      <div style="background:#edf4ff;border-left:5px solid ${borderColor};padding:5px 10px;margin:15px 0px;line-height:25px">${reply}</div>
      `;

  if (!shop.verified) {
    html += `<div style="display:flex;margin-top:20px;">
        Powered by <img style="width:20px;height:20px;padding:0px 2px;margin:7px 3px" src="${process.env.APP_URL}/powered-by-logo.png" /> <a href="https://apps.shopify.com/customer-review-app" target="_blank" style="color: #5166ff;">TrustSync</a>
      </div>`;
  }

  return `
      <div style="font-family:Arial;line-height:1.1;margin:0px;background:${bgColor};width:100%;text-align:center">
        <div style="margin:0px;outline:none;padding:0px;color:#000000;font-family:arial;line-height:1.1;width:100%;text-align:center">
        <div style="clear:both;min-width:100%;padding:20px;color:#000000;font-size:11px;font-family:arial,helvetica,sans-serif;line-height:140%;line-height:1.4;text-align:center;box-sizing:border-box"></div>
        <table border="0" cellpadding="0" cellspacing="0" width="100%" align="left" style="border-collapse:collapse;font-size:14px;min-width:auto;">
            <tbody>
              <tr>
                <td align="center" valign="top" width="100%">
                  <table border="0" cellpadding="0" cellspacing="0" width="597" style="border-collapse:collapse;font-size:14px;min-width:auto;max-width:597px">
                    <tbody>
                      <tr>
                        <td valign="top" align="center" width="597" style="background-color:#ffffff;">
                          <table cellpadding="0" cellspacing="0" border="0" width="597" style="border-collapse:collapse;font-size:14px;min-width:100%;">
                            <tbody>
                              <tr style="">
                                <td valign="top" style="padding:0px;">
                                  <table width="100%" border="0" cellpadding="0" cellspacing="0" style="border-collapse:initial!important;font-size:14px;min-width:100%">
                                    <tbody>
                                      <tr>
                                        <td valign="top" style="padding:0px">
                                          <table width="100%" border="0" cellpadding="0" cellspacing="0" style="border-collapse:collapse;font-size:14px;min-width:100%">
                                            <tbody>
                                              <tr>
                                                <td valign="top" height="10">
                                                  <div style="margin:0;outline:none;padding:0;height:10px">
                                                    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="border-collapse:collapse;font-size:14px;min-width:100%">
                                                      <tbody>
                                                        <tr>
                                                          <td valign="top" height="10" width="597" style="background-color:${borderColor};">&nbsp;</td>
                                                        </tr>
                                                      </tbody>
                                                    </table>
                                                  </div>
                                                </td>
                                              </tr>
                                            </tbody>
                                          </table>
                                        </td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </td>
                              </tr>
                              <tr>
                                <td valign="top" style="padding:0;">
                                  <table width="100%" border="0" cellpadding="0" cellspacing="0" style="border-collapse:initial!important;font-size:14px;min-width:100%">
                                    <tbody>
                                      <tr style="margin:0;padding:0;">
                                        <td valign="top" style="background-color:white;padding:29px 50px 20px 50px;">
                                          <table width="100%" border="0" cellpadding="0" cellspacing="0" style="border-collapse:collapse;font-size:14px;min-width:100%">
                                            <tbody>
                                              <tr>
                                                <td align="center" valign="top" width="507">
                                                  <img
                                                    src="${process.env.APP_URL}/trustsync.png"
                                                    style="display:block;border:none;outline:none;width:200px;margin-bottom:10px"
                                                    class="CToWUd"
                                                    data-bit="iit"
                                                  />
                                                </td>
                                              </tr>
                                              <tr>
                                                <td valign="top" align="left" width="497" style="line-height:200%;margin:0;outline:none;padding:0;font-weight:inherit;line-height:2;text-decoration:inherit;font-family:arial;">
                                                  <div style="color:#171717;font-size:17px;margin-top:15px">${html}</div>
                                                </td>
                                              </tr>
                                            </tbody>
                                          </table>
                                        </td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div style="clear:both;background-color:inherit;background:inherit;min-width:100%;padding:20px;color:#000000;font-size:11px;font-family:arial,helvetica,sans-serif;line-height:140%;line-height:1.4;text-align:center;box-sizing:border-box;"></div>
      </div>
    `;
};

/**
 * Generate weekly report email template
 */
export const weeklyReportEmailTemplate = (
  shop: IShopDocument,
  sentEmails: number,
  pendingEmails: number,
  analyticsPageUrl: string
): string => {
  const userName = shop?.info?.shop_owner || "there";

  return `
   <body style="background-color: #F6F6F6; padding-top: 1px;">
    <table style="width: 700px; background-color: #ffffff; margin: 0 auto; margin-top: 24px; padding:  40px 24px;">
        <tr
            style="text-align: center;  margin-bottom: 35px; display: block; height: 28px;  width: 100%;  text-align: center; ">
            <td style="  display: inline;">
            <a href="#" style"height: 28px;"> <img src="${process.env.APP_URL}/trustsync.png" style="object-fit: cover;  height: 28px; width: auto; text-align: center;"> </a>
            </td>
        </tr>
        <tr>
            <td style="text-align: center; display: inline;">
                <h2
                    style=" font-family: 'Inter', sans-serif; font-size: 32px; font-weight: 600; text-align: center; margin-bottom: 24px; color: #303030; margin-top: 0;">
                    <span style="font-weight: 300; font-style: italic;">Hi,</span> ${userName}
                </h2>

                <p
                    style=" font-family: 'Inter', sans-serif; font-size: 16px; font-weight: 400; text-align: center; color: #303030; margin-bottom: 16px;">
                    Here's a quick look at how your review requests did this week:
                </p>
            </td>
        </tr>
      
        <tr>
        <td style="display: inline-block"> 
        <table style="display: inline-block; width: 650px; overflow:hidden; border-radius: 8px; background-color: #F1F9FF; margin-bottom: 24px; "> 
        <tr
            style="    padding-top: 24px; padding-bottom: 24px; padding-left: 24px; padding-right: 0px; display: block;  width: 650px;">

            <td
                style="background-color: #FFFFFF; border: 1px solid #F0F6FA; border-radius: 8px; padding: 0 24px; display: inline-block; width: 34%;  text-align: left; ">
                <span
                    style="display: inline-block; background-color: #E0FFEA; border: 1px solid #C3F3D2; border-radius: 8px; height: 42px; width: 42px;  text-align: center;  margin-top: 24px; margin-bottom: 24px;">
                    <img src="https://trustcomet.fra1.cdn.digitaloceanspaces.com/production/email-assets/email.png" style="margin-top: 8px; margin-bottom: 6px;">
                </span>
                <span style="display: inline-block; margin-left: 8px;  margin-top: 24px; ">
                    <span
                        style="display: block; text-align: left; font-family: 'Inter', sans-serif; font-size: 24px; color: #00C23E; font-weight: 600; line-height: 1">${sentEmails}</span>
                    <span
                        style="display: block; font-family: 'Inter', sans-serif; font-size: 16px; font-weight: 400; text-align: left; color: #616161; margin-top: -5px">Emails
                        Sent</span>
                </span>
                </span>
            </td>
            <td
                style="background-color: #FFFFFF; border: 1px solid #F0F6FA; border-radius: 8px; padding:0 24px; display: inline-block; width: 34%; text-align: left; margin-left: 10px;">
                <span
                    style="display: inline-block; background-color: #FFF6E5; border: 1px solid #FEEED1; border-radius: 8px; height: 42px; width: 42px;  text-align: center; margin-top: 24px; margin-bottom: 24px;">
                    <img src="https://trustcomet.fra1.cdn.digitaloceanspaces.com/production/email-assets/queue.png"  style="margin-top: 8px; margin-bottom: 6px;">
                </span>
                <span style="display: inline-block; margin-left: 8px;  margin-top: 24px; ">
                    <span
                        style="display: block; text-align: left; font-family: 'Inter', sans-serif; font-size: 24px; color: #F49300; font-weight: 600; line-height: 1;">${pendingEmails}</span>
                    <span
                        style="display: block; font-family: 'Inter', sans-serif; font-size: 16px; font-weight: 400; text-align: left; color: #616161; margin-top: -5px">Emails
                        in Queue</span>
                </span>

            </td>
            <td
                style="background-color: #ffffff8b; border: 1px solid #F0F6FA; border-radius: 8px; padding-left: 0px; display: inline-block; width: 10%;  text-align: left;  margin-left: 10px; ">

                 <span
                    style="display: inline-block; background-color: #EAF3FA; border: 1px solid #E3F1FB; border-radius: 8px; height: 42px; width: 42px;  text-align: center; margin-left: 24px;  margin-top: 24px; margin-bottom: 24px;">
                    <img src="https://trustcomet.fra1.cdn.digitaloceanspaces.com/production/email-assets/Frame.png"  style="margin-top: 8px; margin-bottom: 6px;">
                </span>
            </td>
        </tr>

        </table>
        </td>
        </tr>

        
        <tr style=" margin-bottom: 24px; display: block; padding: 0 50px;">
            <td>
                <p
                    style=" font-family: 'Inter', sans-serif; font-size: 16px; font-weight: 400; text-align: center; color: #616161; padding: 0;">
                    Want to see more details like <span
                        style="font-weight: 500; font-style: italic; color: #303030;">open
                        rates, pending emails, negative feedbacks</span> and which <span
                        style="font-weight: 500; font-style: italic; color: #303030;"> platforms</span> are getting
                    more
                    reviews?</p>
            </td>
        </tr>

        <tr style="text-align: center; margin: 0 auto 48px;">
            <td>
                <a href="${analyticsPageUrl}"
                    style="display: inline-block; font-family: 'Inter', sans-serif; font-size: 16px; font-weight: 400; text-align: center; color: #FFFFFF; background-color: #8D5AEE; border-radius: 8px; padding: 17px 24px;  border: none; text-decoration: none;">Check
                    Your Full Dashboard</a>
            </td>
        </tr>

        <tr style=" display: block; width: 420px;  margin: 0 auto 24px;">
            <td>
                <p
                    style=" font-family: 'Inter', sans-serif; font-size: 16px; font-weight: 300; text-align: center; color: #616161;">
                    Thanks for using TrustSync to grow your business with
                    real customer feedback. Let's keep it going!</p>
            </td>
        </tr>

        <tr
            style=" display: block; padding-bottom: 24px; margin: 0 auto 24px; text-align: center; border-bottom: 1px solid #E7E9F5; ">
            <td style="display: block; width: 200px ; margin: 0 auto;">
                <p
                    style="font-style: italic; font-family: 'Inter', sans-serif; font-size: 16px; font-weight: 300; text-align: center; color: #303030;">
                    Best, <span style="font-weight: 400; font-style: normal; color: #303030;">The TrustSync Team</span>
                </p>
            </td>
        </tr>

        <tr>
            <td>
                <p
                    style=" font-family: 'Inter', sans-serif; font-size: 14px; font-weight: 400; text-align: center; color: #616161; margin-bottom: 16px; padding: 0 20px;">
                    <span style="display: block;">Corporation</span>You are receiving this email because you opted
                    in
                    via
                    our website.
                </p>

            </td>
        </tr>

        <tr style="margin: 0 auto; width: 120px; display: block; margin-bottom: 16px;">

            <td style="display: inline-block; margin-left: 25px ;"> <a href="https://x.com/Storewareapps"
                    style="  display: inline-block;  height: 16px; width: 16px; line-height: 0;"><img
                        src="https://trustcomet.fra1.cdn.digitaloceanspaces.com/production/email-assets/twitter.png" width="100%"></a>
            </td>

            <td style="display: inline-block; margin-left: 25px ;"> <a href="https://www.linkedin.com/company/96298266"
                    style="  display: inline-block;  height: 20px; width: 20px;  line-height: 0;"><img
                        src="https://trustcomet.fra1.cdn.digitaloceanspaces.com/production/email-assets/linkedin.png" width="100%"></a>
            </td>
        </tr>
    </table>
</body>
  `;
};
