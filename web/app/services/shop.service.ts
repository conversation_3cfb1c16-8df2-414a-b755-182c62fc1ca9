import { Session } from "@shopify/shopify-api";
import { ShopInfo } from "@trustsync/types";
import { addDays } from "date-fns";

import EmailSettings from "../models/EmailSettings.js";
import Shop from "../models/Shop.js";
import Subscription from "../models/Subscription.js";
import { graphShopInfo } from "./graph.api.js";

import {
  FLUENT_APP_INSTALLED_TAG_SLUG,
  FLUENT_APP_UNINSTALLED_TAG_SLUG,
  fluentAppInstalledUninstall,
} from "./fluent-crm.service.js";

// When shop installed.
export async function installShop(session: Session): Promise<void> {
  // Check if the shop is already installed
  const existingShop = await Shop.findOne({ shop: session.shop });
  if (existingShop && existingShop.isInstalled) {
    console.log("Shop already installed.");
    return;
  }

  const shopInfo: ShopInfo = await graphShopInfo(session);

  console.log("shopInfo", shopInfo);

  // Update or create shop installation details
  const shopData = await Shop.findOneAndUpdate(
    { shop: session.shop },
    {
      installedOn: Date.now(),
      uninstalledOn: null,
      isInstalled: true,
      scopes: session.scope,
      info: shopInfo,
    },
    { upsert: true }
  );

  fluentAppInstalledUninstall(shopInfo, FLUENT_APP_INSTALLED_TAG_SLUG);

  // Check and set default email settings if not present
  const settings = await EmailSettings.findOne({ shop: session.shop });
  if (!settings) {
    await EmailSettings.findOneAndUpdate(
      { shop: session.shop },
      {
        shop: session.shop,
        from: `${shopInfo.shop_owner} - ${shopInfo.name}`,
        emailHTML: `<p>Hello [first.name].</p><p>We're a small brand and reviews mean the WORLD to us. If you were to leave a review, just let us know and we would send you a 15% coupon to apply on our store. Click on any star to review us below [review.stars]</p><p><br></p><p>Thank you!</p>`,
        // emailHTML: `<p>Hello [first.name].<br/>We're a small brand and reviews mean the WORLD to us. If you were to leave a review, just let us know and we would send you a 15% coupon to apply on our store. Click on any star to review us below [review.stars]</p><br/><p>Thank you!</p>`,
        lowestText: "very dissatisfied",
        highestText: "very satisfied",
      },
      { upsert: true }
    );
  }

  // Check and set default subscription if not present
  const subscription = await Subscription.findOne({ shop: session.shop });
  if (!subscription) {
    await Subscription.findOneAndUpdate(
      { shop: session.shop },
      { plan: "free", resetDate: addDays(new Date(), 30).toISOString() },
      { upsert: true, setDefaultsOnInsert: true }
    );
  }
}

export const uninstalledShop = async (shop: string): Promise<void> => {
  try {
    console.log("Webhook call from APP_UNINSTALLED topic:", shop);

    await Shop.findOneAndUpdate(
      { shop },
      {
        isInstalled: false,
        uninstalledOn: Date.now(),
      }
    );

    const shopData = await Shop.findOne({ shop });

    fluentAppInstalledUninstall(shopData?.info ?? {}, FLUENT_APP_UNINSTALLED_TAG_SLUG);
  } catch (error) {
    console.log({ error });
  }
};
