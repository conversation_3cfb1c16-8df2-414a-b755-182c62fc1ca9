import { ReviewLink, WhenToSend } from "@trustsync/types";
import EmailSettings from "../models/EmailSettings.js";
import Shop from "../models/Shop.js";

// Interface for email setting data
export interface OnboardEmailSettingData {
  active: boolean;
}

// Interface for review link data
export interface OnboardReviewLinkData {
  language: string;
  autoPublish: string;
  reviewLinks: ReviewLink[];
}

// Interface for email trigger setting data
export interface OnboardEmailTriggerData {
  sendOnRepeat: string;
  whenToSend: WhenToSend;
}

export const enabledEmailSendingSetting = async (shop: string, data: OnboardEmailSettingData): Promise<void> => {
  await EmailSettings.findOneAndUpdate({ shop }, { active: !!data.active }, { upsert: true });

  await Shop.findOneAndUpdate({ shop }, { $set: { "onboardingSteps.EMAIL_SENDING": true } }, { upsert: true });
};

export const saveAddReviewLink = async (
  shop: string,
  { language, autoPublish, reviewLinks }: OnboardReviewLinkData
): Promise<void> => {
  await EmailSettings.findOneAndUpdate({ shop }, { language, autoPublish, reviewLinks }, { upsert: true });

  await Shop.findOneAndUpdate({ shop }, { $set: { "onboardingSteps.REVIEW_LINK": true } }, { upsert: true });
};

export const saveOnboardEmailTriggerSetting = async (
  shop: string,
  { sendOnRepeat, whenToSend }: OnboardEmailTriggerData
): Promise<void> => {
  await EmailSettings.findOneAndUpdate({ shop }, { sendOnRepeat, whenToSend }, { upsert: true });

  await Shop.findOneAndUpdate({ shop }, { $set: { "onboardingSteps.EMAIL_TRIGGER": true } }, { upsert: true });
};

export const saveOnboardCompletedSetting = async (shop: string): Promise<void> => {
  await Shop.findOneAndUpdate({ shop }, { $set: { "onboardingSteps.SUBSCRIBED": true } }, { upsert: true });
};
