import express, { Router } from "express";

import { analyticsInfo } from "../controllers/analytics.controller";
import { handleRemoveMail, handleResendMail, handleTestMail } from "../controllers/email.controller";
import { handleAddDomain, handleDomainInfo } from "../controllers/mailgun.controller";
import {
  saveAddReviewLinkSetting,
  saveEmailTriggerSetting,
  saveEnableEmailSetting,
} from "../controllers/onboard.controller";
import { getPastOrderList, schedulePastOrder } from "../controllers/order.controller";
import {
  hangleEmailSettings,
  saveEmailDesignSettings,
  saveGeneralEmailSettings,
  saveReviewSettings,
} from "../controllers/setting.controller";
import { getShop, getShopInfo } from "../controllers/shop.controller";
import { freePlanSubscription, getShopSubscription, proPlanSubscription } from "../controllers/subscription.controller";
import { uploadShopImage, uploadShopLogo } from "../controllers/upload.controller";

import { upload } from "../clients/multer";
import {
  getNegativeFeedbackList,
  getNegativeFeedbackRemove,
  getNegativeFeedbackReply,
} from "../controllers/negative-feedback.controller";
import { handleConfigureSetting } from "../controllers/shop-configure-setting";

const router: Router = express.Router();

router.get("/shop", getShop);
router.get("/shop-info", getShopInfo);

router.get("/settings", hangleEmailSettings);
router.post("/settings/review-settings", saveReviewSettings);
router.post("/settings/email-designs", saveEmailDesignSettings);
router.post("/settings/general-settings", saveGeneralEmailSettings);

// Dismissaable banner configuration
router.post("/configure-settings", handleConfigureSetting);

router.post("/onboard/email-enable", saveEnableEmailSetting);
router.post("/onboard/review-link", saveAddReviewLinkSetting);
router.post("/onboard/email-trigger", saveEmailTriggerSetting);

router.post("/email/remove-mail", handleRemoveMail);
router.post("/email/resend-mail", handleResendMail);
router.post("/email/send-test-mail", handleTestMail);

router.get("/subscription", getShopSubscription);
router.post("/subscribe/free-plan", freePlanSubscription);
router.post("/subscribe/pro-plan", proPlanSubscription);

router.post("/upload-logo", upload.single("image"), uploadShopLogo);
router.post("/upload-image", upload.single("image"), uploadShopImage);

router.get("/analytics-info", analyticsInfo);

router.get("/mailgun/domain-info", handleDomainInfo);
router.post("/mailgun/add-domain", handleAddDomain);

router.get("/negative-feedbacks/list", getNegativeFeedbackList);
router.post("/negative-feedbacks/remove", getNegativeFeedbackRemove);
router.post("/negative-feedbacks/reply", getNegativeFeedbackReply);

router.get("/past-orders", getPastOrderList);
router.post("/past-orders/schedule", schedulePastOrder);

export default router;
