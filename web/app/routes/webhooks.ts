import { DeliveryMethod, WebhookHandler } from "@shopify/shopify-api";
import { verifyWebhookShopifySubscription } from "../services/subscription.service.js";
import { processOrderWebhook } from "../services/webhook.service.js";

const webhookHandlers: { [key: string]: WebhookHandler } = {
  CUSTOMERS_DATA_REQUEST: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/webhooks/customer-data-request",
    callback: async (topic, shop, body, webhookId) => {
      const payload = JSON.parse(body);
    },
  },

  CUSTOMERS_REDACT: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/webhooks/customer-data",
    callback: async (topic, shop, body, webhookId) => {
      const payload = JSON.parse(body);
    },
  },

  SHOP_REDACT: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/webhooks/shop-data",
    callback: async (topic, shop, body, webhookId) => {
      const payload = JSON.parse(body);
    },
  },

  ORDERS_CREATE: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/webhooks/handle-order/created",
    callback: async (topic, shop, body, webhookId) => {
      await processOrderWebhook(topic, shop, JSON.parse(body));
    },
  },

  ORDERS_UPDATED: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/webhooks/handle-order/updated",
    callback: async (topic, shop, body, webhookId) => {
      await processOrderWebhook(topic, shop, JSON.parse(body));
    },
  },

  ORDERS_PAID: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/webhooks/handle-order/paid",
    callback: async (topic, shop, body, webhookId) => {
      await processOrderWebhook(topic, shop, JSON.parse(body));
    },
  },

  ORDERS_FULFILLED: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/webhooks/handle-order/fulfilled",
    callback: async (topic, shop, body, webhookId) => {
      await processOrderWebhook(topic, shop, JSON.parse(body));
    },
  },

  APP_SUBSCRIPTIONS_UPDATE: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/webhooks/app/subscription-update",
    callback: async (topic, shop, body, webhookId) => {
      const { app_subscription } = JSON.parse(body);

      console.log("Subscribe webhook call");

      await verifyWebhookShopifySubscription(shop, app_subscription);
    },
  },
};

export default webhookHandlers;
