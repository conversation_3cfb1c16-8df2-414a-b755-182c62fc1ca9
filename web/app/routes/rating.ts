import express, { Router } from "express";

import { upload } from "../clients/multer.js";
import { handleNegativeReview, handleNegativeReviewList } from "../controllers/negative-reviews.controller.js";

const router: Router = express.Router();

router.get("/negative-reviews", handleNegativeReviewList);
router.post("/negative-reviews", upload.single("attachment"), handleNegativeReview);

export default router;
