export interface ReviewLink {
  id: string;
  platform: string;
  url: string;
  percentage: string | number;
}

interface ReviewSettingRequest {
  autoPublish: string | null;
  reviewLinks: ReviewLink[] | null;
  customNegativeForm: boolean;
  customNegativeLink: string;
}

interface ValidationMessages {
  autoPublish: string | null;
  reviewLinks: string | null;
  customNegativeLink?: string | null;
  isValid: boolean;
}

const isURLValid = (url: string): boolean => {
  const urlRegex = /^(ftp|http|https):\/\/[^ "]+$/;
  return urlRegex.test(url);
};

export const reviewSettingValidation = {
  validateAutoPublish: (value: string | null) => {
    if (!value) {
      return "Please select auto-publish for new reviews.";
    }

    return null; // Indicates no error
  },

  validateReviewLinks: (values: ReviewLink[] | null) => {
    if (!values || values?.length === 0) {
      return null;
    }

    let totalPercentage = 0;
    for (const value of values) {
      if (!value.platform || !value.url || !value.percentage) {
        return `Please provide valid ${value.platform} review link.`;
      }

      if (!isURLValid(value.url)) {
        return `Please enter a valid URL for the ${value.platform} review link.`;
      }

      if (value.percentage) {
        totalPercentage += parseInt(value.percentage.toString(), 10);
      }

      if (totalPercentage > 100) {
        return "You review link total total percentage must be 100%.";
      }
    }

    return null; // Indicates no error
  },

  validateCustomNegativeLink: (value: string | null) => {
    if (!value) {
      return "This custom negative link field is required.";
    }

    if (!isURLValid(value)) {
      return `Please enter a valid custom negative link URL.`;
    }

    return null; // Indicates no error
  },

  validateForm: (request: ReviewSettingRequest): ValidationMessages => {
    let messages: any = {
      autoPublish: reviewSettingValidation.validateAutoPublish(request.autoPublish),
      reviewLinks: reviewSettingValidation.validateReviewLinks(request.reviewLinks),
    };

    if (request.customNegativeForm) {
      messages = {
        ...messages,
        customNegativeLink: reviewSettingValidation.validateCustomNegativeLink(request.customNegativeLink),
      };
    }

    const isValid = Object.values(messages).every((message) => !message);

    return { ...messages, isValid };
  },
};
