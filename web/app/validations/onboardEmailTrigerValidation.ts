const onboardEmailTrigerValidation = {
  validateTagAdd: ({ after, tag }: { after: string; tag: string }) => {
    if (after === "tagadded" && !tag) {
      return "The tag field is required.";
    }

    return null; // Indicates no error
  },

  validateForm: ({ whenToSend }: { whenToSend: { after: string; tag: string } }) => {
    const request = { whenToSend };

    let messages = {
      tag: onboardEmailTrigerValidation.validateTagAdd(request.whenToSend),
    };

    const isValid = Object.values(messages).every((message) => !message);

    return { ...messages, isValid };
  },
};

export default onboardEmailTrigerValidation;
