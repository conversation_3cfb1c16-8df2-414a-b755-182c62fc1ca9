interface EmailDesignSettingRequest {
  from?: string;
  subject?: string;
  emailHTML?: string;
  designTemplate?: string;
  lowestText?: string;
  highestText?: string;
  logoHeight?: number;
  bgColor?: string;
  borderColor?: string;
  logo?: string;
  [key: string]: any;
}

interface ValidationMessages {
  from: string | null;
  subject: string | null;
  emailHTML: string | null;
  designTemplate: string | null;
  lowestText: string | null;
  highestText: string | null;
  logoHeight: string | null;
  bgColor: string | null;
  borderColor: string | null;
  isValid: boolean;
}

export const emailDesignSettingValidation = {
  validateFrom: (value?: string): string | null => {
    if (!value) {
      return "The from field is required.";
    }

    return null; // Indicates no error
  },

  validateSubject: (value?: string): string | null => {
    if (!value) {
      return "The subject field is required.";
    }

    return null; // Indicates no error
  },

  validateTemplate: (value?: string): string | null => {
    if (!value) {
      return "The template field is required.";
    }

    return null; // Indicates no error
  },

  validateDesignTemplate: (value?: string): string | null => {
    if (!value) {
      return "The design template field is required.";
    }

    return null; // Indicates no error
  },

  validateLowestText: (value?: string): string | null => {
    if (!value) {
      return "The lowest text field is required.";
    }

    return null; // Indicates no error
  },

  validateHighestText: (value?: string): string | null => {
    if (!value) {
      return "The heighest text field is required.";
    }

    return null; // Indicates no error
  },

  validateHeight: (value?: number): string | null => {
    if (!value) {
      return "The height field is required.";
    }

    return null; // Indicates no error
  },

  validateBgColor: (value?: string): string | null => {
    if (!value) {
      return "The bg color field is required.";
    }

    return null; // Indicates no error
  },

  validateBorderColor: (value?: string): string | null => {
    if (!value) {
      return "The border color field is required.";
    }

    return null; // Indicates no error
  },

  validateForm: (request: EmailDesignSettingRequest): ValidationMessages => {
    let messages = {
      from: emailDesignSettingValidation.validateFrom(request.from),
      subject: emailDesignSettingValidation.validateSubject(request.subject),
      emailHTML: emailDesignSettingValidation.validateTemplate(request.emailHTML),
      designTemplate: emailDesignSettingValidation.validateDesignTemplate(request.designTemplate),
      lowestText: emailDesignSettingValidation.validateLowestText(request.lowestText),
      highestText: emailDesignSettingValidation.validateHighestText(request.highestText),
      logoHeight: emailDesignSettingValidation.validateHeight(request.logoHeight),
      bgColor: emailDesignSettingValidation.validateBgColor(request.bgColor),
      borderColor: emailDesignSettingValidation.validateBorderColor(request.borderColor),
    };

    const isValid = Object.values(messages).every((message) => !message);

    return { ...messages, isValid };
  },
};
