import { validateEmail } from "../utils/helper.js";

export const sendTestEmailValidation = {
  validateEmail: (value: string) => {
    if (!value) {
      return "The email field is required.";
    }

    if (!validateEmail(value)) {
      return "Please enter a valid email.";
    }

    return null; // Indicates no error
  },

  validateForm: ({ email }: { email: string }) => {
    let messages = {
      email: sendTestEmailValidation.validateEmail(email),
    };

    const isValid = Object.values(messages).every((message) => !message);

    return { ...messages, isValid };
  },
};
