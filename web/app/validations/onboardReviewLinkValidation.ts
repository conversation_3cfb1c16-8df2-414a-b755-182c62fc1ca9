// import { ReviewLink } from "./reviewSettingValidation";

import { ReviewLink } from "@trustsync/types";

const isURLValid = (url: string) => {
  const urlRegex = /^(ftp|http|https):\/\/[^ "]+$/;
  return urlRegex.test(url);
};

const onboardReviewLinkValidation = {
  validateAutoPublish: (value: string | number | null) => {
    if (!value) {
      return "Please select auto-publish for new reviews.";
    }

    return null; // Indicates no error
  },

  validateReviewLinks: (values: ReviewLink[] | null) => {
    if (!values || values?.length === 0) {
      return null;
    }

    let totalPercentage = 0;
    for (const value of values) {
      if (!value.platform || !value.url || !value.percentage) {
        return `Please provide valid ${value.platform} review link.`;
      }

      if (!isURLValid(value.url)) {
        return `Please enter a valid URL for the ${value.platform} review link.`;
      }

      if (value.percentage) {
        totalPercentage += Number(value.percentage);
      }

      if (totalPercentage > 100) {
        return "You review link total total percentage must be 100%.";
      }
    }

    return null; // Indicates no error
  },

  validateForm: ({
    autoPublish,
    reviewLinks,
  }: {
    autoPublish: string | number | null;
    reviewLinks: ReviewLink[] | null;
  }) => {
    const messages = {
      autoPublish: onboardReviewLinkValidation.validateAutoPublish(autoPublish),
      reviewLinks: onboardReviewLinkValidation.validateReviewLinks(reviewLinks),
    };

    const isValid = Object.values(messages).every((message) => !message);

    return { ...messages, isValid };
  },
};

export default onboardReviewLinkValidation;
