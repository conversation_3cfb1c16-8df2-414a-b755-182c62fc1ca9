export const generalSettingValidation = {
  validateTagAdd: ({ after, tag }: { after: string; tag: string }) => {
    if (after === "tagadded" && !tag) {
      return "The tag field is required.";
    }

    return null; // Indicates no error
  },

  validateSender: (value: string | null) => {
    if (!value) {
      return "The sender field is required.";
    }

    return null; // Indicates no error
  },

  validateDomain: (values: string[] | null) => {
    if (!values || values?.length === 0) {
      return "The domain field is required.";
    }

    return null; // Indicates no error
  },

  validateReplyToEmails: (value: string[] | null) => {
    if (!value || value?.length === 0) {
      return "The reply to field is required.";
    }

    return null; // Indicates no error
  },

  validateForm: (request: {
    whenToSend: { after: string; tag: string };
    sender: string;
    selectedReplyToEmails: string[];
  }) => {
    let messages = {
      tag: generalSettingValidation.validateTagAdd(request.whenToSend),
      sender: generalSettingValidation.validateSender(request.sender),
      selectedReplyToEmails: generalSettingValidation.validateReplyToEmails(request.selectedReplyToEmails),
    };

    const isValid = Object.values(messages).every((message) => !message);

    return { ...messages, isValid };
  },
};
