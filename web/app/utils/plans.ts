import { Plan } from "@trustsync/types";

const HUNDRED_EMAIL_WHEN_VERIFIED = 5;
const MULTIPLE_REVIEW_LINK = 10;
const EMAIL_ANALYTICS = 15;
const SEND_EMAIL_STATES = 20;
const MULTIPLE_EMAIL_TEMPLATES = 25;
const BAD_REVIEW_FILTER = 30;
const EMAIL_TRIGGER_OPTIONS = 35;
const SEND_AUTOMATIC_2ND_EMAIL = 40;
const SENDING_TIME_OPTION = 45;
const SKIP_POST_ORDER_OPTION = 50;
const EMAIL_BLACKLIST = 55;

const plans: Plan[] = [
  {
    name: "Free",
    slug: "free",
    icon: "",
    price: 0,
    discountPrice: 0,
    emails: 50,
    options: [
      {
        id: HUNDRED_EMAIL_WHEN_VERIFIED,
        label: "100 emails/mo when verified",
        enable: true,
      },
      {
        id: EMAIL_ANALYTICS,
        label: "Email analytics",
        enable: true,
      },
      {
        id: BAD_REVIEW_FILTER,
        label: "Bad review filter",
        enable: true,
      },
      {
        id: MULTIPLE_REVIEW_LINK,
        label: "Multiple review links",
        enable: true,
      },
      // {
      //   id: SEND_EMAIL_STATES,
      //   label: "Sent emails stats",
      //   enable: true
      // },
      {
        id: MULTIPLE_EMAIL_TEMPLATES,
        label: "10+ email templates",
        enable: false,
      },
      // {
      //   id: EMAIL_TRIGGER_OPTIONS,
      //   label: "Email trigger options",
      //   enable: true,
      // },
      {
        id: SEND_AUTOMATIC_2ND_EMAIL,
        label: "Send automatic 2nd email",
        enable: false,
      },
      {
        id: SENDING_TIME_OPTION,
        label: "Sending time option",
        enable: false,
      },
      {
        id: SKIP_POST_ORDER_OPTION,
        label: "Skip POS orders option",
        enable: false,
      },
      {
        id: EMAIL_BLACKLIST,
        label: "Email blacklist",
        enable: false,
      },
    ],
  },
  {
    name: "Enterprise",
    slug: "enterprise",
    icon: "",
    price: 99.99,
    discountPrice: 99.99,
    emails: 10000,
    options: [
      // {
      //   id: HUNDRED_EMAIL_WHEN_VERIFIED,
      //   label: "100 emails/mo when verified",
      //   enable: true,
      // },
      {
        id: EMAIL_ANALYTICS,
        label: "Email analytics",
        enable: true,
      },
      {
        id: BAD_REVIEW_FILTER,
        label: "Bad review filter",
        enable: true,
      },
      {
        id: MULTIPLE_REVIEW_LINK,
        label: "Multiple review links",
        enable: true,
      },
      // {
      //   id: SEND_EMAIL_STATES,
      //   label: "Sent emails stats",
      //   enable: true
      // },
      {
        id: MULTIPLE_EMAIL_TEMPLATES,
        label: "10+ email templates",
        enable: true,
      },
      // {
      //   id: EMAIL_TRIGGER_OPTIONS,
      //   label: "Email trigger options",
      //   enable: true,
      // },
      {
        id: SEND_AUTOMATIC_2ND_EMAIL,
        label: "Send automatic 2nd email",
        enable: true,
      },
      {
        id: SENDING_TIME_OPTION,
        label: "Sending time option",
        enable: true,
      },
      {
        id: SKIP_POST_ORDER_OPTION,
        label: "Skip POS orders option",
        enable: true,
      },
      {
        id: EMAIL_BLACKLIST,
        label: "Email blacklist",
        enable: true,
      },
    ],
  },
  {
    name: "Basic",
    slug: "basic",
    icon: "",
    price: 9.99,
    discountPrice: 9.99,
    emails: 250,
    options: [
      // {
      //   id: HUNDRED_EMAIL_WHEN_VERIFIED,
      //   label: "100 emails/mo when verified",
      //   enable: true,
      // },
      {
        id: EMAIL_ANALYTICS,
        label: "Email analytics",
        enable: true,
      },
      {
        id: BAD_REVIEW_FILTER,
        label: "Bad review filter",
        enable: true,
      },
      {
        id: MULTIPLE_REVIEW_LINK,
        label: "Multiple review links",
        enable: true,
      },
      // {
      //   id: SEND_EMAIL_STATES,
      //   label: "Sent emails stats",
      //   enable: true
      // },
      {
        id: MULTIPLE_EMAIL_TEMPLATES,
        label: "10+ email templates",
        enable: true,
      },
      // {
      //   id: EMAIL_TRIGGER_OPTIONS,
      //   label: "Email trigger options",
      //   enable: true,
      // },
      {
        id: SEND_AUTOMATIC_2ND_EMAIL,
        label: "Send automatic 2nd email",
        enable: true,
      },
      {
        id: SENDING_TIME_OPTION,
        label: "Sending time option",
        enable: true,
      },
      {
        id: SKIP_POST_ORDER_OPTION,
        label: "Skip POS orders option",
        enable: true,
      },
      {
        id: EMAIL_BLACKLIST,
        label: "Email blacklist",
        enable: true,
      },
    ],
  },
  {
    name: "Pro",
    slug: "pro",
    icon: "",
    price: 19.99,
    discountPrice: 19.99,
    emails: 750,
    options: [
      // {
      //   id: HUNDRED_EMAIL_WHEN_VERIFIED,
      //   label: "100 emails/mo when verified",
      //   enable: true,
      // },
      {
        id: EMAIL_ANALYTICS,
        label: "Email analytics",
        enable: true,
      },
      {
        id: BAD_REVIEW_FILTER,
        label: "Bad review filter",
        enable: true,
      },
      {
        id: MULTIPLE_REVIEW_LINK,
        label: "Multiple review links",
        enable: true,
      },
      // {
      //   id: SEND_EMAIL_STATES,
      //   label: "Sent emails stats",
      //   enable: true
      // },
      {
        id: MULTIPLE_EMAIL_TEMPLATES,
        label: "10+ email templates",
        enable: true,
      },
      // {
      //   id: EMAIL_TRIGGER_OPTIONS,
      //   label: "Email trigger options",
      //   enable: true,
      // },
      {
        id: SEND_AUTOMATIC_2ND_EMAIL,
        label: "Send automatic 2nd email",
        enable: true,
      },
      {
        id: SENDING_TIME_OPTION,
        label: "Sending time option",
        enable: true,
      },
      {
        id: SKIP_POST_ORDER_OPTION,
        label: "Skip POS orders option",
        enable: true,
      },
      {
        id: EMAIL_BLACKLIST,
        label: "Email blacklist",
        enable: true,
      },
    ],
  },
  {
    name: "Elite",
    slug: "elite",
    icon: "",
    price: 39.99,
    discountPrice: 39.99,
    emails: 2000,
    options: [
      // {
      //   id: HUNDRED_EMAIL_WHEN_VERIFIED,
      //   label: "100 emails/mo when verified",
      //   enable: true,
      // },
      {
        id: EMAIL_ANALYTICS,
        label: "Email analytics",
        enable: true,
      },
      {
        id: BAD_REVIEW_FILTER,
        label: "Bad review filter",
        enable: true,
      },
      {
        id: MULTIPLE_REVIEW_LINK,
        label: "Multiple review links",
        enable: true,
      },
      // {
      //   id: SEND_EMAIL_STATES,
      //   label: "Sent emails stats",
      //   enable: true
      // },
      {
        id: MULTIPLE_EMAIL_TEMPLATES,
        label: "10+ email templates",
        enable: true,
      },
      // {
      //   id: EMAIL_TRIGGER_OPTIONS,
      //   label: "Email trigger options",
      //   enable: true,
      // },
      {
        id: SEND_AUTOMATIC_2ND_EMAIL,
        label: "Send automatic 2nd email",
        enable: true,
      },
      {
        id: SENDING_TIME_OPTION,
        label: "Sending time option",
        enable: true,
      },
      {
        id: SKIP_POST_ORDER_OPTION,
        label: "Skip POS orders option",
        enable: true,
      },
      {
        id: EMAIL_BLACKLIST,
        label: "Email blacklist",
        enable: true,
      },
    ],
  },
];

export default plans;

export const getPlan = (slug: string): Plan | undefined => {
  return plans.find((plan) => plan.slug === slug);
};

export const getProPlan = (): Plan[] => {
  return plans.filter((plan) => plan.slug !== "enterprise" && plan.slug !== "free");
};

export const getCurrentPlan = (slug: string): Plan | undefined => {
  return plans.find((plan) => plan.slug === slug);
};

export const getNextPlan = (slug: string): Plan => {
  const currentIndex = plans.findIndex((plan) => plan.slug === slug);
  if (currentIndex === -1 || currentIndex === plans.length - 1) {
    return plans[plans.length - 1];
  }

  return plans[currentIndex + 1];
};
