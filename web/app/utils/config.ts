export interface OptionItem {
  label: string;
  value: string | number;
}

export interface ReviewLinkOption {
  label: string;
  value: string;
  prefix: boolean;
}

export const autoPublishOptions: OptionItem[] = [
  { label: "Only 5-star", value: 5 },
  { label: "4-star and 5-star", value: 4 },
  { label: "3-star, 4-star and 5-star", value: 3 },
];

export const reviewLinkOptions: ReviewLinkOption[] = [
  {
    label: "Google",
    value: "google",
    prefix: false,
  },
  {
    label: "Trustpilot",
    value: "trustpilot",
    prefix: false,
  },
  {
    label: "Facebook",
    value: "facebook",
    prefix: false,
  },
  {
    label: "Amazon",
    value: "amazon",
    prefix: false,
  },
  {
    label: "Yelp",
    value: "yelp",
    prefix: false,
  },
  {
    label: "Bazaarvoice",
    value: "bazaarvoice",
    prefix: false,
  },
  {
    label: "Yotpo",
    value: "yotpo",
    prefix: false,
  },
  {
    label: "Powerreviews",
    value: "powerreviews",
    prefix: false,
  },
  {
    label: "Reevoo",
    value: "reevoo",
    prefix: false,
  },
  {
    label: "Feefo",
    value: "feefo",
    prefix: false,
  },
  {
    label: "Revi",
    value: "revi",
    prefix: false,
  },
  {
    label: "TrustedShops",
    value: "trustedshops",
    prefix: false,
  },
  {
    label: "Foursquare",
    value: "foursquare",
    prefix: false,
  },
  {
    label: "Sitejabber",
    value: "sitejabber",
    prefix: false,
  },
  {
    label: "ResellerRatings",
    value: "resellerratings",
    prefix: false,
  },
  {
    label: "Ebay",
    value: "ebay",
    prefix: false,
  },
  {
    label: "Pluck",
    value: "pluc",
    prefix: false,
  },
  {
    label: "RIVIO",
    value: "rivio",
    prefix: false,
  },
  {
    label: "Capterra",
    value: "capterra",
    prefix: false,
  },
  {
    label: "Getapp",
    value: "getapp",
    prefix: false,
  },
  {
    label: "Reviews.io",
    value: "reviewsio",
    prefix: false,
  },
  {
    label: "ConsumerAffairs",
    value: "cosumeraffairs",
    prefix: false,
  },
  {
    label: "Other",
    value: "other",
    prefix: false,
  },
];

export const autoPublishHelpTextOptions: Record<number, string> = {
  3: "1 and 2",
  4: "1, 2 and 3",
  5: "1, 2, 3 and 4",
};

export const percentageReviewOptions: OptionItem[] = [
  { label: "100% of the time", value: "100%" },
  { label: "90% of the time", value: "90%" },
  { label: "80% of the time", value: "80%" },
  { label: "70% of the time", value: "70%" },
  { label: "60% of the time", value: "60%" },
  { label: "50% of the time", value: "50%" },
  { label: "40% of the time", value: "40%" },
  { label: "30% of the time", value: "30%" },
  { label: "20% of the time", value: "20%" },
  { label: "10% of the time", value: "10%" },
];

export const timeOptions: OptionItem[] = [
  { label: "00:00", value: "00:00" },
  { label: "00:30", value: "00:30" },
  { label: "01:00", value: "01:00" },
  { label: "01:30", value: "01:30" },
  { label: "02:00", value: "02:00" },
  { label: "02:30", value: "02:30" },
  { label: "03:00", value: "03:00" },
  { label: "03:30", value: "03:30" },
  { label: "04:00", value: "04:00" },
  { label: "04:30", value: "04:30" },
  { label: "05:00", value: "05:00" },
  { label: "05:30", value: "05:30" },
  { label: "06:00", value: "06:00" },
  { label: "06:30", value: "06:30" },
  { label: "07:00", value: "07:00" },
  { label: "07:30", value: "07:30" },
  { label: "08:00", value: "08:00" },
  { label: "08:30", value: "08:30" },
  { label: "09:00", value: "09:00" },
  { label: "09:30", value: "09:30" },
  { label: "10:00", value: "10:00" },
  { label: "10:30", value: "10:30" },
  { label: "11:00", value: "11:00" },
  { label: "11:30", value: "11:30" },
  { label: "12:00", value: "12:00" },
  { label: "12:30", value: "12:30" },
  { label: "13:00", value: "13:00" },
  { label: "13:30", value: "13:30" },
  { label: "14:00", value: "14:00" },
  { label: "14:30", value: "14:30" },
  { label: "15:00", value: "15:00" },
  { label: "15:30", value: "15:30" },
  { label: "16:00", value: "16:00" },
  { label: "16:30", value: "16:30" },
  { label: "17:00", value: "17:00" },
  { label: "17:30", value: "17:30" },
  { label: "18:00", value: "18:00" },
  { label: "18:30", value: "18:30" },
  { label: "19:00", value: "19:00" },
  { label: "19:30", value: "19:30" },
  { label: "20:00", value: "20:00" },
  { label: "20:30", value: "20:30" },
  { label: "21:00", value: "21:00" },
  { label: "21:30", value: "21:30" },
  { label: "22:00", value: "22:00" },
  { label: "22:30", value: "22:30" },
  { label: "23:00", value: "23:00" },
  { label: "23:30", value: "23:30" },
];

export const designOptions: OptionItem[] = [
  {
    label: "Gray Stars inside squares",
    value: "boxedGray",
  },
  {
    label: "Numbers",
    value: "numbers",
  },
  {
    label: "Google Review Stars",
    value: "regular",
  },
  {
    label: "Gray Stars",
    value: "regularGray",
  },
  {
    label: "Colorful Boxed Vertical Stars",
    value: "colored",
  },
  {
    label: "Regular Boxed Vertical Stars",
    value: "regularVerticalBoxed",
  },
  {
    label: "Regular Vertical Stars",
    value: "regularVertical",
  },
  {
    label: "Hearts Emojis",
    value: "heart",
  },
  {
    label: "Taco Emojis",
    value: "taco",
  },
  {
    label: "Thuder Emojis",
    value: "thunder",
  },
  {
    label: "Fire Emojis",
    value: "fire",
  },
];

export const whenOptions: OptionItem[] = [
  { label: "1 day", value: "1" },
  { label: "2 days", value: "2" },
  { label: "3 days", value: "3" },
  { label: "4 days", value: "4" },
  { label: "5 days", value: "5" },
  { label: "6 days", value: "6" },
  { label: "7 days", value: "7" },
  { label: "8 days", value: "8" },
  { label: "9 days", value: "9" },
  { label: "10 days", value: "10" },
  { label: "11 days", value: "11" },
  { label: "12 days", value: "12" },
  { label: "13 days", value: "13" },
  { label: "14 days", value: "14" },
  { label: "15 days", value: "15" },
  { label: "16 days", value: "16" },
  { label: "17 days", value: "17" },
  { label: "18 days", value: "18" },
  { label: "19 days", value: "19" },
  { label: "20 days", value: "20" },
  { label: "21 days", value: "21" },
  { label: "22 days", value: "22" },
  { label: "23 days", value: "23" },
  { label: "24 days", value: "24" },
  { label: "25 days", value: "25" },
  { label: "26 days", value: "26" },
  { label: "27 days", value: "27" },
  { label: "28 days", value: "28" },
  { label: "29 days", value: "29" },
  { label: "30 days", value: "30" },
];

export const whenActionOptions: OptionItem[] = [
  { label: "after fulfilled", value: "fulfilled" },
  { label: "after tag is added", value: "tagadded" },
  { label: "after order created", value: "created" },
  { label: "after order paid", value: "paid" },
];

export const secondWhenActionOptions: OptionItem[] = [
  { label: "after 1st email", value: "email" },
  { label: "after order", value: "order" },
];

export const repeatOptions: OptionItem[] = [
  { label: "Ask again", value: "yes" },
  { label: "Don't ask again", value: "no" },
];
