import { fromZonedTime, toZonedTime } from "date-fns-tz";

import { autoPublishHelpTextOptions } from "./config.js";

export const helpTextForAutoPublish = (autoPublish: number): string => {
  return autoPublishHelpTextOptions[autoPublish] ?? "";
};

export const calculatePercentage = (sendEmails: number, limitEmails: number): number => {
  let percentage = Math.ceil((sendEmails / limitEmails) * 100);
  return parseInt(Math.min(percentage, 100).toString()) || 0; // Set percentage to 100 if it's greater than 100
};

export const validateEmail = (email: string): boolean => {
  var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const roundToTwoDigits = (num: number): number => {
  return Math.round((num + Number.EPSILON) * 100) / 100;
};

export const capitalizeFirstLetter = (str: string): string => {
  if (str.length === 0) {
    return str;
  }
  return str.charAt(0).toUpperCase() + str.slice(1);
};

export const getDateTimeStamp = (date: Date | string): number => {
  return new Date(date).getTime();
};

export const sleep = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

interface TimeConfig {
  specificTimeActive: boolean;
  specificTime: string;
}

export const addTimeToDate = ({ specificTimeActive, specificTime }: TimeConfig, shopTimeZone?: string): Date => {
  if (!specificTimeActive || !specificTime) {
    return new Date();
  }

  const timezone = shopTimeZone || Intl.DateTimeFormat().resolvedOptions().timeZone;

  // Get the current date in the user's timezone
  const currentDate = new Date();
  const zonedDate = toZonedTime(currentDate, timezone);

  // Split the time string into hours and minutes
  const [hours, minutes] = specificTime.split(":").map(Number);

  // Set the hours and minutes to the zoned date
  zonedDate.setHours(hours);
  zonedDate.setMinutes(minutes);
  zonedDate.setSeconds(0);
  zonedDate.setMilliseconds(0);

  // Convert back to UTC
  return fromZonedTime(zonedDate, timezone);
};

export const validateImage = (file: File): string => {
  const validTypes = ["image/jpg", "image/jpeg", "image/png", "image/gif"];
  const maxSize = 2 * 1024 * 1024; // 2 MB

  if (!validTypes.includes(file.type)) {
    return "Invalid file type. Only JPEG, PNG, and GIF are allowed.";
  }

  if (file.size > maxSize) {
    return "File size exceeds the limit of 2MB.";
  }

  return "";
};

export const truncateString = (str?: string): string => {
  if (!str) {
    return "";
  }

  if (str.length > 20) {
    return str.slice(0, 20) + "...";
  }

  return str;
};

export const isEmpty = (value: any): boolean => {
  if (Array.isArray(value)) {
    return value.length === 0;
  } else {
    return value === "" || value == null;
  }
};

export const getDOImageUrl = (name: string): string => {
  return process.env.STORAGE_CDN_BASE_URL + "/" + name;
};

export const extractId = (gid: string): number | string => {
  if (!gid || typeof gid !== "string") {
    return gid || "";
  }

  return Number(gid.split("/").pop());
};

export const flattenShopData = (data: any): any => {
  return {
    id: extractId(data.id),
    name: data.name,
    email: data.email,
    url: data.url,
    myshopifyDomain: data.myshopifyDomain,
    timezoneAbbreviation: data.timezoneAbbreviation,
    currencyCode: data.currencyCode,
    shop_owner: data.shopOwnerName,
    moneyWithCurrencyFormat: data.currencyFormats?.moneyWithCurrencyFormat,
    displayName: data.plan?.displayName,
    partnerDevelopment: data.plan?.partnerDevelopment,
    shopifyPlus: data.plan?.shopifyPlus,
    address1: data.billingAddress?.address1,
    address2: data.billingAddress?.address2,
    city: data.billingAddress?.city,
    company: data.billingAddress?.company,
    country: data.billingAddress?.country,
    countryCodeV2: data.billingAddress?.countryCodeV2,
    billingAddressId: data.billingAddress?.id, // Renamed to avoid conflict with main `id`
    latitude: data.billingAddress?.latitude,
    longitude: data.billingAddress?.longitude,
    province: data.billingAddress?.province,
    provinceCode: data.billingAddress?.provinceCode,
    zip: data.billingAddress?.zip,
    formattedArea: data.billingAddress?.formattedArea,
    phone: data.billingAddress?.phone,
  };
};

export const stripTags = (html: string): string => html.replace(/<[^>]*>/g, "");

export const extractOrderIdFromEmail = (str: string): string | null => {
  const match = str.match(/#\d+/);
  const orderId = match ? match[0].slice(1) : null;
  return orderId;
};

export const stripShopDomain = (shop: string): string => shop.replace(".myshopify.com", "");
