import dotenv from "dotenv";
dotenv.config({ path: "./web/.env" });

// APP_UNINSTALLED
// APP_SUBSCRIPTIONS_UPDATE

// ORDERS_CREATE
// ORDERS_UPDATED
// ORDERS_PAID
// ORDERS_FULFILLED

const WEBHOOK_BASED_URL = process.env.APP_URL || "https://app.trustsync.io";

interface WebhookConfig {
  topic: string;
  callbackUrl: string;
  format: string;
}

const webhooks: WebhookConfig[] = [
  {
    topic: "APP_UNINSTALLED",
    callbackUrl: WEBHOOK_BASED_URL + "/webhooks",
    format: "JSON",
  },
  {
    topic: "APP_SUBSCRIPTIONS_UPDATE",
    callbackUrl: WEBHOOK_BASED_URL + "/webhooks/app/subscription-update",
    format: "JSON",
  },
  {
    topic: "ORDERS_CREATE",
    callbackUrl: WEB<PERSON>O<PERSON>_BASED_URL + "/webhooks/handle-order/created",
    format: "JSON",
  },
  {
    topic: "ORDERS_UPDATED",
    callbackUrl: WEBHOOK_BASED_URL + "/webhooks/handle-order/updated",
    format: "JSON",
  },
  {
    topic: "ORDERS_PAID",
    callbackUrl: WEBHOOK_BASED_URL + "/webhooks/handle-order/paid",
    format: "JSON",
  },
  {
    topic: "ORDERS_FULFILLED",
    callbackUrl: WEBHOOK_BASED_URL + "/webhooks/handle-order/fulfilled",
    format: "JSON",
  },
];

export default webhooks;
