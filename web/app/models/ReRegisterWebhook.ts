import { BaseEntity } from "@trustsync/types";
import mongoose, { Document, Model } from "mongoose";

const { model, Schema } = mongoose;

// ReRegisterWebhook document interface
interface ReRegisterWebhookDocument extends BaseEntity {
  shop: string;
  status?: boolean;
}

// Mongoose document interface
interface IReRegisterWebhookDocument extends Omit<ReRegisterWebhookDocument, "_id">, Document {}

const ReRegisterWebhookSchema = new Schema<IReRegisterWebhookDocument>(
  {
    shop: { type: String, required: true },
    status: { type: Boolean, default: false },
  },
  { timestamps: { createdAt: "created_at" } }
);

ReRegisterWebhookSchema.index({ shop: 1 });

const ReRegisterWebhook: Model<IReRegisterWebhookDocument> =
  mongoose.models?.ReRegisterWebhook || model<IReRegisterWebhookDocument>("ReRegisterWebhook", ReRegisterWebhookSchema);

export default ReRegisterWebhook;
export type { IReRegisterWebhookDocument, ReRegisterWebhookDocument };
