import { ShopDocument } from "@trustsync/types";
import mongoose, { Document, Model } from "mongoose";

const { model, Schema } = mongoose;

// Mongoose document interface
interface IShopDocument extends Omit<ShopDocument, "_id">, Document {}

const ShopSchema = new Schema<IShopDocument>(
  {
    shop: { type: String, required: true },
    scopes: { type: String, default: null },
    isInstalled: { type: Boolean, default: false },
    installedOn: { type: Date, default: Date.now },
    uninstalledOn: { type: Date, default: null },
    webhooks: { type: Object, default: {} },
    verified: { type: <PERSON><PERSON><PERSON>, default: false },
    sentPastOrders: { type: Boolean, default: false },
    onboardingSteps: { type: Object, default: {} },
    // see https://shopify.dev/api/admin-rest/2022-04/resources/shop
    info: { type: Object, default: {} },
  },
  { timestamps: { createdAt: "created_at" } }
);

ShopSchema.index({ shop: 1 });
ShopSchema.index({ isInstalled: 1 });

const Shop: Model<IShopDocument> = mongoose.models?.Shop || model<IShopDocument>("Shop", ShopSchema);

export default Shop;
export type { IShopDocument };
