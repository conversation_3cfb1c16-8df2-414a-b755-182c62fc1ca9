import { ShopEmailsDocument } from "@trustsync/types";
import mongoose, { Document, Model } from "mongoose";

const { model, Schema } = mongoose;

// Mongoose document interface
interface IShopEmailsDocument extends Omit<ShopEmailsDocument, "_id">, Document {}

const ShopEmailSchema = new Schema<IShopEmailsDocument>(
  {
    shop: { type: String, required: true },
    email: { type: String, required: true },
    sentDate: { type: Date, default: null },
    sent: { type: Boolean, default: false },
    opened: { type: Boolean, default: false },
    clicked: { type: Boolean, default: false },
    sentEmails: { type: Number, default: 0 },
    pendingEmails: { type: Number, default: 0 },
  },
  { timestamps: { createdAt: "created_at" } }
);

ShopEmailSchema.index({ shop: 1 });

const ShopEmails: Model<IShopEmailsDocument> =
  mongoose.models?.ShopEmails || model<IShopEmailsDocument>("ShopEmails", ShopEmailSchema);

export default ShopEmails;
export type { IShopEmailsDocument };
