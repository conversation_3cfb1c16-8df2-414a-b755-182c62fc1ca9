import { EmailSettingsDocument } from "@trustsync/types";
import mongoose, { Document, Model } from "mongoose";

const { model, Schema } = mongoose;

// Review link interface

// Mongoose document interface
interface IEmailSettingsDocument extends Omit<EmailSettingsDocument, "_id">, Document {}

const EmailSettingsSchema = new Schema<IEmailSettingsDocument>(
  {
    shop: { type: String, required: true },
    active: { type: Boolean, default: false },
    language: { type: String, default: "english" },
    autoPublish: { type: String, default: "4" },
    reviewLinks: { type: [Object], required: false },
    designTemplate: { type: String, default: "regular" },
    lowestText: { type: String, required: false },
    highestText: { type: String, required: false },
    whenToSend: { type: Object, default: { days: "10", after: "fulfilled" } },
    sendOnRepeat: { type: String, default: "yes" },
    sender: { type: String, required: false },
    logo: { type: String, required: false },
    logoHeight: { type: Number, default: null },
    customersPick: { type: Boolean, default: false },
    secondEmail: { type: Boolean, default: false },
    secondWhenToSend: { type: Object, default: { days: "1", after: "email" } },
    onlyOnlineStoreOrders: { type: Boolean, default: false },
    customNegativeForm: { type: Boolean, default: false },
    customNegativeLink: { type: String, required: false },
    replyToEmails: { type: [String], required: false },
    blacklistedEmails: { type: [String], required: false },
    specificTimeActive: { type: Boolean, default: false },
    specificTime: { type: String, required: false },
    from: { type: String, required: false },
    subject: {
      type: String,
      required: false,
      default: "We need 10 seconds of your time!",
    },
    emailHTML: { type: String, required: false },
    bgColor: { type: String, default: null },
    borderColor: { type: String, default: null },
    valid: { type: Boolean, default: false },
    domain: { type: String, required: false },
    domainVerified: { type: Boolean, default: false },
  },
  { timestamps: { createdAt: "created_at" } }
);

EmailSettingsSchema.index({ shop: 1 });
EmailSettingsSchema.index({ active: 1 });

const EmailSettings: Model<IEmailSettingsDocument> =
  mongoose.models?.EmailSettings || model<IEmailSettingsDocument>("EmailSettings", EmailSettingsSchema);

export default EmailSettings;
export type { IEmailSettingsDocument };
