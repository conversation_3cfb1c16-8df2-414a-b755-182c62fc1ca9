import { MessageDocument, NegativeReviewDocument } from "@trustsync/types";
import mongoose, { Document, Model } from "mongoose";

const { model, Schema } = mongoose;

// Mongoose document interfaces
interface IMessageDocument extends MessageDocument, Document {}
interface INegativeReviewDocument extends Omit<NegativeReviewDocument, "_id">, Document {}

const MessageSchema = new Schema<IMessageDocument>(
  {
    from: { type: String, enum: ["shop", "customer"], required: true },
    messageId: { type: String, required: true },
    message: { type: String, required: false },
    attachments: { type: [String], required: false, default: undefined },
    status: { type: String, default: "unresolved", required: true },
    readAt: { type: Date, default: null },
    created_at: { type: Date, default: Date.now },
  },
  { _id: false }
);

const NegativeReviewSchema = new Schema<INegativeReviewDocument>(
  {
    shop: { type: String, required: true },
    orderId: { type: String, default: null },
    name: { type: String, default: "" },
    email: { type: String, default: "" },
    rating: { type: Number, default: 0 },
    message: { type: String, default: "" },
    attachment: { type: String, required: false },
    status: { type: String, default: "unresolved", required: true },
    reply: { type: String, required: false },
    conversation: { type: [MessageSchema], required: false, default: undefined },
  },
  { timestamps: { createdAt: "created_at" } }
);

NegativeReviewSchema.index({ shop: 1 });

const NegativeReview: Model<INegativeReviewDocument> =
  mongoose.models?.NegativeReview || model<INegativeReviewDocument>("NegativeReview", NegativeReviewSchema);

export default NegativeReview;
export type { IMessageDocument, INegativeReviewDocument };
