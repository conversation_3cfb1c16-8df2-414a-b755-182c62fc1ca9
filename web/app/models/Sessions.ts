import { BaseEntity } from "@trustsync/types";
import mongoose, { Document, Model } from "mongoose";

const { model, Schema } = mongoose;

// Sessions document interface
interface SessionsDocument extends BaseEntity {
  sessionId: string;
  payload: any;
  created_at?: Date;
  updated_at?: Date | null;
}

// Mongoose document interface
interface ISessionsDocument extends Omit<SessionsDocument, "_id" | "id">, Document {}

const SessionsSchema = new Schema<ISessionsDocument>({
  id: { type: String, required: true },
  payload: { type: Schema.Types.Mixed, required: true },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: null },
});

SessionsSchema.index({ id: 1 });

const Sessions: Model<ISessionsDocument> =
  mongoose.models.Sessions || model<ISessionsDocument>("Sessions", SessionsSchema);

export default Sessions;
export type { ISessionsDocument, SessionsDocument };
