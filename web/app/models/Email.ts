import { EmailDocument } from "@trustsync/types";
import mongoose, { Document, Model } from "mongoose";

const { model, Schema } = mongoose;

// Mongoose document interface
interface IEmailDocument extends Omit<EmailDocument, "_id">, Document {}

const EmailSchema = new Schema<IEmailDocument>(
  {
    shop: { type: String, required: true },
    email: { type: String, default: null },
    sendDate: { type: Date, default: null },
    lastTryDate: { type: Date, default: null },
    orderId: { type: String, default: null },
    orderNumber: { type: String, default: null },
    firstName: { type: String, default: null },
    sent: { type: Boolean, default: false },
    opened: { type: Boolean, default: false },
    clicked: { type: Boolean, default: false },
    sentSecond: { type: Boolean, default: false },
    reviewURL: { type: String, default: null },
    reviewPlatform: { type: String, default: null },
    messageId: { type: String, default: null },
  },
  { timestamps: { createdAt: "created_at" } }
);

EmailSchema.index({ shop: 1 });
EmailSchema.index({ sent: 1 });
EmailSchema.index({ sendDate: -1 });

const Email: Model<IEmailDocument> = mongoose.models?.Email || model<IEmailDocument>("Email", EmailSchema);

export default Email;
export type { IEmailDocument };
