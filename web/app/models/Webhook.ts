import { WebhookDocument, WebhookStatus } from "@trustsync/types";
import mongoose, { Document, Model } from "mongoose";

// Mongoose document interface
interface IWebhookDocument extends Omit<WebhookDocument, "_id">, Document {}

const WebhookSchema = new mongoose.Schema<IWebhookDocument>({
  topic: {
    type: String,
    required: true,
  },
  payload: {
    type: Object,
    required: true,
  },
  shop: {
    type: String,
    required: true,
  },
  status: {
    type: String,
    enum: {
      values: ["TO_TREAT", "ERROR", "TREATED"] as WebhookStatus[],
      message: "invalid activity type",
    },
    default: "TO_TREAT",
  },
  // source: {
  //   type: String,
  //   enum: {
  //     values: ["NEW", "OLD"],
  //     message: "invalid activity type",
  //   },
  //   required: true,
  // },
  treated_date: {
    type: Date,
    default: null,
    required: function (this: IWebhookDocument) {
      return this.status === "TREATED";
    },
  },
  last_processing_date: {
    type: Date,
    default: null,
  },
  // Note message
  message: {
    type: String,
    default: null,
  },
  error_message: {
    type: String,
    default: null,
  },
  next_retry_date: {
    type: Date,
    default: null,
  },
  retry_count: {
    type: Number,
    default: 0,
  },
  created_at: { type: Date, default: Date.now },
  updated_at: { type: Date, default: Date.now },
});

WebhookSchema.index({ shop: 1 });
WebhookSchema.index({ status: 1 });

const WebhookModel: Model<IWebhookDocument> =
  mongoose.models.webhook || mongoose.model<IWebhookDocument>("webhook", WebhookSchema);

export default WebhookModel;
export type { IWebhookDocument };
