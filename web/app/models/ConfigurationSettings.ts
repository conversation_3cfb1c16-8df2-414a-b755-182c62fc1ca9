import { BaseEntity } from "@trustsync/types";
import mongoose, { Document, Model } from "mongoose";

const { model, Schema } = mongoose;

// ConfigurationSettings document interface
interface ConfigurationSettingsDocument extends BaseEntity {
  shop: string;
  key: string;
  value?: Date | null;
}

// Mongoose document interface
interface IConfigurationSettingsDocument extends Omit<ConfigurationSettingsDocument, "_id">, Document {}

const ConfigurationSettingSchema = new Schema<IConfigurationSettingsDocument>(
  {
    shop: { type: String, required: true },
    key: { type: String, required: true },
    value: { type: Date, default: null },
  },
  { timestamps: { createdAt: "created_at" } }
);

ConfigurationSettingSchema.index({ shop: 1 });

const ConfigurationSettings: Model<IConfigurationSettingsDocument> =
  mongoose.models?.ConfigurationSettings ||
  model<IConfigurationSettingsDocument>("ConfigurationSettings", ConfigurationSettingSchema);

export default ConfigurationSettings;
export type { ConfigurationSettingsDocument, IConfigurationSettingsDocument };
