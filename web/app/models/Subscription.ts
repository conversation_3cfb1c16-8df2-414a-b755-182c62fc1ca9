import { SubscriptionDocument } from "@trustsync/types";
import mongoose, { Document, Model } from "mongoose";

const { model, Schema } = mongoose;

// Mongoose document interface
interface ISubscriptionDocument extends Omit<SubscriptionDocument, "_id">, Document {}

const SubscriptionSchema = new Schema<ISubscriptionDocument>(
  {
    shop: { type: String, required: true },
    plan: { type: String, default: "free" },
    chargeId: { type: Number, default: null },
    tmpChargeId: { type: Number, default: null },
    emails: { type: Number, default: 0 },
    limitEmails: { type: Number, default: 50 },
    dateBilled: { type: Date, default: null },
    resetDate: { type: Date, default: null },
    status: { type: String, default: "active" },
    valid: { type: Boolean, default: true },
    error: { type: String, default: null },
  },
  { timestamps: { createdAt: "created_at" } }
);

SubscriptionSchema.index({ shop: 1 });
SubscriptionSchema.index({ resetDate: -1 });

const Subscription: Model<ISubscriptionDocument> =
  mongoose.models?.Subscription || model<ISubscriptionDocument>("Subscription", SubscriptionSchema);

export default Subscription;
export type { ISubscriptionDocument };
