APP_NAME=customer-review-app-1
APP_VERSION=2.2.0
VITE_APP_VERSION=2.2.0
APP_URL=https://app.trustsync.io
APP_REVIEW_URL=https://review.trustsync.io

# Set environment for test subdcription and mailgun
APP_ENVIRONMENT=production

# Shopify App Key
SHOPIFY_API_KEY=339be005e835ad8abca2f5df8e624654
SHOPIFY_API_SECRET=d72ff2f704cf063d155491607c35186c
SCOPES=read_orders,read_all_orders,read_customers
SHOPIFY_APP_URL=https://app.trustsync.io
HOST=https://app.trustsync.io
SHOP=easytrustdev.myshopify.com

# MongoDB Database URL
MONGODB_URI=mongodb+srv://prod:<EMAIL>/prod?tls=true&authSource=admin&replicaSet=trustcomet-mongodb

# S3 Bucket Credential
STORAGE_BUCKET=trustcomet
STORAGE_REGION=fra1
STORAGE_FOLDER=production
STORAGE_ACCESS_KEY=DO00X2UEG6UG2WR4YLBT
STORAGE_SECRET=o3wicjNYX/57f2algsdbLOEbB/CgPNmdP+Qw8NqNC9k
STORAGE_ENDPOINT=https://fra1.digitaloceanspaces.com
STORAGE_CDN_BASE_URL=https://trustcomet.fra1.cdn.digitaloceanspaces.com/production

# Mailgun Credential
MAILGUN_API_KEY=**************************************************
MAILGUN_SANDBOX_DOMAIN="sandbox0e7effeb8d70454e823d7d2e0ba18917.mailgun.org"
MAILGUN_DEV_TO_EMAIL="<EMAIL>"