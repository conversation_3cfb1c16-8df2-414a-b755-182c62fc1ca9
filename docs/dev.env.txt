APP_NAME=trustsync-dev
APP_VERSION=2.0.0
VITE_APP_VERSION=2.0.0
APP_URL=https://app.trustsync.dev
APP_REVIEW_URL=https://review.trustsync.dev

# Set environment for test subdcription and mailgun
APP_ENVIRONMENT=production

# Shopify App Crendetials
SHOPIFY_API_KEY=e2ea6c1330444ae4954b76fbdd9eef7d
SHOPIFY_API_SECRET=7c989893425337929a60001fc87fd845
SCOPES=read_orders,read_customers
SHOPIFY_APP_URL=https://app.trustsync.dev
SHOP=easytrustdev.myshopify.com
HOST=https://app.trustsync.dev

# MongoDB Database URL
MONGODB_URI=mongodb+srv://dev:<EMAIL>/dev?tls=true&authSource=admin&replicaSet=trustcomet-mongodb

# S3 Bucket Credential
STORAGE_BUCKET=trustcomet
STORAGE_REGION=fra1
STORAGE_FOLDER=development
STORAGE_ACCESS_KEY=DO00X2UEG6UG2WR4YLBT
STORAGE_SECRET=o3wicjNYX/57f2algsdbLOEbB/CgPNmdP+Qw8NqNC9k
STORAGE_ENDPOINT=https://fra1.digitaloceanspaces.com
STORAGE_CDN_BASE_URL=https://trustcomet.fra1.cdn.digitaloceanspaces.com/development

# Mailgun Credential
MAILGUN_API_KEY=**************************************************
MAILGUN_SANDBOX_DOMAIN=trustsync.io
MAILGUN_DEV_TO_EMAIL=<EMAIL>