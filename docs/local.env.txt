APP_NAME=trustsync-react
APP_VERSION=2.0.0
VITE_APP_VERSION=2.0.0
APP_URL=https://app.trustsync.io
APP_REVIEW_URL=https://app.trustsync.io

# Set environment for test subdcription and mailgun
APP_ENVIRONMENT=development

# Shopify App Crendetials
SHOPIFY_API_KEY=87875fce7dc2e9f0753d6eb4b22c1968
SHOPIFY_API_SECRET=7ae53b8d822b80ff7509cbe2c6c56242
SCOPES=read_orders,read_customers
SHOPIFY_APP_URL=https://app.trustsync.io
HOST=https://app.trustsync.io
SHOP=easytrustdev.myshopify.com


# MongoDB Database URL
MONGODB_URI=mongodb://localhost:27017/trustsync-remix

# S3 Bucket Credential
STORAGE_BUCKET=trustcomet
STORAGE_REGION=fra1
STORAGE_FOLDER=staging
STORAGE_ACCESS_KEY=DO00X2UEG6UG2WR4YLBT
STORAGE_SECRET="o3wicjNYX/57f2algsdbLOEbB/CgPNmdP+Qw8NqNC9k"
STORAGE_ENDPOINT=https://fra1.digitaloceanspaces.com
STORAGE_CDN_BASE_URL=https://trustcomet.fra1.cdn.digitaloceanspaces.com/staging

# Mailgun Credential
# MAILGUN_API_KEY=**************************************************
# MAILGUN_SANDBOX_DOMAIN=sandbox43a75255de734cebb3a028725a7a7b29.mailgun.org
# MAILGUN_DEV_TO_EMAIL=<EMAIL>

MAILGUN_API_KEY=**************************************************
MAILGUN_SANDBOX_DOMAIN=trustsync.dev
MAILGUN_DEV_TO_EMAIL=<EMAIL>


#Redis Credential
REDIS_HOST=127.0.0.1:6379
REDIS_DATABASE=1
REDIS_USERNAME=trustsync
REDIS_PASSWORD=Trust2025xR7a9
