name: "Deploy to Production"
on:
  push:
    tags:
      - v* # Trigger when a tag with 'v' prefix is pushed

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Extract Tag Information
        id: tag_info
        run: |
          TAG_NAME=${GITHUB_REF##*/}
          APP_VERSION=${TAG_NAME#v}
          LAST_UPDATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
          echo "TAG_NAME=$TAG_NAME" >> $GITHUB_ENV
          echo "APP_VERSION=$APP_VERSION" >> $GITHUB_ENV
          echo "LAST_UPDATE=$LAST_UPDATE" >> $GITHUB_ENV

      - name: SSH Remote Commands
        uses: appleboy/ssh-action@v1.2.2
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USER }}
          key: ${{ secrets.KEY }}
          script: |
            set -e
            # Ensure the environment variables are set
            echo "APP_VERSION=${{ env.APP_VERSION }}"

            export NVM_DIR=~/.nvm
            source ~/.nvm/nvm.sh
            cd /var/www/html/trustsync-app-react/

            git checkout main 
            git pull origin main

            # Ensure APP_VERSION exists or add it, then update it
            grep -q '^APP_VERSION=' web/.env && sed -i "s/^APP_VERSION=.*/APP_VERSION=${{ env.APP_VERSION }}/" web/.env || echo "APP_VERSION=${{ env.APP_VERSION }}" >> web/.env
            grep -q '^VITE_APP_VERSION=' web/.env && sed -i "s/^VITE_APP_VERSION=.*/VITE_APP_VERSION=${{ env.APP_VERSION }}/" web/.env || echo "VITE_APP_VERSION=${{ env.APP_VERSION }}" >> web/.env

            pnpm install
            pnpm run build -c shopify.app.trustsync.toml
            pnpm run process:prod:run
