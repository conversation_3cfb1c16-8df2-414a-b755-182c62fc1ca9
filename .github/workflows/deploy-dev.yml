name: "Deploy to Staging"
on:
  push:
    branches:
      - staging

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: SSH Remote Commands
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.DEV_HOST }}
          username: ${{ secrets.DEV_USER }}
          key: ${{ secrets.DEV_KEY }}
          script_stop: true
          script: |
            cd /var/www/html/trustsync-app-react/
            git stash
            git pull origin staging
            pnpm install
            pnpm run build -c shopify.app.trustsync-dev.toml
            pnpm run process:prod:run
