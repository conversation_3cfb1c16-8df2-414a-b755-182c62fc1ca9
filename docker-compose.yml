version: "3.7"
services:
  redis:
    image: redis
    container_name: trustSync_redis
    volumes:
      - ./data/redis:/data
    ports:
      - "${REDIS_PORT:-6380}:6379"
    expose:
      - ${REDIS_PORT}
    env_file:
      - ./web/.env
    command:
      - /bin/sh
      - -c
      - redis-server --requirepass ${REDIS_DEFAULT_PASSWORD} --user ${REDIS_USERNAME} \>${REDIS_PASSWORD} on +@all ~* allchannels --user khairul \>${REDIS_PASSWORD} on +@all ~* allchannels
