{"name": "trustsync-react", "version": "1.0.0", "main": "web/index.js", "license": "UNLICENSED", "scripts": {"shopify": "shopify", "build": "shopify app build", "build:types": "pnpm --filter @trustsync/types run build", "dev": "shopify app dev", "info": "shopify app info", "generate": "shopify app generate", "deploy": "shopify app deploy", "serve": "cd web && pnpm run serve", "serve:ts": "cd web && pnpm run serve:ts", "type-check": "pnpm run -r type-check", "type-check:backend": "cd web && pnpm run type-check", "type-check:frontend": "cd web/frontend && pnpm run type-check", "web-register": "pnpm tsx web/app/commands/register.webhook.ts", "web-list": "pnpm tsx web/app/commands/list.webhooks.rest.ts", "process:prod:run": "cd web && pnpm run process:prod:run"}, "dependencies": {"@shopify/cli": "3.68.0"}, "author": "rostom", "private": true, "engines": {"node": ">=20.18.0", "pnpm": ">=10.8.0"}, "packageManager": "pnpm@10.8.0+sha512.0e82714d1b5b43c74610193cb20734897c1d00de89d0e18420aebc5977fa13d780a9cb05734624e81ebd81cc876cd464794850641c48b9544326b5622ca29971", "engineStrict": true}