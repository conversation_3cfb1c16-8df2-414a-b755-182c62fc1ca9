import { BaseEntity } from ".";

// Message interface
export interface MessageDocument {
  from: "shop" | "customer";
  messageId: string;
  message?: string;
  attachments?: string[];
  status: string;
  readAt: Date | null;
  created_at: Date;
}

export type NegativeReviewStaus = "unresolved" | "replied" | "resolved";

// NegativeReview document interface
export interface NegativeReviewDocument extends BaseEntity {
  shop: string;
  orderId?: string | null;
  name?: string;
  email?: string;
  rating?: number;
  message?: string;
  attachment?: string;
  status: NegativeReviewStaus;
  reply?: string;
  conversation?: MessageDocument[];
}
