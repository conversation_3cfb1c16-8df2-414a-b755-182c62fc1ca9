/**
 * @fileoverview Shop-related type definitions
 */

import { BaseEntity } from "./index.js";

/**
 * Shopify shop information from API
 */
export interface ShopInfo {
  /** Shop ID */
  id?: number;
  /** Shop name */
  name?: string;
  /** Shop email */
  email?: string;
  /** Shop URL */
  url?: string;
  /** MyShopify domain */
  myshopifyDomain?: string;
  /** Timezone abbreviation */
  timezoneAbbreviation?: string;
  /** Currency code */
  currencyCode?: string;
  /** Shop owner name */
  shop_owner?: string;
  /** Currency formats */
  currencyFormats?: {
    moneyWithCurrencyFormat?: string;
  };
  /** Plan information */
  plan?: {
    displayName?: string;
    partnerDevelopment?: boolean;
    shopifyPlus?: boolean;
  };
  /** Billing address */
  billingAddress?: {
    address1?: string;
    address2?: string;
    city?: string;
    company?: string;
    country?: string;
    countryCodeV2?: string;
    id?: string;
    latitude?: number;
    longitude?: number;
    province?: string;
    provinceCode?: string;
    zip?: string;
    formatted?: string;
    formattedArea?: string;
    phone?: string;
  };
}

/**
 * Shop onboarding steps
 */
export interface OnboardingSteps {
  [key: string]: boolean;
}

/**
 * Shop webhooks configuration
 */
export interface ShopWebhooks {
  [key: string]: any;
}

/**
 * Shop document interface (MongoDB model)
 */
export interface ShopDocument extends BaseEntity {
  /** Shop domain (required) */
  shop: string;
  /** OAuth scopes granted to the app */
  scopes?: string | null;
  /** Whether the app is installed */
  isInstalled?: boolean;
  /** When the app was installed */
  installedOn?: Date;
  /** When the app was uninstalled */
  uninstalledOn?: Date | null;
  /** Webhook configuration */
  webhooks?: ShopWebhooks;
  /** Whether the shop is verified */
  verified?: boolean;
  /** Whether past orders have been processed */
  sentPastOrders?: boolean;
  /** Onboarding progress */
  onboardingSteps?: OnboardingSteps;
  /** Shop information from Shopify API */
  info: ShopInfo;
}

/**
 * Shopify GraphQL Order Customer interface
 */
export interface ShopifyOrderCustomer {
  /** Customer ID */
  id: string;
  /** Customer display name */
  displayName?: string;
  /** Customer first name */
  firstName?: string;
  /** Customer last name */
  lastName?: string;
}

/**
 * Shopify GraphQL Order interface
 */
export interface ShopifyOrder {
  /** Order ID */
  id: string;
  /** Order name/number */
  name?: string;
  /** Customer email */
  email?: string;
  /** Customer information */
  customer?: ShopifyOrderCustomer;
  /** Order creation date */
  createdAt: string;
}

/**
 * Shopify GraphQL Order Edge interface
 */
export interface ShopifyOrderEdge {
  /** Order node */
  node: ShopifyOrder;
}

/**
 * Shopify GraphQL PageInfo interface
 */
export interface ShopifyPageInfo {
  /** Has previous page */
  hasPreviousPage: boolean;
  /** Has next page */
  hasNextPage: boolean;
  /** Start cursor */
  startCursor?: string;
  /** End cursor */
  endCursor?: string;
}

/**
 * Shopify GraphQL Orders Response interface
 */
export interface ShopifyOrdersResponse {
  /** Order edges */
  edges: ShopifyOrderEdge[];
  /** Page information */
  pageInfo: ShopifyPageInfo;
}

/**
 * Past order option interface
 */
export interface PastOrderOption {
  /** Option label */
  label: string;
  /** Option value */
  value: string;
}
