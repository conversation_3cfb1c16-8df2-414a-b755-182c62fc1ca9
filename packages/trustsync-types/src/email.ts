/**
 * @fileoverview Email-related type definitions
 */

import { BaseEntity } from "./index.js";

export interface EmailDocument extends BaseEntity {
  shop: string;
  email?: string | null;
  sendDate?: Date | null;
  lastTryDate?: Date | null;
  orderId?: string | null;
  orderNumber?: string | null;
  firstName?: string | null;
  sent?: boolean;
  opened?: boolean;
  clicked?: boolean;
  sentSecond?: boolean;
  reviewURL?: string | null;
  reviewPlatform?: string | null;
  messageId?: string | null;
}

export interface ReviewLink {
  id: string;
  platform: string;
  url: string;
  percentage: string;
}

export interface WhenToSend {
  days: string;
  after: string;
  tag: string;
}

export interface SecondWhenToSend {
  days: string;
  after: string;
}

// EmailSettings document interface
export interface EmailSettingsDocument extends BaseEntity {
  shop: string;
  active?: boolean;
  language?: string;
  autoPublish?: string;
  reviewLinks?: ReviewLink[];
  designTemplate?: string;
  lowestText?: string;
  highestText?: string;
  whenToSend?: WhenToSend;
  sendOnRepeat?: string;
  sender?: string;
  logo?: string;
  logoHeight?: number | null;
  customersPick?: boolean;
  secondEmail?: boolean;
  secondWhenToSend?: SecondWhenToSend;
  onlyOnlineStoreOrders?: boolean;
  customNegativeForm?: boolean;
  customNegativeLink?: string;
  replyToEmails?: string[];
  blacklistedEmails?: string[];
  specificTimeActive?: boolean;
  specificTime?: string;
  from?: string;
  subject?: string;
  emailHTML?: string;
  bgColor?: string | null;
  borderColor?: string | null;
  valid?: boolean;
  domain?: string;
  domainVerified?: boolean;
}
