/**
 * @fileoverview API-related type definitions
 */

/**
 * API response interface
 */
export interface ApiResponse<T> {
  /** Response data */
  data: T;
  /** Response message */
  message: string;
  /** HTTP status code */
  status: number;
  /** Whether request was successful */
  success: boolean;
  /** Validation errors */
  errors?: any;
}

/**
 * API paginated response interface
 */
export interface ApiPaginatedResponse<T> {
  /** Array of items */
  data: T[];
  /** Pagination info */
  pagination: {
    /** Total number of items */
    // total: number;
    /** Current page number */
    page?: number;
    /** Items per page */
    limit?: number;
    /** Total number of pages */
    totalPages?: number;
    /** Whether there's a next page */
    hasNext?: boolean;
    /** Whether there's a previous page */
    hasPrev?: boolean;

    pageSize?: number;
    totalItems?: number;
  };
  /** Response message */
  message?: string;
  /** Whether request was successful */
  success?: boolean;
}

/**
 * Shopify webhook headers interface
 */
export interface ShopifyWebhookHeaders {
  /** Webhook topic */
  "x-shopify-topic": string;
  /** HMAC signature */
  "x-shopify-hmac-sha256": string;
  /** Shop domain */
  "x-shopify-shop-domain": string;
  /** API version */
  "x-shopify-api-version": string;
  /** Webhook ID */
  "x-shopify-webhook-id": string;
}
