/**
 * @fileoverview Subscription-related type definitions
 */

import { BaseEntity } from "./index.js";

/**
 * Subscription plan type
 */
export type SubscriptionPlanType = "free" | "basic" | "pro" | "elite" | "enterprise";

/**
 * Subscription status type
 */
export type SubscriptionStatusType = "active" | "inactive";

/**
 * Billing cycle type
 */
export type BillingCycleType = "monthly" | "yearly";

/**
 * Subscription data interface
 */
export interface SubscriptionDocument extends BaseEntity {
  shop: string;
  plan?: string;
  chargeId?: number | null;
  tmpChargeId?: number | null;
  emails?: number;
  limitEmails?: number;
  dateBilled?: Date | null;
  resetDate?: Date | null;
  status?: string;
  valid?: boolean;
  error?: string | null;
}
export interface PlanOption {
  id: number;
  label: string;
  enable: boolean;
}

export interface Plan {
  name: string;
  slug: string;
  icon: string;
  price: number;
  discountPrice: number;
  emails: number;
  options: PlanOption[];
}
