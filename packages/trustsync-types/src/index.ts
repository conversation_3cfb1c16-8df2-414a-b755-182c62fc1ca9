import { ObjectId } from "mongoose";
/**
 * @fileoverview Main entry point for TrustSync shared type definitions
 *
 * This package provides shared type definitions that can be used across
 * both frontend and backend components of the TrustSync application.
 *
 * All types are defined using native TypeScript syntax.
 */

// Re-export all type definitions
// export * from "./analytics.js";
export * from "./";
export * from "./api";
export * from "./email";
export * from "./negative-reviews";
export * from "./shop";
export * from "./shop-emails";
export * from "./subscription";
export * from "./webhook";

/**
 * Base entity interface for MongoDB documents
 */
export interface BaseEntity {
  /** MongoDB ObjectId as string */
  _id: ObjectId;
  /** Version */
  _v?: number;
  /** Creation timestamp */
  createdAt: Date;
  /** Last update timestamp */
  updatedAt?: Date;
}
