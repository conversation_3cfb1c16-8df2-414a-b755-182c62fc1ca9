import { BaseEntity } from ".";

/**
 * Webhook status enum
 */
export type WebhookStatus = "TO_TREAT" | "ERROR" | "TREATED";

/**
 * Webhook document interface (MongoDB model)
 */
export interface WebhookDocument extends BaseEntity {
  /** Webhook topic */
  topic: string;
  /** Webhook payload data */
  payload: Record<string, any>;
  /** Shop domain */
  shop: string;
  /** Processing status */
  status?: WebhookStatus;
  /** Date when webhook was treated */
  treated_date?: Date | null;
  /** Last processing attempt date */
  last_processing_date?: Date | null;
  /** Processing message */
  message?: string | null;
  /** Error message if processing failed */
  error_message?: string | null;
  /** Next retry date */
  next_retry_date?: Date | null;
  /** Number of retry attempts */
  retry_count?: number;
  /** Creation timestamp */
  created_at?: Date;
  /** Update timestamp */
  updated_at?: Date;
}
