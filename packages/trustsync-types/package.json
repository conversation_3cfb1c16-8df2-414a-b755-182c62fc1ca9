{"name": "@trustsync/types", "version": "1.0.0", "description": "Shared type definitions for TrustSync application", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./shop": {"types": "./dist/shop.d.ts", "import": "./dist/shop.js"}, "./email": {"types": "./dist/email.d.ts", "import": "./dist/email.js"}, "./subscription": {"types": "./dist/subscription.d.ts", "import": "./dist/subscription.js"}, "./analytics": {"types": "./dist/analytics.d.ts", "import": "./dist/analytics.js"}, "./api": {"types": "./dist/api.d.ts", "import": "./dist/api.js"}}, "files": ["dist/**/*"], "scripts": {"build": "rm -rf ./dist/* && tsc", "dev": "tsc --watch", "type-check": "tsc --noEmit", "prepare": "pnpm run build", "prepublishOnly": "pnpm run clean && pnpm run build"}, "keywords": ["types", "typescript", "trustsync"], "author": "TrustSync Team", "license": "MIT", "devDependencies": {"typescript": "^5.3.0"}}