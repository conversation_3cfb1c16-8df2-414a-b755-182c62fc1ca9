# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "339be005e835ad8abca2f5df8e624654"
application_url = "https://app.trustsync.io/"
embedded = true
name = "TrustSync"
handle = "customer-review-app-1"

[webhooks]
api_version = "2024-10"

  [[webhooks.subscriptions]]
  uri = "/webhooks/customer-data"
  compliance_topics = [ "customers/redact" ]

  [[webhooks.subscriptions]]
  uri = "/webhooks/customer-data-request"
  compliance_topics = [ "customers/data_request" ]

  [[webhooks.subscriptions]]
  uri = "/webhooks/shop-data"
  compliance_topics = [ "shop/redact" ]

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_all_orders,read_customers,read_orders"
use_legacy_install_flow = false

[auth]
redirect_urls = [
  "https://app.trustsync.io/auth/callback",
  "https://app.trustsync.io/auth/shopify/callback",
  "https://app.trustsync.io/api/auth/callback",
  "https://app.trustsync.io/auth/tokens"
]

[pos]
embedded = false

[build]
include_config_on_deploy = true
